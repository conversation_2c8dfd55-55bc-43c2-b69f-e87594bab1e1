# ManufacturerFacility

**Source File:** [ManufacturerFacility.cs](../ManufacturerFacility.cs)

## Overview
The `ManufacturerFacility` entity represents the relationship between manufacturers and their facilities. This entity captures the specific locations where manufacturers operate, including production facilities, distribution centers, and service locations.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the manufacturer facility |
| `ManufacturerId` | `Guid` | Yes | Foreign key to the manufacturer |
| `FacilityId` | `Guid` | Yes | Foreign key to the facility |
| `IsPrimaryFacility` | `bool` | Yes | Indicates if this is the manufacturer's primary facility |
| `FacilityType` | `string?` | No | Type of facility (max 100 characters) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the relationship was created |
| `CreatedBy` | `Guid?` | No | User who created this relationship |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this relationship |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Manufacturer` | `Manufacturer?` | The manufacturer operating at this facility |
| `Facility` | `Facility?` | The facility where the manufacturer operates |

## Relationships

### Many-to-One Relationships
- **ManufacturerFacility → Manufacturer**: Each facility relationship belongs to one manufacturer
- **ManufacturerFacility → Facility**: Each relationship is for one facility

## Facility Types

### Production Facilities
Manufacturing locations where products are produced:

#### Primary Manufacturing
- **Description**: Main production facilities
- **Characteristics**: Full production capabilities, quality control
- **Products**: Complete product lines, high-volume production
- **Capabilities**: Research and development, engineering support

#### Secondary Manufacturing
- **Description**: Specialized or regional production facilities
- **Characteristics**: Focused production, specific product lines
- **Products**: Specialized products, regional variants
- **Capabilities**: Limited product range, regional customization

#### Contract Manufacturing
- **Description**: Third-party manufacturing facilities
- **Characteristics**: Outsourced production, quality agreements
- **Products**: Specific product lines under contract
- **Capabilities**: Cost-effective production, capacity flexibility

### Distribution Facilities
Locations focused on storage and distribution:

#### Regional Distribution Centers
- **Description**: Regional warehouses and distribution
- **Characteristics**: Large inventory, regional coverage
- **Products**: Full product range for region
- **Capabilities**: Inventory management, logistics coordination

#### Local Warehouses
- **Description**: Local storage and quick delivery
- **Characteristics**: Limited inventory, fast delivery
- **Products**: High-turnover items, emergency stock
- **Capabilities**: Same-day delivery, local service

### Service Facilities
Locations providing customer service and support:

#### Service Centers
- **Description**: Customer service and technical support
- **Characteristics**: Technical expertise, customer interface
- **Services**: Equipment service, technical support, training
- **Capabilities**: Field service, repair, maintenance

#### Training Centers
- **Description**: Customer and dealer training facilities
- **Characteristics**: Training equipment, educational resources
- **Services**: Product training, certification programs
- **Capabilities**: Hands-on training, technical education

## Business Rules

1. **Manufacturer Association**: Every facility relationship must belong to a manufacturer
2. **Facility Association**: Every relationship must reference a valid facility
3. **Primary Facility**: Each manufacturer should have at least one primary facility
4. **Facility Type Validation**: Facility types should be from a controlled vocabulary
5. **Geographic Consistency**: Facility locations should align with manufacturer operations

## Usage Examples

### Creating Manufacturer Facility Relationships
```csharp
var manufacturerFacilities = new List<ManufacturerFacility>
{
    // Primary manufacturing facility
    new ManufacturerFacility
    {
        Id = Guid.NewGuid(),
        ManufacturerId = millerElectricId,
        FacilityId = appleton_wi_facilityId,
        IsPrimaryFacility = true,
        FacilityType = "Primary Manufacturing",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = facility_manager_id
    },
    
    // Regional distribution center
    new ManufacturerFacility
    {
        ManufacturerId = millerElectricId,
        FacilityId = dallas_tx_facilityId,
        IsPrimaryFacility = false,
        FacilityType = "Regional Distribution",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = facility_manager_id
    },
    
    // Service center
    new ManufacturerFacility
    {
        ManufacturerId = millerElectricId,
        FacilityId = atlanta_ga_facilityId,
        IsPrimaryFacility = false,
        FacilityType = "Service Center",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = facility_manager_id
    }
};
```

### Creating Multi-Manufacturer Facility
```csharp
var sharedFacility = new List<ManufacturerFacility>
{
    // Manufacturer A at shared facility
    new ManufacturerFacility
    {
        ManufacturerId = manufacturerAId,
        FacilityId = sharedFacilityId,
        IsPrimaryFacility = false,
        FacilityType = "Contract Manufacturing",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = operations_manager_id
    },
    
    // Manufacturer B at same facility
    new ManufacturerFacility
    {
        ManufacturerId = manufacturerBId,
        FacilityId = sharedFacilityId,
        IsPrimaryFacility = false,
        FacilityType = "Contract Manufacturing",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = operations_manager_id
    }
};
```

## Facility Operations

### Manufacturing Operations
- **Production Planning**: Coordinate production across facilities
- **Quality Control**: Maintain quality standards across locations
- **Capacity Management**: Optimize capacity utilization
- **Supply Chain**: Coordinate supply chain across facilities

### Distribution Operations
- **Inventory Management**: Manage inventory across distribution network
- **Logistics Coordination**: Coordinate shipping and receiving
- **Order Fulfillment**: Optimize order fulfillment processes
- **Customer Service**: Provide customer service from multiple locations

### Service Operations
- **Technical Support**: Provide technical support from service centers
- **Field Service**: Coordinate field service activities
- **Training Programs**: Deliver training programs at multiple locations
- **Warranty Service**: Provide warranty service and support

## Geographic Considerations

### Regional Coverage
- **Market Coverage**: Ensure adequate market coverage
- **Service Levels**: Maintain consistent service levels
- **Local Presence**: Establish local presence in key markets
- **Cultural Adaptation**: Adapt to local cultures and practices

### Logistics Optimization
- **Transportation Costs**: Minimize transportation costs
- **Delivery Times**: Optimize delivery times to customers
- **Inventory Positioning**: Position inventory for optimal service
- **Network Efficiency**: Optimize facility network efficiency

## Compliance and Regulations

### Manufacturing Compliance
- **Quality Standards**: Comply with quality standards (ISO 9001, etc.)
- **Safety Regulations**: Meet safety regulations (OSHA, etc.)
- **Environmental Compliance**: Comply with environmental regulations
- **Product Certifications**: Maintain product certifications

### International Operations
- **Import/Export Regulations**: Comply with trade regulations
- **Local Regulations**: Meet local regulatory requirements
- **Tax Compliance**: Comply with local tax requirements
- **Currency Management**: Manage multi-currency operations

## Performance Metrics

### Operational Metrics
- **Production Efficiency**: Measure production efficiency by facility
- **Quality Metrics**: Track quality performance across facilities
- **Cost Metrics**: Monitor costs by facility and operation type
- **Service Metrics**: Measure service performance and customer satisfaction

### Financial Metrics
- **Revenue by Facility**: Track revenue generation by facility
- **Profitability**: Measure facility profitability
- **Investment Returns**: Calculate returns on facility investments
- **Cost Allocation**: Allocate costs across facilities

## Related Entities

- [Manufacturer](Manufacturer.md) - The manufacturers operating facilities
- [Facility](Facility.md) - The facilities where manufacturers operate
- [Equipment](Equipment.md) - Equipment manufactured at these facilities
- [Electrode](Electrode.md) - Electrodes manufactured at these facilities

## Database Considerations

- The `FacilityType` property has a maximum length of 100 characters
- Index on `ManufacturerId` for manufacturer-based queries
- Index on `FacilityId` for facility-based queries
- Index on `IsPrimaryFacility` for primary facility queries
- Foreign key constraints should be properly configured
- Consider unique constraints to prevent duplicate manufacturer-facility combinations
- Implement business rules to ensure at least one primary facility per manufacturer

## Integration with Business Systems

### ERP Integration
- **Manufacturing Planning**: Integrate with manufacturing planning systems
- **Inventory Management**: Connect with inventory management systems
- **Financial Systems**: Integrate with financial and accounting systems
- **Supply Chain**: Connect with supply chain management systems

### CRM Integration
- **Customer Service**: Integrate with customer service systems
- **Sales Support**: Connect with sales and marketing systems
- **Service Management**: Integrate with service management systems
- **Partner Management**: Connect with partner and dealer systems

## Future Enhancements

### Advanced Facility Management
- **Capacity Planning**: Advanced capacity planning across facilities
- **Predictive Maintenance**: Predictive maintenance for facility equipment
- **Energy Management**: Energy management and optimization
- **Sustainability Tracking**: Environmental impact tracking

### Digital Integration
- **IoT Integration**: Internet of Things integration for facility monitoring
- **Real-Time Visibility**: Real-time visibility into facility operations
- **Mobile Access**: Mobile access to facility information
- **Analytics Platform**: Advanced analytics for facility optimization
