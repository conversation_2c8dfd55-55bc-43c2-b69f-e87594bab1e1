# CatElectrodeSpecialRule

**Source File:** [CatElectrodeSpecialRule.cs](../CatElectrodeSpecialRule.cs)

## Overview
The `CatElectrodeSpecialRule` entity represents special rules and requirements for electrode classifications in specific welding applications. This entity captures unique or non-standard requirements that may apply to certain electrode types or welding processes.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the special rule |
| `WeldingArcDesignationSpec` | `string` | Yes | Welding arc designation specification (max 255 characters) |
| `WeldingProcessId` | `Guid` | Yes | Foreign key to the welding process |
| `SpecificationId` | `Guid?` | No | Optional foreign key to related specification |
| `MinimumYieldStrength` | `decimal?` | No | Minimum yield strength requirement (6,3 precision) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the rule was created |
| `CreatedBy` | `Guid?` | No | User who created the rule record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the rule record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `WeldingProcess` | `WeldingProcess` | The welding process this rule applies to |
| `Specification` | `Specification?` | Optional related specification |

## Relationships

### Many-to-One Relationships
- **CatElectrodeSpecialRule → WeldingProcess**: Each rule applies to one welding process
- **CatElectrodeSpecialRule → Specification**: Each rule may reference one specification

## Business Rules

1. **Process Association**: Every special rule must be associated with a welding process
2. **Specification Validation**: If specified, the specification must be valid and active
3. **Yield Strength Validation**: Yield strength values must be positive when specified
4. **Designation Format**: Welding arc designation must follow standard format
5. **Rule Uniqueness**: Rules should be unique for process/specification combinations

## Special Rule Types

### Process-Specific Rules
Special rules that apply to specific welding processes:

#### SMAW (Stick Welding) Rules
- **Position Restrictions**: Specific position limitations
- **Current Requirements**: Special current type requirements
- **Preheat Requirements**: Mandatory preheating conditions
- **Interpass Temperature**: Maximum interpass temperature limits

#### GTAW (TIG Welding) Rules
- **Gas Requirements**: Specific shielding gas requirements
- **Tungsten Type**: Required tungsten electrode types
- **Arc Length**: Critical arc length specifications
- **Travel Speed**: Minimum/maximum travel speed requirements

#### GMAW (MIG Welding) Rules
- **Wire Feed Speed**: Specific wire feed speed ranges
- **Voltage Requirements**: Critical voltage parameters
- **Gas Flow Rates**: Required gas flow specifications
- **Contact Tip Distance**: Stick-out requirements

### Application-Specific Rules
Rules for specific applications or industries:

#### Aerospace Applications
- **Cleanliness Requirements**: Ultra-clean welding environment
- **Documentation**: Enhanced documentation requirements
- **Testing**: Additional testing and inspection requirements
- **Traceability**: Complete material traceability

#### Nuclear Applications
- **Qualification**: Special welder qualification requirements
- **Materials**: Restricted material specifications
- **Procedures**: Enhanced procedure requirements
- **Quality Assurance**: Stringent QA requirements

#### Pipeline Applications
- **Weather Conditions**: Environmental condition limits
- **Root Pass**: Special root pass requirements
- **Tie-in Procedures**: Specific tie-in welding procedures
- **Testing**: Mandatory testing requirements

## Usage Examples

### Creating a SMAW Special Rule
```csharp
var smawRule = new CatElectrodeSpecialRule
{
    Id = Guid.NewGuid(),
    WeldingArcDesignationSpec = "E7018-H4R",
    WeldingProcessId = smawProcessId,
    SpecificationId = awsA51SpecId,
    MinimumYieldStrength = 58000m,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = welding_engineer_id
};
```

### Creating a Pipeline Special Rule
```csharp
var pipelineRule = new CatElectrodeSpecialRule
{
    WeldingArcDesignationSpec = "API-5L-X65-Root",
    WeldingProcessId = smawProcessId,
    SpecificationId = api1104SpecId,
    MinimumYieldStrength = 65000m,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = pipeline_engineer_id
};
```

### Creating a Nuclear Application Rule
```csharp
var nuclearRule = new CatElectrodeSpecialRule
{
    WeldingArcDesignationSpec = "ASME-III-NB-Special",
    WeldingProcessId = gtawProcessId,
    SpecificationId = asmeSection3SpecId,
    MinimumYieldStrength = 70000m,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = nuclear_engineer_id
};
```

## Rule Categories

### Strength Requirements
Rules that specify enhanced strength requirements:
- **High-Strength Applications**: Minimum yield strength above standard
- **Fatigue Resistance**: Enhanced fatigue performance requirements
- **Impact Toughness**: Minimum impact toughness at specific temperatures
- **Creep Resistance**: High-temperature creep resistance requirements

### Environmental Requirements
Rules for specific environmental conditions:
- **Low Temperature**: Arctic or cryogenic applications
- **High Temperature**: Elevated temperature service
- **Corrosive Environment**: Enhanced corrosion resistance
- **Radiation Environment**: Nuclear radiation resistance

### Quality Requirements
Enhanced quality and testing requirements:
- **Non-Destructive Testing**: Mandatory NDT requirements
- **Destructive Testing**: Additional mechanical testing
- **Chemical Analysis**: Enhanced chemical analysis requirements
- **Microstructure**: Specific microstructure requirements

## Compliance and Validation

### Rule Validation
- **Process Compatibility**: Ensure rule is compatible with welding process
- **Specification Alignment**: Verify alignment with referenced specifications
- **Technical Feasibility**: Confirm technical feasibility of requirements
- **Industry Standards**: Ensure compliance with industry standards

### Documentation Requirements
- **Technical Justification**: Document technical basis for special rules
- **Test Data**: Supporting test data and validation results
- **Approval Process**: Formal approval process for special rules
- **Change Control**: Controlled process for rule modifications

## Implementation Considerations

### Procedure Development
- **WPS Development**: Incorporate special rules into welding procedures
- **Qualification Testing**: Validate procedures meet special requirements
- **Documentation**: Document special requirements in procedures
- **Training**: Train welders on special requirements

### Quality Control
- **Inspection Plans**: Develop enhanced inspection plans
- **Testing Protocols**: Implement additional testing protocols
- **Acceptance Criteria**: Define specific acceptance criteria
- **Non-Conformance**: Procedures for handling non-conformances

## Related Entities

- [WeldingProcess](WeldingProcess.md) - The welding process this rule applies to
- [Specification](Specification.md) - Related specifications
- [ElectrodeClassification](ElectrodeClassification.md) - Electrode classifications affected by rules
- [Equipment](Equipment.md) - Equipment that may be subject to special rules

## Database Considerations

- The `WeldingArcDesignationSpec` property has a maximum length of 255 characters
- The `MinimumYieldStrength` property has precision (6,3) for accurate strength values
- Index on `WeldingProcessId` for process-based queries
- Index on `SpecificationId` for specification-based queries
- Foreign key constraints should be properly configured
- Consider adding fields for rule effective dates and approval status

## Future Enhancements

### Rule Management
- **Rule Versioning**: Track versions of special rules
- **Approval Workflow**: Implement approval workflow for new rules
- **Impact Analysis**: Analyze impact of rule changes
- **Notification System**: Notify affected parties of rule changes

### Integration Features
- **Procedure Integration**: Automatic integration with welding procedures
- **Compliance Checking**: Automated compliance checking
- **Reporting**: Enhanced reporting on special rule compliance
- **Training Integration**: Integration with training management systems
