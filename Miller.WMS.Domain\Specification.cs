﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class Specification : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }


    public SpecificationType SpecificationType { get; set; }
    public string? OtherSpecification { get; set; } // required if Type == Other (enforce in domain)
    public string Code { get; set; } = null!;
    public string Title { get; set; } = null!;
    public string? Description { get; set; }

    public Guid IssuingOrganizationId { get; set; }
    public IssuingOrganization IssuingOrganization { get; set; } = null!;

    public SpecificationStatus Status { get; set; }

    public Guid? SupersededSpecId { get; set; }
    public Specification? SupersededSpec { get; set; }
    public ICollection<Specification> SupersedingSpecs { get; set; } = [];
}
