﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WorkCenterHasEquipmentConfiguration : IEntityTypeConfiguration<WorkCenterHasEquipment>
{
    public void Configure(EntityTypeBuilder<WorkCenterHasEquipment> builder)
    {
        builder.HasKey(e => new { e.WorkCenterId, e.EquipmentId });

        builder.HasIndex(e => e.EquipmentId);

        builder.HasOne(e => e.WorkCenter)
               .WithMany()
               .HasForeignKey(e => e.WorkCenterId);

        builder.HasOne(e => e.Equipment)
               .WithMany()
               .HasForeignKey(e => e.EquipmentId);
    }
}
