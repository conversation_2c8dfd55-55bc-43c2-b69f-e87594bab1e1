﻿<?xml version="1.0" encoding="utf-8"?>
<ClassDiagram MajorVersion="1" MinorVersion="1">
  <Class Name="Miller.WMS.Domain.CatElectrodeSpecialRule">
    <Position X="15.5" Y="17.25" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedFromPoint="true">
      <Path>
        <Point X="17.562" Y="17.25" />
        <Point X="17.562" Y="14.7" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAABAAAiBAAAACAAAAAAAASAAAAAAAAAE=</HashCode>
      <FileName>CatElectrodeSpecialRule.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WeldingProcess" />
      <Property Name="Specification" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Customer">
    <Position X="56.5" Y="20" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAAAAACAgAAQAAAAAQAAAAAAAAAAAAAE=</HashCode>
      <FileName>Customer.cs</FileName>
    </TypeIdentifier>
    <ShowAsCollectionAssociation>
      <Property Name="Facilities" />
    </ShowAsCollectionAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.CustomerFacility">
    <Position X="59.75" Y="19.5" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAECCAABAAAAAAEIACAAAAQAAAIAABADAAAABAAAAAE=</HashCode>
      <FileName>CustomerFacility.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Customer" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Electrode">
    <Position X="4" Y="6.25" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="MetalChemicalComposition" Type="Miller.WMS.Domain.MetalChemicalComposition" FixedFromPoint="true">
      <Path>
        <Point X="6.5" Y="7.468" />
        <Point X="10.25" Y="7.468" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.154" Y="-0.411" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AEACCAABAAAAAAAAAiAAAACAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>Electrode.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="MetalChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassification">
    <Position X="10.25" Y="13.5" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="MetalChemicalCompositionId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
      <Property Name="SpecificationId" Hidden="true" />
      <Property Name="TungstenElectrodeClassificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedToPoint="true">
      <Path>
        <Point X="13" Y="13.938" />
        <Point X="15.5" Y="13.938" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="CoveringType" Type="Miller.WMS.Domain.ElectrodeCoveringType" FixedToPoint="true">
      <Path>
        <Point X="10.25" Y="15.188" />
        <Point X="9" Y="15.188" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="TungstenElectrodeClassification" Type="Miller.WMS.Domain.TungstenElectrodeClassification" FixedToPoint="true">
      <Path>
        <Point X="10.25" Y="14.788" />
        <Point X="6.25" Y="14.788" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.245" Y="0.142" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="MetalChemicalComposition" Type="Miller.WMS.Domain.MetalChemicalComposition" FixedToPoint="true">
      <Path>
        <Point X="11.625" Y="13.5" />
        <Point X="11.625" Y="10.661" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.95" Y="0.171" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>gkACCAABgAAAEBAAAiQAAAAiAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>ElectrodeClassification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Specification" />
      <Property Name="CoveringType" />
      <Property Name="TungstenElectrodeClassification" />
      <Property Name="MetalChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="1.634" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassificationCurrentType">
    <Position X="12" Y="20.75" Width="3" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ElectrodeClassificationId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.06" Y="0.371" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="CurrentType" Type="Miller.WMS.Domain.CurrentType">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.117" Y="0.112" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABAAAIAAAAgCAAAAAAAAAAAAAAAAAAEAAAAAE=</HashCode>
      <FileName>ElectrodeClassificationHasCurrentType.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeClassification" />
      <Property Name="CurrentType" />
    </ShowAsAssociation>
    <Lollipop Position="0.542" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassificationHasWeldingPosition">
    <Position X="8.25" Y="11.5" Width="3" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ElectrodeClassificationId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification" ManuallyRouted="true">
      <Path>
        <Point X="11.062" Y="12.368" />
        <Point X="11.062" Y="12.781" />
        <Point X="11.062" Y="12.781" />
        <Point X="11.062" Y="13.5" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.024" Y="0.336" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABAAAIAAAAgCAAAAAAAABAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>ElectrodeClassificationHasWeldingPosition.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeClassification" />
      <Property Name="WeldingPosition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeClassificationHasWeldingProcess">
    <Position X="12.5" Y="16" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ElectrodeClassificationId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.149" Y="0.106" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="WeldingProcess" Type="Miller.WMS.Domain.WeldingProcess">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.307" Y="0.122" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABAAAIAAAAgCAAAAAAAAAAAAAAQAAAAAAAAAE=</HashCode>
      <FileName>ElectrodeClassificationHasWeldingProcess.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeClassification" />
      <Property Name="WeldingProcess" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeDiameter">
    <Position X="51.75" Y="4.75" Width="2.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABACAAAAAAACAAAAAAAAAAAAAAAAABAAAAQAE=</HashCode>
      <FileName>ElectrodeDiameter.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="ElectrodeType" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeHasElectrodeClassification">
    <Position X="3.5" Y="12.75" Width="3.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="ElectrodeClassification" Type="Miller.WMS.Domain.ElectrodeClassification" FixedFromPoint="true">
      <Path>
        <Point X="6.75" Y="13.675" />
        <Point X="10.25" Y="13.675" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABAAAIAAAAgCAAAAAAAAAAAkAAAAAAAAAAAAE=</HashCode>
      <FileName>ElectrodeHasElectrodeClassification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Electrode" />
      <Property Name="ElectrodeClassification" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.ElectrodeMadeByManufacturer">
    <Position X="33" Y="5.75" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="ManufacturerFacility" Type="Miller.WMS.Domain.ManufacturerFacility" FixedFromPoint="true">
      <Path>
        <Point X="35.75" Y="6.802" />
        <Point X="38.25" Y="6.802" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.122" Y="-0.343" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAgACAABAAAAAAAAACAAAAAAAAEAAkAEAAQAAAAAAAE=</HashCode>
      <FileName>ElectrodeMadeByManufacturer.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Electrode" />
      <Property Name="Manufacturer" />
      <Property Name="ManufacturerFacility" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Equipment">
    <Position X="42.5" Y="29.5" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ManufacturerId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Manufacturer" Type="Miller.WMS.Domain.Manufacturer" FixedFromPoint="true">
      <Path>
        <Point X="43.625" Y="29.5" />
        <Point X="43.625" Y="7.488" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.135" Y="0.19" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>ABgCCAAFAAAAAAAAACAAAAAAAAEAAAAAAQgAAAAAAAE=</HashCode>
      <FileName>Equipment.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Manufacturer" />
      <Property Name="Type" />
      <Property Name="SubType" />
      <Property Name="Status" />
    </ShowAsAssociation>
    <Lollipop Position="1.3" />
  </Class>
  <Class Name="Miller.WMS.Domain.EquipmentHasCurrentType">
    <Position X="37.5" Y="30.25" Width="3.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="EquipmentId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Equipment" Type="Miller.WMS.Domain.Equipment">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.071" Y="-0.41" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABAAAAAAAAACAAAAQAIAAAAAAAAAAAEAAAAAE=</HashCode>
      <FileName>EquipmentHasCurrentType.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Equipment" />
      <Property Name="CurrentType" />
    </ShowAsAssociation>
    <Lollipop Position="0.69" />
  </Class>
  <Class Name="Miller.WMS.Domain.Facility">
    <Position X="37" Y="22" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Organization" Type="Miller.WMS.Domain.Organization">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.057" Y="0.07" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAASCAABAAAAAAAAACAgAAQAAQAAAAACAAAAAAAAIAE=</HashCode>
      <FileName>Facility.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Organization" />
    </ShowAsAssociation>
    <ShowAsCollectionAssociation>
      <Property Name="UserFacilityRoles" />
    </ShowAsCollectionAssociation>
    <Lollipop Position="0.827" />
  </Class>
  <Class Name="Miller.WMS.Domain.FacilityAreaLevelOne">
    <Position X="33.75" Y="22.75" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FacilityId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAAKCAABAAAAAAIAACAAAAAAAQAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>FacilityAreaLevelOne.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Facility" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FacilityAreaLevelThree">
    <Position X="27.25" Y="26" Width="2.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FacilityAreaLevelTwoId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="FacilityAreaLevelTwo" Type="Miller.WMS.Domain.FacilityAreaLevelTwo" FixedFromPoint="true">
      <Path>
        <Point X="29.5" Y="26.762" />
        <Point X="31.25" Y="26.762" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.255" Y="-0.345" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAAAAACABAAAAAQAAAAAAABAAAAAAAAE=</HashCode>
      <FileName>FacilityAreaLevelThree.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FacilityAreaLevelTwo" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FacilityAreaLevelTwo">
    <Position X="31.25" Y="26" Width="3.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FacilityAreaLevelOneId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="FacilityAreaLevelOne" Type="Miller.WMS.Domain.FacilityAreaLevelOne">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.649" Y="0.203" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAAACACAAAAAAAQAAAAAAAAAAIAAAAAE=</HashCode>
      <FileName>FacilityAreaLevelTwo.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FacilityAreaLevelOne" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Flux">
    <Position X="56.25" Y="11.5" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FluxChemicalCompositionId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAADAAAAAQAAACAAAACAAAAAAAAAAAQAAAAQAAE=</HashCode>
      <FileName>Flux.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FluxChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Gas">
    <Position X="21.5" Y="25" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="GasChemicalCompositionId" Hidden="true" />
      <Property Name="GasClassificationId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="GasClassification" Type="Miller.WMS.Domain.GasClassification">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.179" Y="0.074" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="GasChemicalComposition" Type="Miller.WMS.Domain.GasChemicalComposition">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.874" Y="0.156" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAABAACQABAQAAAAAAAAAAAAIAAAAAAE=</HashCode>
      <FileName>Gas.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="GasClassification" />
      <Property Name="GasChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.GasChemicalComposition">
    <Position X="20" Y="27.25" Width="2" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAMAAAACAAAAAAAAAAAQAAIAAAAAAAAAE=</HashCode>
      <FileName>GasChemicalComposition.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.214" />
  </Class>
  <Class Name="Miller.WMS.Domain.GasClassification">
    <Position X="23.5" Y="27.5" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACSEABAAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>GasClassification.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.559" />
  </Class>
  <Class Name="Miller.WMS.Domain.IssuingOrganization">
    <Position X="22" Y="13.5" Width="2.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCABBAAAggAAAACAAAAQAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>IssuingOrganization.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Manufacturer">
    <Position X="42.5" Y="6" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAAAAECAAAAQAAAAAAAAAAQAAAAAAAAE=</HashCode>
      <FileName>Manufacturer.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Type" />
    </ShowAsAssociation>
    <Lollipop Position="1.075" />
  </Class>
  <Class Name="Miller.WMS.Domain.ManufacturerFacility">
    <Position X="38.25" Y="6.75" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Manufacturer" Type="Miller.WMS.Domain.Manufacturer" ManuallyRouted="true" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="40.75" Y="7.125" />
        <Point X="42.5" Y="7.125" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAgCCAABAAAAAAAIACAAAAQAAAMAAAADAAAABAAAAAE=</HashCode>
      <FileName>ManufacturerFacility.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Manufacturer" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Material">
    <Position X="24.5" Y="8.75" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FulfilledById" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="FulfilledBy" Type="Miller.WMS.Domain.Material" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="27" Y="10.687" />
        <Point X="27.25" Y="10.687" />
        <Point X="27.25" Y="11.438" />
        <Point X="27" Y="11.438" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AMAiCAABQEGAAAAAgCIAAAAAAAAAAgAAEQAAQAkAIAE=</HashCode>
      <FileName>Material.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FulfilledBy" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.MaterialSubstitution">
    <Position X="17.75" Y="9.25" Width="2.25" />
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedFromPoint="true">
      <Path>
        <Point X="18.125" Y="11.7" />
        <Point X="18.125" Y="12.25" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true" ManuallySized="true">
        <Position X="0.048" Y="0.064" Height="0.182" Width="1.079" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AEQCCAABAAAAABAAAiQAAAAAAAAAAAAAABAAAIAAAAE=</HashCode>
      <FileName>MaterialSubstitution.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Specification" />
      <Property Name="Material" />
      <Property Name="SubstitutedBy" />
      <Property Name="ApprovalType" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.MetalChemicalComposition">
    <Position X="10.25" Y="7.25" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABiAAAAAgMACAASAQAAABCCAAAAAAIAAAAAAE=</HashCode>
      <FileName>MetalChemicalComposition.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Organization">
    <Position X="37.25" Y="19.5" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCCAFAAAAAAAAACAAAAQAAAAAQAAAAAAAAgAAAAE=</HashCode>
      <FileName>Organization.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="IndustryType" />
      <Property Name="Status" />
    </ShowAsAssociation>
    <Lollipop Position="0.976" />
  </Class>
  <Class Name="Miller.WMS.Domain.Specification">
    <Position X="15.5" Y="12.25" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="SpecificationType" Type="Miller.WMS.Domain.SpecificationType" FixedToPoint="true">
      <Path>
        <Point X="18.25" Y="14.312" />
        <Point X="19.75" Y="14.312" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="SupersededSpec" Type="Miller.WMS.Domain.Specification" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="18.25" Y="13.106" />
        <Point X="18.5" Y="13.106" />
        <Point X="18.5" Y="12.562" />
        <Point X="18.25" Y="12.562" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.312" Y="-0.742" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACCAAFAAAiAFIAACAgAAAAAAAAAAAAAAAAAAIAAEM=</HashCode>
      <FileName>Specification.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="SpecificationType" />
      <Property Name="IssuingOrganization" />
      <Property Name="Status" />
      <Property Name="SupersededSpec" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.SupplementalFillerMetal">
    <Position X="51.25" Y="20" Width="3.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAAAAACAAAAAAAAAAABAAAAAAAAAAAAE=</HashCode>
      <FileName>SupplementalFillerMetal.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.TungstenElectrodeClassification">
    <Position X="3.5" Y="14.5" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCQABAAAAAAgAACQAAAAAAAAAAAAAAIAAgAAAgAE=</HashCode>
      <FileName>TungstenElectrodeClassification.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.User">
    <Position X="40" Y="19.25" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="UserFacilityRoles" Type="Miller.WMS.Domain.UserFacilityRole">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.134" Y="0.324" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAASCAABACAAAAAAACAAAAQAAQAAAAAAAAAAAAAAIAE=</HashCode>
      <FileName>User.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Organization" />
    </ShowAsAssociation>
    <ShowAsCollectionAssociation>
      <Property Name="UserFacilityRoles" />
    </ShowAsCollectionAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.UserFacilityRole">
    <Position X="40.25" Y="22.75" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>EAAJCAABAAAAAAIAACAAAAgAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>UserFacilityRole.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="User" />
      <Property Name="Facility" />
    </ShowAsAssociation>
    <Lollipop Position="1.086" />
  </Class>
  <Class Name="Miller.WMS.Domain.ANumberElectrode">
    <Position X="15.5" Y="7.5" Width="2.25" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="MetalChemicalCompositionId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
      <Property Name="SpecificationId" Hidden="true" />
    </Members>
    <AssociationLine Name="Specification" Type="Miller.WMS.Domain.Specification" FixedToPoint="true">
      <Path>
        <Point X="16.938" Y="8.796" />
        <Point X="16.938" Y="12.25" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.049" Y="0.29" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="MetalChemicalComposition" Type="Miller.WMS.Domain.MetalChemicalComposition">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.336" Y="-0.446" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AEACCAABAAAAABAAAiAAAAQAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>ANumberElectrode.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Specification" />
      <Property Name="MetalChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FluxClass">
    <Position X="60.5" Y="14" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAAAAACAAAAQAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>FluxClass.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.883" />
  </Class>
  <Class Name="Miller.WMS.Domain.FluxHasFluxClass" Collapsed="true">
    <Position X="60.25" Y="11.75" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FluxClassId" Hidden="true" />
      <Property Name="FluxId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <AssociationLine Name="Flux" Type="Miller.WMS.Domain.Flux">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.129" Y="-0.606" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="FluxClass" Type="Miller.WMS.Domain.FluxClass">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.122" Y="0.132" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABAAAAAAAAACAAAQAAAAAwAgAAAAAAAAAAAAE=</HashCode>
      <FileName>FluxHasFluxClass.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Flux" />
      <Property Name="FluxClass" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenter">
    <Position X="27.75" Y="21.75" Width="4" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FacilityAreaLevelOneId" Hidden="true" />
      <Property Name="FacilityAreaLevelThreeId" Hidden="true" />
      <Property Name="FacilityAreaLevelTwoId" Hidden="true" />
      <Property Name="FacilityId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Type" Type="Miller.WMS.Domain.WorkCenterType" FixedFromPoint="true">
      <Path>
        <Point X="29.25" Y="21.75" />
        <Point X="29.25" Y="20.931" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="Facility" Type="Miller.WMS.Domain.Facility" FixedToPoint="true">
      <Path>
        <Point X="31.75" Y="22.17" />
        <Point X="37" Y="22.17" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="FacilityAreaLevelOne" Type="Miller.WMS.Domain.FacilityAreaLevelOne" FixedToPoint="true">
      <Path>
        <Point X="31.75" Y="23.022" />
        <Point X="33.75" Y="23.022" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="FacilityAreaLevelTwo" Type="Miller.WMS.Domain.FacilityAreaLevelTwo">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.528" Y="0.518" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="FacilityAreaLevelThree" Type="Miller.WMS.Domain.FacilityAreaLevelThree">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.662" Y="0.141" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAKCAAFAAAgAAICQCABAAQAAAAAAAAAARAIIAAAAAE=</HashCode>
      <FileName>WorkCenter.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="Type" />
      <Property Name="Status" />
      <Property Name="Facility" />
      <Property Name="FacilityAreaLevelOne" />
      <Property Name="FacilityAreaLevelTwo" />
      <Property Name="FacilityAreaLevelThree" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.Waveform">
    <Position X="25.25" Y="26.5" Width="1.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>IAACCAABAAAgAAAAACAAAAQAAAAAAAAAAAAAAAAAAAE=</HashCode>
      <FileName>Waveform.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasElectrode">
    <Position X="28.5" Y="6.75" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="WorkCenter" Type="Miller.WMS.Domain.WorkCenter">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-1.095" Y="0.068" />
      </MemberNameLabel>
    </AssociationLine>
    <AssociationLine Name="Electrode" Type="Miller.WMS.Domain.Electrode" FixedFromPoint="true">
      <Path>
        <Point X="28.5" Y="6.859" />
        <Point X="6.5" Y="6.859" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABgAAAAAAAACAAAAAAAAAAAkAAAAAAAAAAQAE=</HashCode>
      <FileName>WorkCenterHasElectrode.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Electrode" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasEquipment">
    <Position X="18" Y="21.25" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="WorkCenter" Type="Miller.WMS.Domain.WorkCenter" FixedFromPoint="true">
      <Path>
        <Point X="20.75" Y="21.794" />
        <Point X="27.75" Y="21.794" />
      </Path>
    </AssociationLine>
    <AssociationLine Name="Equipment" Type="Miller.WMS.Domain.Equipment" FixedFromPoint="true" FixedToPoint="true">
      <Path>
        <Point X="19.344" Y="22.546" />
        <Point X="19.344" Y="29.664" />
        <Point X="42.5" Y="29.664" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABgAAAAAAAACAAAAQAIAAAAAAAAAAAAAAAQAE=</HashCode>
      <FileName>WorkCenterHasEquipment.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Equipment" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasGas">
    <Position X="21.25" Y="22.25" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
    </Members>
    <AssociationLine Name="Gas" Type="Miller.WMS.Domain.Gas" FixedFromPoint="true">
      <Path>
        <Point X="23.188" Y="23.546" />
        <Point X="23.188" Y="25" />
      </Path>
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="-0.575" Y="0.155" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAgACAABwAAAAAAAACAAAAAAAAAAAAAAAAAAAAAAQAE=</HashCode>
      <FileName>WorkCenterHasGas.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Gas" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.WorkCenterHasWaveform">
    <Position X="25.75" Y="24.5" Width="2.75" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
      <Property Name="ModifiedBy" Hidden="true" />
      <Property Name="WaveformId" Hidden="true" />
      <Property Name="WorkCenterId" Hidden="true" />
    </Members>
    <AssociationLine Name="Waveform" Type="Miller.WMS.Domain.Waveform">
      <MemberNameLabel ManuallyPlaced="true">
        <Position X="0.086" Y="0.54" />
      </MemberNameLabel>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAAACAABgAAAAAAAADAAAAAAAAAAAAAAAAABAAAAQAE=</HashCode>
      <FileName>WorkCenterHasWaveform.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="WorkCenter" />
      <Property Name="Waveform" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FluxChemicalComposition">
    <Position X="52.25" Y="11.5" Width="2" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAgAAAAACAAAQAAAAAAAAAAAAAAAAAACAE=</HashCode>
      <FileName>FluxChemicalComposition.cs</FileName>
    </TypeIdentifier>
    <Lollipop Position="0.2" />
  </Class>
  <Class Name="Miller.WMS.Domain.FluxChemicalCompositionLimits">
    <Position X="52" Y="14.5" Width="2.5" />
    <Members>
      <Property Name="CreatedAt" Hidden="true" />
      <Property Name="CreatedBy" Hidden="true" />
      <Property Name="FluxChemicalCompositionId" Hidden="true" />
      <Property Name="ModifiedAt" Hidden="true" />
    </Members>
    <AssociationLine Name="FluxChemicalComposition" Type="Miller.WMS.Domain.FluxChemicalComposition" FixedToPoint="true">
      <Path>
        <Point X="53.438" Y="14.5" />
        <Point X="53.438" Y="13.373" />
      </Path>
    </AssociationLine>
    <TypeIdentifier>
      <HashCode>AAACCAABAAAAAQAAACAAAAAAAAAAAgAIAAAIAAAQAAE=</HashCode>
      <FileName>FluxChemicalCompositionLimits.cs</FileName>
    </TypeIdentifier>
    <ShowAsAssociation>
      <Property Name="FluxChemicalComposition" />
    </ShowAsAssociation>
    <Lollipop Position="0.2" />
  </Class>
  <Enum Name="Miller.WMS.Domain.ElectrodeCoveringType">
    <Position X="6.5" Y="15" Width="2.5" />
    <TypeIdentifier>
      <HashCode>ACAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAAAABAAAAABA=</HashCode>
      <FileName>ElectrodeCoveringType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.ElectrodeType">
    <Position X="51.75" Y="6.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAABAAAAAAAAAAgAAIAAAAAAAAAAAAAAA=</HashCode>
      <FileName>ElectrodeType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.EquipmentStatus">
    <Position X="42" Y="26.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAACAAAAAAAABAAAAAAAAAAAAAAAAAgAAAAAAAAAAA=</HashCode>
      <FileName>EquipmentStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.EquipmentSubType">
    <Position X="42.25" Y="32" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAACIAQAgAABAAAAAAAAAAAIBAAAAAAAAAAAABJAAAA=</HashCode>
      <FileName>EquipmentSubType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.EquipmentType">
    <Position X="45.25" Y="30.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAAAAAAgAAAIAAAAAAAAAAAAAAAAIAACAGAAAQAAAA=</HashCode>
      <FileName>EquipmentType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.ManufacturerType">
    <Position X="42.25" Y="4" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAQAQAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAAAA=</HashCode>
      <FileName>ManufacturerType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.MaterialSubstitutionApprovalType">
    <Position X="21.5" Y="8.5" Width="2.75" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAEAAAA=</HashCode>
      <FileName>MaterialSubstitutionApprovalType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.OrganizationIndustryType">
    <Position X="37" Y="17.25" Width="2.25" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAAAAAAEAAAAA=</HashCode>
      <FileName>OrganizationIndustryType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.OrganizationStatus">
    <Position X="33.75" Y="18.75" Width="2.5" />
    <TypeIdentifier>
      <HashCode>AAAACABAAAAAAAAAAAAAAAAAAAAAAAAgAAAAAAAAAAA=</HashCode>
      <FileName>OrganizationStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.SpecificationStatus">
    <Position X="12.75" Y="11.5" Width="2" />
    <TypeIdentifier>
      <HashCode>AAAACAAAAAAAAAEAAAAAAAAAAAAAAAAgAAAAAAAAAAA=</HashCode>
      <FileName>SpecificationStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.SpecificationType">
    <Position X="19.75" Y="14" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BEBAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAEAAA=</HashCode>
      <FileName>SpecificationType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WeldingPosition">
    <Position X="8" Y="8.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAABAAAAAAAAAEAIAAAAAAACAABAAIAAAAAA=</HashCode>
      <FileName>WeldingPosition.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WeldingProcess">
    <Position X="12.5" Y="17.25" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AATAAABAAAAAAAAAAAJAAgAAAAAAAAAAAAABAABAACA=</HashCode>
      <FileName>WeldingProcess.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WorkCenterStatus">
    <Position X="24.75" Y="22.75" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAACABAAAAAAAAAAAAAAAAAAAAAAAAgAAACAAAAAAA=</HashCode>
      <FileName>WorkCenterStatus.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.WorkCenterType">
    <Position X="28" Y="19.5" Width="1.5" />
    <TypeIdentifier>
      <HashCode>BAAAAAAAAAAAAAAAAAAAAAAAAAAAAIAAAACAAAQAAAA=</HashCode>
      <FileName>WorkCenterType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Enum Name="Miller.WMS.Domain.CurrentType">
    <Position X="12.75" Y="30" Width="1.5" />
    <TypeIdentifier>
      <HashCode>AAAAAAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAACgA=</HashCode>
      <FileName>CurrentType.cs</FileName>
    </TypeIdentifier>
  </Enum>
  <Font Name="Segoe UI" Size="9" />
</ClassDiagram>