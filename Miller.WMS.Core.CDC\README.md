# Miller WMS Core CDC Service

A Java-based Change Data Capture (CDC) service using Debezium Engine to capture changes from PostgreSQL and forward them to Elasticsearch.

## Overview

This service embeds the Debezium Engine to monitor the `Organizations` table in PostgreSQL and automatically index changes to Elasticsearch in real-time. It's designed to be orchestrated by .NET Aspire alongside other WMS services.

## Features

- **Embedded Debezium Engine** - No external Kafka required
- **PostgreSQL CDC** - Monitors Organization table changes using logical replication
- **Elasticsearch Integration** - Automatically indexes changes to Elasticsearch
- **Aspire Orchestrated** - Fully managed by .NET Aspire AppHost
- **Health Monitoring** - Provides `/ping` endpoint for health checks
- **Environment-based Configuration** - Uses environment variables for all connections

## Prerequisites

- Java 21 or higher
- Maven 3.6 or higher
- PostgreSQL with logical replication enabled (wal_level=logical)
- Elasticsearch 8.x

## Building

```bash
# Build the application
mvn clean package

# This creates target/miller-wms-core-cdc-1.0.0.jar
```

## Configuration

The service is configured via environment variables (set by Aspire):

### Database Configuration
- `DB_HOST` - PostgreSQL host
- `DB_PORT` - PostgreSQL port (default: 5432)
- `DB_NAME` - Database name (default: wms)
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password

### Elasticsearch Configuration
- `ES_HOST` - Elasticsearch host
- `ES_PORT` - Elasticsearch port (default: 9200)
- `ES_INDEX_NAME` - Index name for organizations (default: organizations)

### Application Configuration
- `HEALTH_PORT` - Health check port (default: 8080)

## Running with Aspire

The service is automatically started by the Aspire AppHost when you run:

```bash
dotnet run --project Miller.WMS.Edge.AppHost
```

The service will:
1. Wait for PostgreSQL and Elasticsearch to be healthy
2. Wait for the DataService to seed the database
3. Start monitoring the Organizations table for changes
4. Index any changes to Elasticsearch in real-time

## Health Check

The service provides a health check endpoint at `/ping` that returns `"true"` when healthy.

## Monitoring

The service integrates with .NET Aspire's monitoring and uses OpenTelemetry for observability. Logs and metrics are available through the Aspire dashboard.

## Architecture

```
PostgreSQL (Organizations table) 
    ↓ (Logical Replication)
Debezium Engine 
    ↓ (Change Events)
Elasticsearch Sink 
    ↓ (Index Operations)
Elasticsearch (organizations index)
```

## Development

### Local Development
1. Ensure PostgreSQL has logical replication enabled
2. Build the Java application: `mvn clean package`
3. Run the Aspire AppHost: `dotnet run --project Miller.WMS.Edge.AppHost`

### Testing
Integration tests are available in the `Miller.WMS.Edge.Tests` project:
```bash
dotnet test Miller.WMS.Edge.Tests --filter "CdcTests"
```

## Troubleshooting

### Common Issues

1. **PostgreSQL Connection Issues**
   - Ensure wal_level=logical is set
   - Verify the replication slot is created
   - Check database permissions

2. **Elasticsearch Connection Issues**
   - Verify Elasticsearch is running and accessible
   - Check index permissions

3. **Java/Maven Issues**
   - Ensure Java 21+ is installed
   - Verify Maven can download dependencies

### Logs
Check the Aspire dashboard for detailed logs and metrics from the CDC service.
