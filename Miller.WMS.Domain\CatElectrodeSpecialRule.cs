﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class CatElectrodeSpecialRule : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public string WeldingArcDesignationSpec { get; set; } = null!;

    // FKs
    public Guid WeldingProcessId { get; set; }
    public Guid? SpecificationId { get; set; }

    // Other fields
    public decimal? MinimumYieldStrength { get; set; }

    // Navigation properties (assumed targets; adjust names/types if your model differs)
    public WeldingProcess WeldingProcess { get; set; }
    public Specification? Specification { get; set; }
}