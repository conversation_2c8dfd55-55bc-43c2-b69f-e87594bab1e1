﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FacilityAreaLevelTwoConfiguration : IEntityTypeConfiguration<FacilityAreaLevelTwo>
{
    public void Configure(EntityTypeBuilder<FacilityAreaLevelTwo> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.FacilityAreaName)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.FacilityAreaLevelOne)
               .WithMany()
               .HasForeign<PERSON>ey(e => e.FacilityAreaLevelOneId);
    }
}
