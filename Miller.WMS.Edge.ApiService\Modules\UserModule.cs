using <PERSON>;
using Facet;
using Facet.Extensions;
using Microsoft.EntityFrameworkCore;
using Miller.WMS.Shared.Data;
using Miller.WMS.Domain;

namespace Miller.WMS.Edge.ApiService.Modules;

public class UserModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/users")
            .WithTags("Users")
            .WithOpenApi();

        group.MapGet("/", GetUsers)
            .WithName("GetUsers")
            .WithSummary("Get all users")
            .WithDescription("Retrieves a list of all users");

        group.MapGet("/{id:guid}", GetUserById)
            .WithName("GetUserById")
            .WithSummary("Get user by ID")
            .WithDescription("Retrieves a specific user by their ID");

        group.MapGet("/organization/{organizationId:guid}", GetUsersByOrganization)
            .WithName("GetUsersByOrganization")
            .WithSummary("Get users by organization")
            .WithDescription("Retrieves all users for a specific organization");

        group.MapGet("/email/{email}", GetUserByEmail)
            .WithName("GetUserByEmail")
            .WithSummary("Get user by email")
            .WithDescription("Retrieves a user by their email address");

        group.MapPost("/", CreateUser)
            .WithName("CreateUser")
            .WithSummary("Create a new user")
            .WithDescription("Creates a new user");

        group.MapPut("/{id:guid}", UpdateUser)
            .WithName("UpdateUser")
            .WithSummary("Update a user")
            .WithDescription("Updates an existing user");

        group.MapDelete("/{id:guid}", DeleteUser)
            .WithName("DeleteUser")
            .WithSummary("Delete a user")
            .WithDescription("Deletes a user");
    }

    private static async Task<IResult> GetUsers(WmsContext context)
    {
        var users = await context.Users
            .Include(u => u.Organization)
            .Include(u => u.UserFacilityRoles)
            .ToListAsync();

        var userDtos = users.SelectFacets<User, UserDto>().ToList();
        return Results.Ok(userDtos);
    }

    private static async Task<IResult> GetUserById(Guid id, WmsContext context)
    {
        var user = await context.Users
            .Include(u => u.Organization)
            .Include(u => u.UserFacilityRoles)
            .FirstOrDefaultAsync(u => u.Id == id);

        if (user == null)
            return Results.NotFound($"User with ID {id} not found");

        return Results.Ok(user.ToFacet<User, UserDto>());
    }

    private static async Task<IResult> GetUsersByOrganization(Guid organizationId, WmsContext context)
    {
        var users = await context.Users
            .Include(u => u.Organization)
            .Include(u => u.UserFacilityRoles)
            .Where(u => u.OrganizationId == organizationId)
            .ToListAsync();

        var userDtos = users.SelectFacets<User, UserDto>().ToList();
        return Results.Ok(userDtos);
    }

    private static async Task<IResult> GetUserByEmail(string email, WmsContext context)
    {
        var user = await context.Users
            .Include(u => u.Organization)
            .Include(u => u.UserFacilityRoles)
            .FirstOrDefaultAsync(u => u.Email.ToLower() == email.ToLower());

        if (user == null)
            return Results.NotFound($"User with email {email} not found");

        return Results.Ok(user.ToFacet<User, UserDto>());
    }

    private static async Task<IResult> CreateUser(CreateUserRequest request, WmsContext context)
    {
        var user = request.ToFacet<CreateUserRequest, User>();

        // Verify organization exists
        var organizationExists = await context.Organizations.AnyAsync(o => o.Id == user.OrganizationId);
        if (!organizationExists)
            return Results.BadRequest($"Organization with ID {user.OrganizationId} not found");

        // Check if email already exists
        var emailExists = await context.Users.AnyAsync(u => u.Email.ToLower() == user.Email.ToLower());
        if (emailExists)
            return Results.BadRequest($"User with email {user.Email} already exists");

        user.Id = Guid.NewGuid();
        user.CreatedAt = DateTime.UtcNow;
        user.CreatedBy = Guid.NewGuid(); // Get from request token when auth configured

        context.Users.Add(user);
        await context.SaveChangesAsync();

        // Reload with includes for response
        var createdUser = await context.Users
            .Include(u => u.Organization)
            .Include(u => u.UserFacilityRoles)
            .FirstAsync(u => u.Id == user.Id);

        return Results.Created($"/api/users/{user.Id}", createdUser.ToFacet<User, UserDto>());
    }

    private static async Task<IResult> UpdateUser(Guid id, UpdateUserRequest request, WmsContext context)
    {
        var updatedUser = request.ToFacet<UpdateUserRequest, User>();

        // Validate that URL ID matches request object ID
        if (id != updatedUser.Id)
            return Results.BadRequest($"URL ID {id} does not match request object ID {updatedUser.Id}");

        var existingUser = await context.Users.FindAsync(id);
        if (existingUser == null)
            return Results.NotFound($"User with ID {id} not found");

        // Verify organization exists if changing
        if (updatedUser.OrganizationId != existingUser.OrganizationId)
        {
            var organizationExists = await context.Organizations.AnyAsync(o => o.Id == updatedUser.OrganizationId);
            if (!organizationExists)
                return Results.BadRequest($"Organization with ID {updatedUser.OrganizationId} not found");
        }

        // Check if email already exists for another user
        if (updatedUser.Email.ToLower() != existingUser.Email.ToLower())
        {
            var emailExists = await context.Users.AnyAsync(u => u.Email.ToLower() == updatedUser.Email.ToLower() && u.Id != id);
            if (emailExists)
                return Results.BadRequest($"User with email {updatedUser.Email} already exists");
        }

        // Preserve creation audit fields (excluded from request by design)
        updatedUser.CreatedAt = existingUser.CreatedAt;
        updatedUser.CreatedBy = existingUser.CreatedBy;
        updatedUser.ModifiedAt = DateTime.UtcNow;
        updatedUser.ModifiedBy = Guid.NewGuid(); // Get from request token when auth configured

        context.Entry(existingUser).CurrentValues.SetValues(updatedUser);
        await context.SaveChangesAsync();

        // Reload with includes for response
        var reloadedUser = await context.Users
            .Include(u => u.Organization)
            .Include(u => u.UserFacilityRoles)
            .FirstAsync(u => u.Id == id);

        return Results.Ok(reloadedUser.ToFacet<User, UserDto>());
    }

    private static async Task<IResult> DeleteUser(Guid id, WmsContext context)
    {
        var user = await context.Users.FindAsync(id);
        if (user == null)
            return Results.NotFound($"User with ID {id} not found");

        context.Users.Remove(user);
        await context.SaveChangesAsync();

        return Results.NoContent();
    }
}

// DTOs using Facet
[Facet(typeof(User))]
public partial class UserDto
{
}

[Facet(typeof(User),
    exclude: [
        nameof(User.Id),
        nameof(User.CreatedAt),
        nameof(User.CreatedBy),
        nameof(User.ModifiedAt),
        nameof(User.ModifiedBy)],
    Kind = FacetKind.Record)]
public partial record CreateUserRequest { }

[Facet(typeof(User),
    exclude: [
        nameof(User.CreatedAt),
        nameof(User.CreatedBy),
        nameof(User.ModifiedAt)],
    Kind = FacetKind.Record)]
public partial record UpdateUserRequest { }
