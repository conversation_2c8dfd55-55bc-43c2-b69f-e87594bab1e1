﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FacilityAreaLevelThreeConfiguration : IEntityTypeConfiguration<FacilityAreaLevelThree>
{
    public void Configure(EntityTypeBuilder<FacilityAreaLevelThree> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.FacilityAreaName)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.FacilityAreaLevelTwo)
               .WithMany()
               .HasForeignKey(e => e.FacilityAreaLevelTwoId);
    }
}
