# ElectrodeDiameter

**Source File:** [ElectrodeDiameter.cs](../ElectrodeDiameter.cs)

## Overview
The `ElectrodeDiameter` entity represents the available diameters for different types of welding electrodes. This entity is crucial for defining the physical dimensions of electrodes and ensuring proper electrode selection for welding applications.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the electrode diameter |
| `ElectrodeTypeId` | `Guid` | Yes | Foreign key to the electrode type |
| `Diameter` | `decimal` | Yes | Electrode diameter (6,3 precision) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the diameter was created |
| `CreatedBy` | `Guid?` | No | User who created the diameter record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the diameter record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `ElectrodeType` | `ElectrodeType` | The electrode type this diameter applies to |

## Relationships

### Many-to-One Relationships
- **ElectrodeDiameter → ElectrodeType**: Each diameter belongs to one electrode type

## Electrode Types and Standard Diameters

### Stick Electrodes (SMAW)
Common stick electrode diameters in inches:
- **1/16" (1.588 mm)**: Very thin electrodes for sheet metal
- **5/64" (1.984 mm)**: Thin electrodes for light gauge material
- **3/32" (2.381 mm)**: Common diameter for general purpose welding
- **1/8" (3.175 mm)**: Most popular diameter for structural welding
- **5/32" (3.969 mm)**: Heavy-duty welding applications
- **3/16" (4.763 mm)**: Thick section welding
- **1/4" (6.350 mm)**: Very heavy section welding

### TIG Filler Wires (GTAW)
Common TIG wire diameters:
- **0.030" (0.762 mm)**: Thin sheet metal welding
- **0.035" (0.889 mm)**: Light gauge applications
- **0.045" (1.143 mm)**: General purpose welding
- **1/16" (1.588 mm)**: Common diameter for structural work
- **3/32" (2.381 mm)**: Medium thickness applications
- **1/8" (3.175 mm)**: Heavy section welding
- **5/32" (3.969 mm)**: Very thick material welding

### MIG Wires (GMAW)
Common MIG wire diameters:
- **0.023" (0.584 mm)**: Very thin material welding
- **0.030" (0.762 mm)**: Thin sheet metal applications
- **0.035" (0.889 mm)**: Most common diameter for general welding
- **0.045" (1.143 mm)**: Heavy-duty applications
- **0.052" (1.321 mm)**: Thick section welding
- **1/16" (1.588 mm)**: Industrial applications
- **3/32" (2.381 mm)**: Very heavy section welding

### Tungsten Electrodes (GTAW)
Common tungsten electrode diameters:
- **0.040" (1.016 mm)**: Precision welding applications
- **1/16" (1.588 mm)**: Light to medium amperage welding
- **3/32" (2.381 mm)**: Medium amperage applications
- **1/8" (3.175 mm)**: High amperage welding
- **5/32" (3.969 mm)**: Very high amperage applications
- **3/16" (4.763 mm)**: Heavy-duty welding
- **1/4" (6.350 mm)**: Maximum amperage applications

## Business Rules

1. **Electrode Type Association**: Every diameter must be associated with an electrode type
2. **Precision Requirements**: Diameters must be specified with appropriate precision (6,3)
3. **Standard Sizes**: Diameters should conform to industry standard sizes
4. **Uniqueness**: Diameters should be unique within each electrode type
5. **Measurement Units**: Consider standardizing on metric or imperial units

## Usage Examples

### Creating Standard Stick Electrode Diameters
```csharp
var stickDiameters = new List<ElectrodeDiameter>
{
    new ElectrodeDiameter
    {
        Id = Guid.NewGuid(),
        ElectrodeTypeId = stickElectrodeTypeId,
        Diameter = 1.588m, // 1/16"
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "system"
    },
    new ElectrodeDiameter
    {
        ElectrodeTypeId = stickElectrodeTypeId,
        Diameter = 3.175m, // 1/8"
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "system"
    },
    new ElectrodeDiameter
    {
        ElectrodeTypeId = stickElectrodeTypeId,
        Diameter = 4.763m, // 3/16"
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "system"
    }
};
```

### Creating MIG Wire Diameters
```csharp
var migDiameters = new List<ElectrodeDiameter>
{
    new ElectrodeDiameter
    {
        ElectrodeTypeId = migWireTypeId,
        Diameter = 0.762m, // 0.030"
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "system"
    },
    new ElectrodeDiameter
    {
        ElectrodeTypeId = migWireTypeId,
        Diameter = 0.889m, // 0.035"
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "system"
    },
    new ElectrodeDiameter
    {
        ElectrodeTypeId = migWireTypeId,
        Diameter = 1.143m, // 0.045"
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "system"
    }
};
```

## Diameter Selection Criteria

### Material Thickness
- **Thin Materials (< 3mm)**: Use smaller diameter electrodes
- **Medium Thickness (3-12mm)**: Use medium diameter electrodes
- **Thick Materials (> 12mm)**: Use larger diameter electrodes

### Welding Position
- **Flat Position**: Can use larger diameter electrodes
- **Vertical/Overhead**: Typically require smaller diameter electrodes
- **Out-of-Position**: Limited to specific diameter ranges

### Current Requirements
- **Low Current**: Smaller diameter electrodes
- **Medium Current**: Medium diameter electrodes
- **High Current**: Larger diameter electrodes

### Joint Configuration
- **Root Pass**: Typically smaller diameter electrodes
- **Fill Passes**: Medium to large diameter electrodes
- **Cap Pass**: Diameter selected for desired bead profile

## International Standards

### Metric Sizes (ISO/EN)
Common metric electrode diameters:
- **1.0 mm, 1.2 mm, 1.6 mm, 2.0 mm, 2.5 mm, 3.2 mm, 4.0 mm, 5.0 mm, 6.0 mm**

### Imperial Sizes (AWS/ASME)
Common imperial electrode diameters:
- **1/16", 5/64", 3/32", 1/8", 5/32", 3/16", 1/4"**

### Conversion Considerations
- **Dual Units**: Support both metric and imperial measurements
- **Conversion Accuracy**: Maintain precision during unit conversion
- **Regional Preferences**: Consider regional measurement preferences
- **Standard Compliance**: Ensure compliance with applicable standards

## Related Entities

- [ElectrodeType](ElectrodeType.md) - The electrode type enumeration
- [Electrode](Electrode.md) - Electrodes that use these diameters
- [Equipment](Equipment.md) - Equipment that may have diameter limitations

## Database Considerations

- The `Diameter` property has precision (6,3) for accurate measurements
- Index on `ElectrodeTypeId` for type-based queries
- Consider unique constraint on `ElectrodeTypeId` + `Diameter` combination
- Foreign key constraints should be properly configured
- Consider adding fields for unit of measure and standard references

## Quality and Standards

### Manufacturing Tolerances
- **Diameter Tolerance**: Typical ±0.05mm for precision electrodes
- **Roundness**: Electrodes should maintain circular cross-section
- **Surface Finish**: Smooth surface finish for consistent feeding
- **Straightness**: Especially important for wire electrodes

### Quality Control
- **Incoming Inspection**: Verify diameter specifications
- **Statistical Process Control**: Monitor diameter consistency
- **Calibration**: Regular calibration of measuring equipment
- **Documentation**: Maintain diameter certification records
