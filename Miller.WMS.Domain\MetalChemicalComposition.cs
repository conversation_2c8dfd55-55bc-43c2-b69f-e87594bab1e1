﻿namespace Miller.WMS.Domain;

public class MetalChemicalComposition : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }


    public decimal? MinCarbon { get; set; }
    public decimal? MaxCarbon { get; set; }
    public decimal? MinChromium { get; set; }
    public decimal? MaxChromium { get; set; }
    public decimal? MinMolybdenum { get; set; }
    public decimal? MaxMolybdenum { get; set; }
    public decimal? MinNickel { get; set; }
    public decimal? MaxNickel { get; set; }
    public decimal? MinManganese { get; set; }
    public decimal? MaxManganese { get; set; }
    public decimal? MinSilicon { get; set; }
    public decimal? MaxSilicon { get; set; }
}
