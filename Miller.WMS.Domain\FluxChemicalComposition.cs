﻿namespace Miller.WMS.Domain;

public class FluxChemicalComposition : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public required string Symbol { get; set; }        // max 20
    public required string Description { get; set; }   // max 255

    public ICollection<FluxChemicalCompositionLimits> Limits { get; set; } = new List<FluxChemicalCompositionLimits>();
}
