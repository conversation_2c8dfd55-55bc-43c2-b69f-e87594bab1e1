﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class CatElectrodeSpecialRuleConfiguration : IEntityTypeConfiguration<CatElectrodeSpecialRule>
{
    public void Configure(EntityTypeBuilder<CatElectrodeSpecialRule> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.WeldingArcDesignationSpec)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.MinimumYieldStrength)
               .HasPrecision(6, 3);

        builder.HasOne(e => e.Specification)
               .WithMany()
               .HasForeignKey(e => e.SpecificationId)
               .OnDelete(DeleteBehavior.SetNull);
    }
}