﻿namespace Miller.WMS.Domain;

public class FluxChemicalCompositionLimits : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid FluxChemicalCompositionId { get; set; }
    public required FluxChemicalComposition FluxChemicalComposition { get; set; }

    public required string ChemicalConstituents { get; set; } // max 255
    public decimal ConstituentLimitMin { get; set; }           // (5,4)
    public decimal ConstituentLimitMax { get; set; }           // (5,4)
}
