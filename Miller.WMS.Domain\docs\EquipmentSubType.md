# EquipmentSubType Enumeration

**Source File:** [EquipmentSubType.cs](../EquipmentSubType.cs)

## Overview
The `EquipmentSubType` enumeration provides more specific categorization of equipment within the broader equipment types. This detailed classification enables precise equipment management, maintenance planning, and resource allocation.

## Values

| Value | Description | Equipment Type | Primary Function |
|-------|-------------|----------------|------------------|
| `PowerSupply` | Welding power sources | Welding | Provide welding current and voltage |
| `Torch` | Welding torches and guns | Welding | Direct welding arc and filler metal |
| `Feeder` | Wire and electrode feeders | Welding | Feed consumable electrodes |
| `ContactTip` | Contact tips and consumables | Welding | Electrical contact for wire electrodes |
| `PreHeat` | Pre-heating equipment | Induction | Heat materials before welding |
| `PostHeat` | Post-weld heat treatment | Induction | Heat treatment after welding |
| `Cleaning` | Cleaning and preparation | Cleaning | Surface preparation and cleaning |
| `ToeTreatment` | Weld toe treatment equipment | Other | Improve weld toe geometry |
| `FumeExtraction` | Fume extraction systems | Safety | Remove welding fumes and gases |
| `GrindingSanding` | Grinding and sanding equipment | Machining | Surface finishing and preparation |
| `Filtering` | Filtration systems | Safety | Filter air and fluids |

## Equipment SubType Characteristics

### PowerSupply
- **Function**: Generate and control welding current and voltage
- **Equipment Examples**:
  - SMAW (Stick) power supplies
  - GTAW (TIG) power supplies
  - GMAW (MIG) power supplies
  - Multi-process power supplies
  - Plasma cutting power supplies
- **Key Features**:
  - Current and voltage control
  - Duty cycle ratings
  - Process-specific waveforms
  - Remote control capabilities
- **Applications**:
  - Manual welding operations
  - Semi-automatic welding
  - Automated welding systems
  - Cutting operations

### Torch
- **Function**: Direct welding arc and deliver filler metal
- **Equipment Examples**:
  - GTAW torches (air-cooled, water-cooled)
  - GMAW guns (push-pull, spool guns)
  - SMAW electrode holders
  - Plasma cutting torches
  - Robotic welding torches
- **Key Features**:
  - Gas delivery systems
  - Cooling systems
  - Ergonomic design
  - Consumable compatibility
- **Applications**:
  - Manual welding
  - Semi-automatic welding
  - Robotic welding
  - Cutting operations

### Feeder
- **Function**: Feed wire electrodes at controlled rates
- **Equipment Examples**:
  - Wire feeders for GMAW
  - Push-pull feeders
  - Spool guns
  - Robotic wire feeders
  - Flux-cored wire feeders
- **Key Features**:
  - Variable speed control
  - Wire tension control
  - Drive roll systems
  - Wire guides and liners
- **Applications**:
  - Continuous wire welding
  - Long-distance wire feeding
  - Automated welding systems
  - High-deposition welding

### ContactTip
- **Function**: Provide electrical contact for wire electrodes
- **Equipment Examples**:
  - Standard contact tips
  - Heavy-duty contact tips
  - Specialty alloy tips
  - Robotic contact tips
  - Quick-change tip systems
- **Key Features**:
  - Electrical conductivity
  - Wear resistance
  - Heat dissipation
  - Easy replacement
- **Applications**:
  - GMAW welding
  - FCAW welding
  - Robotic welding
  - High-current applications

### PreHeat
- **Function**: Heat materials before welding to improve weldability
- **Equipment Examples**:
  - Induction heating systems
  - Resistance heating systems
  - Gas-fired heaters
  - Ceramic heating pads
  - Infrared heaters
- **Key Features**:
  - Temperature control
  - Uniform heating
  - Energy efficiency
  - Safety controls
- **Applications**:
  - Thick section welding
  - High-carbon steel welding
  - Cold weather welding
  - Stress reduction

### PostHeat
- **Function**: Heat treatment after welding for stress relief
- **Equipment Examples**:
  - Post-weld heat treatment systems
  - Stress relief furnaces
  - Local heating systems
  - Temperature monitoring systems
- **Key Features**:
  - Precise temperature control
  - Uniform heating zones
  - Programmable controllers
  - Documentation systems
- **Applications**:
  - Pressure vessel welding
  - Structural welding
  - Code-required heat treatment
  - Stress relief operations

### Cleaning
- **Function**: Surface preparation and cleaning operations
- **Equipment Examples**:
  - Wire brushes and grinders
  - Chemical cleaning systems
  - Ultrasonic cleaners
  - Degreasing equipment
  - Shot blast equipment
- **Key Features**:
  - Effective contamination removal
  - Surface preparation capability
  - Environmental compliance
  - Safety features
- **Applications**:
  - Weld preparation
  - Inter-pass cleaning
  - Post-weld cleaning
  - General maintenance

### ToeTreatment
- **Function**: Improve weld toe geometry and fatigue life
- **Equipment Examples**:
  - TIG dressing equipment
  - Grinding and polishing tools
  - Peening equipment
  - Ultrasonic impact treatment
- **Key Features**:
  - Precise control
  - Consistent results
  - Fatigue improvement
  - Quality enhancement
- **Applications**:
  - Fatigue-critical welds
  - Structural welding
  - Pressure vessel welding
  - Quality improvement

### FumeExtraction
- **Function**: Remove welding fumes and protect workers
- **Equipment Examples**:
  - Local exhaust systems
  - Portable fume extractors
  - Central extraction systems
  - Downdraft tables
  - Crossdraft systems
- **Key Features**:
  - High airflow capacity
  - Filtration systems
  - Noise control
  - Energy efficiency
- **Applications**:
  - Welding operations
  - Cutting operations
  - Grinding operations
  - General air quality

### GrindingSanding
- **Function**: Surface finishing and material removal
- **Equipment Examples**:
  - Angle grinders
  - Belt sanders
  - Disc grinders
  - Pneumatic grinders
  - Robotic grinding systems
- **Key Features**:
  - Variable speed control
  - Dust collection
  - Ergonomic design
  - Safety guards
- **Applications**:
  - Weld finishing
  - Surface preparation
  - Defect removal
  - Dimensional correction

### Filtering
- **Function**: Filter air, gases, and fluids
- **Equipment Examples**:
  - Air filtration systems
  - Oil filtration systems
  - Water filtration systems
  - Gas purification systems
  - HEPA filter systems
- **Key Features**:
  - High filtration efficiency
  - Low pressure drop
  - Easy maintenance
  - Monitoring systems
- **Applications**:
  - Air quality control
  - Coolant filtration
  - Gas purification
  - Environmental protection

## Maintenance Requirements by SubType

### PowerSupply Maintenance
- **Electrical Systems**: Regular inspection of electrical connections
- **Cooling Systems**: Coolant system maintenance and replacement
- **Calibration**: Regular calibration of output parameters
- **Component Replacement**: Replacement of wear components

### Torch Maintenance
- **Consumable Replacement**: Regular replacement of tips, nozzles, cups
- **Gas System**: Gas line and fitting inspection
- **Cooling System**: Coolant flow and leak checks
- **Cable Inspection**: Power and control cable inspection

### Feeder Maintenance
- **Drive System**: Drive roll and motor maintenance
- **Wire Path**: Liner and guide maintenance
- **Tension System**: Wire tension adjustment and calibration
- **Electrical**: Control system and connection maintenance

## Safety Considerations by SubType

### PowerSupply Safety
- **Electrical Safety**: Proper grounding and electrical safety
- **Arc Flash**: Arc flash protection and procedures
- **Ventilation**: Adequate cooling and ventilation
- **Emergency Shutdown**: Emergency stop capabilities

### Torch Safety
- **Hot Surfaces**: Protection from hot torch components
- **Gas Safety**: Proper gas handling and leak detection
- **Ergonomics**: Proper torch handling and positioning
- **Maintenance Safety**: Safe maintenance procedures

### FumeExtraction Safety
- **Airflow Verification**: Regular airflow testing
- **Filter Maintenance**: Safe filter replacement procedures
- **Electrical Safety**: Electrical system safety
- **Fire Prevention**: Fire prevention in dust collection

## Related Entities

- [Equipment](Equipment.md) - Equipment entities with specific subtypes
- [EquipmentType](EquipmentType.md) - Broader equipment type categories
- [EquipmentStatus](EquipmentStatus.md) - Operational status of equipment
- [WorkCenter](WorkCenter.md) - Work centers containing different equipment subtypes

## Usage in Domain Model

The `EquipmentSubType` enumeration is used in:
- **Equipment entity**: Provides detailed equipment categorization
- **Maintenance planning**: Subtype-specific maintenance requirements
- **Inventory management**: Spare parts and consumables by subtype
- **Training programs**: Subtype-specific operator training

## Standards and Specifications

### Welding Equipment Standards
- **AWS**: Welding equipment specifications and requirements
- **IEC**: International electrical equipment standards
- **UL**: Safety standards for electrical equipment
- **CSA**: Canadian electrical safety standards

### Safety Standards
- **OSHA**: Occupational safety requirements
- **NFPA**: Fire safety and electrical standards
- **ANSI**: Equipment safety standards
- **ISO**: International safety standards

## Best Practices

### SubType Management
1. **Detailed Classification**: Use specific subtypes for accurate categorization
2. **Maintenance Programs**: Develop subtype-specific maintenance programs
3. **Spare Parts Management**: Organize spare parts by equipment subtype
4. **Training Specialization**: Provide subtype-specific training
5. **Performance Monitoring**: Track performance by equipment subtype

### Operational Efficiency
- **Standardization**: Standardize equipment within subtypes where possible
- **Interchangeability**: Consider interchangeability within subtypes
- **Upgrade Planning**: Plan upgrades by equipment subtype
- **Cost Management**: Track costs by equipment subtype
