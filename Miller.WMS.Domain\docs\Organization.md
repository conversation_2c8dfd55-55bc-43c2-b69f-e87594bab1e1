# Organization

**Source File:** [Organization.cs](../Organization.cs)

## Overview
The `Organization` entity represents companies, business entities, or organizational units within the Miller WMS system. Organizations serve as the top-level container for facilities, users, and business operations.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the organization |
| `Name` | `string` | Yes | Organization name |
| `IndustryType` | `OrganizationIndustryType?` | No | Type of industry the organization operates in |
| `Status` | `OrganizationStatus` | Yes | Current operational status of the organization |
| `CreatedAt` | `DateTime?` | No | Timestamp when the organization was created |
| `CreatedBy` | `Guid?` | No | User who created the organization record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the organization record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Facilities` | `ICollection<Facility>` | Collection of facilities owned by this organization |
| `Users` | `ICollection<User>` | Collection of users associated with this organization |

## Relationships

### One-to-Many Relationships
- **Organization → Facilities**: An organization can have multiple facilities
- **Organization → Users**: An organization can have multiple users

## Business Rules

1. **Name Uniqueness**: Organization names should be unique within the system
2. **Status Management**: Organizations can be activated, deactivated, or archived
3. **Cascade Operations**: When an organization is deleted, consider the impact on associated facilities and users
4. **Industry Classification**: Industry type helps categorize organizations for reporting and compliance

## Usage Examples

### Creating a New Organization
```csharp
var organization = new Organization
{
    Id = Guid.NewGuid(),
    Name = "Miller Electric Manufacturing Co.",
    IndustryType = OrganizationIndustryType.Manufacturing,
    Status = OrganizationStatus.Active,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "system"
};
```

### Organization with Facilities
```csharp
var organization = new Organization
{
    Name = "Acme Welding Corp",
    Status = OrganizationStatus.Active,
    Facilities = new List<Facility>
    {
        new Facility { Name = "Main Plant", Code = "MP001" },
        new Facility { Name = "Warehouse", Code = "WH001" }
    }
};
```

## Related Entities

- [Facility](Facility.md) - Physical locations owned by the organization
- [User](User.md) - Users associated with the organization
- [Customer](Customer.md) - External customers that may interact with the organization
- [Manufacturer](Manufacturer.md) - Equipment and material manufacturers

## Enumerations

### OrganizationIndustryType
Defines the industry classification for organizations (specific values defined in the enumeration).

### OrganizationStatus
Defines the operational status of organizations (specific values defined in the enumeration).

## Database Considerations

- The `Name` property should have appropriate length constraints
- Consider indexing on `Name` and `Status` for query performance
- Foreign key relationships should be properly configured for facilities and users
- Soft delete patterns may be preferred over hard deletes due to referential integrity
