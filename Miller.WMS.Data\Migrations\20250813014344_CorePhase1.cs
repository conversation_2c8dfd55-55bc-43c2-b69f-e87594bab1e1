﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Miller.WMS.Data.Migrations
{
    /// <inheritdoc />
    public partial class CorePhase1 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserFacilityRoles_Facilities_FacilityId",
                table: "UserFacilityRoles");

            migrationBuilder.DropForeignKey(
                name: "FK_UserFacilityRoles_Users_UserId",
                table: "UserFacilityRoles");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UserFacilityRoles",
                table: "UserFacilityRoles");

            migrationBuilder.DropColumn(
                name: "Role",
                table: "UserFacilityRoles");

            migrationBuilder.RenameTable(
                name: "UserFacilityRoles",
                newName: "UserFacilities");

            migrationBuilder.RenameIndex(
                name: "IX_UserFacilityRoles_FacilityId",
                table: "UserFacilities",
                newName: "IX_UserFacilities_FacilityId");

            migrationBuilder.AlterColumn<Guid>(
                name: "OrganizationId",
                table: "Users",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Users",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "Users",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "Users",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "Users",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "Users",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModifiedAt",
                table: "Users",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastModifiedBy",
                table: "Users",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Organizations",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "Organizations",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "Organizations",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "Organizations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "IndustryType",
                table: "Organizations",
                type: "integer",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModifiedAt",
                table: "Organizations",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastModifiedBy",
                table: "Organizations",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<int>(
                name: "Status",
                table: "Organizations",
                type: "integer",
                nullable: false,
                defaultValue: 0);

            migrationBuilder.AlterColumn<Guid>(
                name: "OrganizationId",
                table: "Facilities",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Facilities",
                type: "character varying(50)",
                maxLength: 50,
                nullable: false,
                oldClrType: typeof(string),
                oldType: "text");

            migrationBuilder.AlterColumn<Guid>(
                name: "Id",
                table: "Facilities",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer")
                .OldAnnotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AddColumn<string>(
                name: "Address",
                table: "Facilities",
                type: "character varying(255)",
                maxLength: 255,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Code",
                table: "Facilities",
                type: "character varying(50)",
                maxLength: 50,
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "Facilities",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "Facilities",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModifiedAt",
                table: "Facilities",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastModifiedBy",
                table: "Facilities",
                type: "text",
                nullable: true);

            migrationBuilder.AlterColumn<Guid>(
                name: "FacilityId",
                table: "UserFacilities",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AlterColumn<Guid>(
                name: "UserId",
                table: "UserFacilities",
                type: "uuid",
                nullable: false,
                oldClrType: typeof(int),
                oldType: "integer");

            migrationBuilder.AddColumn<DateTime>(
                name: "CreatedAt",
                table: "UserFacilities",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreatedBy",
                table: "UserFacilities",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "LastModifiedAt",
                table: "UserFacilities",
                type: "timestamp with time zone",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "LastModifiedBy",
                table: "UserFacilities",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "RoleAtFacility",
                table: "UserFacilities",
                type: "character varying(255)",
                maxLength: 255,
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserFacilities",
                table: "UserFacilities",
                columns: new[] { "UserId", "FacilityId" });

            migrationBuilder.CreateTable(
                name: "Customers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Code = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Customers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeDiameters",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ElectrodeTypeId = table.Column<Guid>(type: "uuid", nullable: false),
                    ElectrodeType = table.Column<int>(type: "integer", nullable: false),
                    Diameter = table.Column<decimal>(type: "numeric(6,3)", precision: 6, scale: 3, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeDiameters", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FacilityAreaLevelOnes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    FacilityId = table.Column<Guid>(type: "uuid", nullable: false),
                    FacilityAreaName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FacilityAreaLevelOnes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FacilityAreaLevelOnes_Facilities_FacilityId",
                        column: x => x.FacilityId,
                        principalTable: "Facilities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FluxChemicalComposition",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Symbol = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false),
                    Description = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FluxChemicalComposition", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "FluxClass",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FluxClass", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GasChemicalCompositions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    CarbonDioxide = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    Oxygen = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    Argon = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    Helium = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GasChemicalCompositions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "GasClassifications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Group = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false),
                    Subgroup = table.Column<string>(type: "character varying(5)", maxLength: 5, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_GasClassifications", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "IssuingOrganizations",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Abbreviation = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Website = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Description = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_IssuingOrganizations", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Manufacturers",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    IsActive = table.Column<bool>(type: "boolean", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Manufacturers", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Materials",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Spec = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    FulfilledById = table.Column<Guid>(type: "uuid", nullable: true),
                    MaterialForm = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    Hardness = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    MaterialDiameter = table.Column<decimal>(type: "numeric(6,3)", precision: 6, scale: 3, nullable: true),
                    MaterialThickness = table.Column<decimal>(type: "numeric(6,3)", precision: 6, scale: 3, nullable: true),
                    BaseMetalGroup = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    BaseMetalSubGroup = table.Column<string>(type: "character varying(10)", maxLength: 10, nullable: false),
                    CharpyRequirement = table.Column<bool>(type: "boolean", nullable: false),
                    WeldCarbonEquivalent = table.Column<decimal>(type: "numeric(5,2)", precision: 5, scale: 2, nullable: true),
                    WeldMinYieldStrength = table.Column<int>(type: "integer", nullable: true),
                    Class = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Grade = table.Column<string>(type: "character varying(50)", maxLength: 50, nullable: false),
                    Condition = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    HeatTreatment = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true),
                    ForgeMaterial = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Materials", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Materials_Materials_FulfilledById",
                        column: x => x.FulfilledById,
                        principalTable: "Materials",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "MetalChemicalCompositions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    MinCarbon = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxCarbon = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MinChromium = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxChromium = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MinMolybdenum = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxMolybdenum = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MinNickel = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxNickel = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MinManganese = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxManganese = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MinSilicon = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxSilicon = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MetalChemicalCompositions", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "SupplementalFillerMetals",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    TypeName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_SupplementalFillerMetals", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "TungstenElectrodeClassifications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Classification = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    PrincipalOxide = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    MinMass = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxMass = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    MaxImpurities = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: true),
                    Color = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TungstenElectrodeClassifications", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "Waveform",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Version = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Waveform", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "CustomerFacilities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    CustomerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Address = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    AddressAdditionalInformation = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    City = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    State = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ZipCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Country = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    CustomerId1 = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CustomerFacilities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CustomerFacilities_Customers_CustomerId",
                        column: x => x.CustomerId,
                        principalTable: "Customers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_CustomerFacilities_Customers_CustomerId1",
                        column: x => x.CustomerId1,
                        principalTable: "Customers",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "FacilityAreaLevelTwos",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    FacilityAreaLevelOneId = table.Column<Guid>(type: "uuid", nullable: false),
                    FacilityAreaName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FacilityAreaLevelTwos", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FacilityAreaLevelTwos_FacilityAreaLevelOnes_FacilityAreaLev~",
                        column: x => x.FacilityAreaLevelOneId,
                        principalTable: "FacilityAreaLevelOnes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FluxChemicalCompositionLimits",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    FluxChemicalCompositionId = table.Column<Guid>(type: "uuid", nullable: false),
                    ChemicalConstituents = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    ConstituentLimitMin = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: false),
                    ConstituentLimitMax = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FluxChemicalCompositionLimits", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FluxChemicalCompositionLimits_FluxChemicalComposition_FluxC~",
                        column: x => x.FluxChemicalCompositionId,
                        principalTable: "FluxChemicalComposition",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Fluxes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    TradeName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    IsDrying = table.Column<bool>(type: "boolean", nullable: false),
                    VendorRecommendationDryingFlux = table.Column<string>(type: "text", nullable: true),
                    IsPenetrationEnhanced = table.Column<bool>(type: "boolean", nullable: false),
                    FluxChemicalCompositionId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Fluxes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Fluxes_FluxChemicalComposition_FluxChemicalCompositionId",
                        column: x => x.FluxChemicalCompositionId,
                        principalTable: "FluxChemicalComposition",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Gases",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    GasClassificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    GasChemicalCompositionId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Gases", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Gases_GasChemicalCompositions_GasChemicalCompositionId",
                        column: x => x.GasChemicalCompositionId,
                        principalTable: "GasChemicalCompositions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Gases_GasClassifications_GasClassificationId",
                        column: x => x.GasClassificationId,
                        principalTable: "GasClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Specification",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    SpecificationType = table.Column<int>(type: "integer", nullable: false),
                    OtherSpecification = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    Code = table.Column<string>(type: "character varying(100)", maxLength: 100, nullable: false),
                    Title = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    IssuingOrganizationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    SupersededSpecId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Specification", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Specification_IssuingOrganizations_IssuingOrganizationId",
                        column: x => x.IssuingOrganizationId,
                        principalTable: "IssuingOrganizations",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_Specification_Specification_SupersededSpecId",
                        column: x => x.SupersededSpecId,
                        principalTable: "Specification",
                        principalColumn: "Id");
                });

            migrationBuilder.CreateTable(
                name: "Equipment",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ManufacturerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    SubType = table.Column<int>(type: "integer", nullable: false),
                    Model = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Equipment", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Equipment_Manufacturers_ManufacturerId",
                        column: x => x.ManufacturerId,
                        principalTable: "Manufacturers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ManufacturerFacilities",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ManufacturerId = table.Column<Guid>(type: "uuid", nullable: false),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Address = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    City = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    State = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true),
                    ZipCode = table.Column<string>(type: "character varying(20)", maxLength: 20, nullable: true),
                    Country = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ManufacturerFacilities", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ManufacturerFacilities_Manufacturers_ManufacturerId",
                        column: x => x.ManufacturerId,
                        principalTable: "Manufacturers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "Electrodes",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    TradeName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    MetalChemicalCompositionId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Electrodes", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Electrodes_MetalChemicalCompositions_MetalChemicalCompositi~",
                        column: x => x.MetalChemicalCompositionId,
                        principalTable: "MetalChemicalCompositions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FacilityAreaLevelThrees",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    FacilityAreaLevelTwoId = table.Column<Guid>(type: "uuid", nullable: false),
                    FacilityAreaName = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FacilityAreaLevelThrees", x => x.Id);
                    table.ForeignKey(
                        name: "FK_FacilityAreaLevelThrees_FacilityAreaLevelTwos_FacilityAreaL~",
                        column: x => x.FacilityAreaLevelTwoId,
                        principalTable: "FacilityAreaLevelTwos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "FluxHasFluxClass",
                columns: table => new
                {
                    FluxId = table.Column<Guid>(type: "uuid", nullable: false),
                    FluxClassId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_FluxHasFluxClass", x => new { x.FluxId, x.FluxClassId });
                    table.ForeignKey(
                        name: "FK_FluxHasFluxClass_FluxClass_FluxClassId",
                        column: x => x.FluxClassId,
                        principalTable: "FluxClass",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_FluxHasFluxClass_Fluxes_FluxId",
                        column: x => x.FluxId,
                        principalTable: "Fluxes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ANumberElectrode",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    SpecificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    MetalChemicalCompositionId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ANumberElectrode", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ANumberElectrode_MetalChemicalCompositions_MetalChemicalCom~",
                        column: x => x.MetalChemicalCompositionId,
                        principalTable: "MetalChemicalCompositions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ANumberElectrode_Specification_SpecificationId",
                        column: x => x.SpecificationId,
                        principalTable: "Specification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "CatElectrodeSpecialRules",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    WeldingArcDesignationSpec = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    WeldingProcessId = table.Column<Guid>(type: "uuid", nullable: false),
                    SpecificationId = table.Column<Guid>(type: "uuid", nullable: true),
                    MinimumYieldStrength = table.Column<decimal>(type: "numeric(6,3)", precision: 6, scale: 3, nullable: true),
                    WeldingProcess = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CatElectrodeSpecialRules", x => x.Id);
                    table.ForeignKey(
                        name: "FK_CatElectrodeSpecialRules_Specification_SpecificationId",
                        column: x => x.SpecificationId,
                        principalTable: "Specification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeClassifications",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    SpecificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    Classification = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    YieldStrength = table.Column<decimal>(type: "numeric(6,3)", precision: 6, scale: 3, nullable: false),
                    TensileStrength = table.Column<decimal>(type: "numeric(6,3)", precision: 6, scale: 3, nullable: false),
                    Elongation = table.Column<decimal>(type: "numeric(5,4)", precision: 5, scale: 4, nullable: false),
                    CoveringType = table.Column<int>(type: "integer", nullable: true),
                    TungstenElectrodeClassificationId = table.Column<Guid>(type: "uuid", nullable: true),
                    MetalChemicalCompositionId = table.Column<Guid>(type: "uuid", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeClassifications", x => x.Id);
                    table.ForeignKey(
                        name: "FK_ElectrodeClassifications_MetalChemicalCompositions_MetalChe~",
                        column: x => x.MetalChemicalCompositionId,
                        principalTable: "MetalChemicalCompositions",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ElectrodeClassifications_Specification_SpecificationId",
                        column: x => x.SpecificationId,
                        principalTable: "Specification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_ElectrodeClassifications_TungstenElectrodeClassifications_T~",
                        column: x => x.TungstenElectrodeClassificationId,
                        principalTable: "TungstenElectrodeClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "MaterialSubstitutions",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    SpecificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    MaterialId = table.Column<Guid>(type: "uuid", nullable: false),
                    SubstitutedById = table.Column<Guid>(type: "uuid", nullable: false),
                    ApprovalType = table.Column<int>(type: "integer", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_MaterialSubstitutions", x => x.Id);
                    table.ForeignKey(
                        name: "FK_MaterialSubstitutions_Materials_MaterialId",
                        column: x => x.MaterialId,
                        principalTable: "Materials",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MaterialSubstitutions_Materials_SubstitutedById",
                        column: x => x.SubstitutedById,
                        principalTable: "Materials",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_MaterialSubstitutions_Specification_SpecificationId",
                        column: x => x.SpecificationId,
                        principalTable: "Specification",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeMadeByManufacturers",
                columns: table => new
                {
                    ElectrodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    ManufacturerId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    ManufacturerFacilityId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeMadeByManufacturers", x => new { x.ElectrodeId, x.ManufacturerId });
                    table.ForeignKey(
                        name: "FK_ElectrodeMadeByManufacturers_Electrodes_ElectrodeId",
                        column: x => x.ElectrodeId,
                        principalTable: "Electrodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ElectrodeMadeByManufacturers_ManufacturerFacilities_Manufac~",
                        column: x => x.ManufacturerFacilityId,
                        principalTable: "ManufacturerFacilities",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_ElectrodeMadeByManufacturers_Manufacturers_ManufacturerId",
                        column: x => x.ManufacturerId,
                        principalTable: "Manufacturers",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkCenter",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true),
                    Name = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Description = table.Column<string>(type: "character varying(255)", maxLength: 255, nullable: false),
                    Type = table.Column<int>(type: "integer", nullable: false),
                    Status = table.Column<int>(type: "integer", nullable: false),
                    FacilityId = table.Column<Guid>(type: "uuid", nullable: false),
                    FacilityAreaLevelOneId = table.Column<Guid>(type: "uuid", nullable: true),
                    FacilityAreaLevelTwoId = table.Column<Guid>(type: "uuid", nullable: true),
                    FacilityAreaLevelThreeId = table.Column<Guid>(type: "uuid", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCenter", x => x.Id);
                    table.ForeignKey(
                        name: "FK_WorkCenter_Facilities_FacilityId",
                        column: x => x.FacilityId,
                        principalTable: "Facilities",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkCenter_FacilityAreaLevelOnes_FacilityAreaLevelOneId",
                        column: x => x.FacilityAreaLevelOneId,
                        principalTable: "FacilityAreaLevelOnes",
                        principalColumn: "Id");
                    table.ForeignKey(
                        name: "FK_WorkCenter_FacilityAreaLevelThrees_FacilityAreaLevelThreeId",
                        column: x => x.FacilityAreaLevelThreeId,
                        principalTable: "FacilityAreaLevelThrees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                    table.ForeignKey(
                        name: "FK_WorkCenter_FacilityAreaLevelTwos_FacilityAreaLevelTwoId",
                        column: x => x.FacilityAreaLevelTwoId,
                        principalTable: "FacilityAreaLevelTwos",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.SetNull);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeClassificationCurrentTypes",
                columns: table => new
                {
                    ElectrodeClassificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    CurrentType = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeClassificationCurrentTypes", x => new { x.ElectrodeClassificationId, x.CurrentType });
                    table.ForeignKey(
                        name: "FK_ElectrodeClassificationCurrentTypes_ElectrodeClassification~",
                        column: x => x.ElectrodeClassificationId,
                        principalTable: "ElectrodeClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeClassificationWeldingPositions",
                columns: table => new
                {
                    ElectrodeClassificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    WeldingPosition = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeClassificationWeldingPositions", x => new { x.ElectrodeClassificationId, x.WeldingPosition });
                    table.ForeignKey(
                        name: "FK_ElectrodeClassificationWeldingPositions_ElectrodeClassifica~",
                        column: x => x.ElectrodeClassificationId,
                        principalTable: "ElectrodeClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeClassificationWeldingProcesses",
                columns: table => new
                {
                    ElectrodeClassificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    WeldingProcess = table.Column<int>(type: "integer", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeClassificationWeldingProcesses", x => new { x.ElectrodeClassificationId, x.WeldingProcess });
                    table.ForeignKey(
                        name: "FK_ElectrodeClassificationWeldingProcesses_ElectrodeClassifica~",
                        column: x => x.ElectrodeClassificationId,
                        principalTable: "ElectrodeClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "ElectrodeHasElectrodeClassifications",
                columns: table => new
                {
                    ElectrodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    ElectrodeClassificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_ElectrodeHasElectrodeClassifications", x => new { x.ElectrodeId, x.ElectrodeClassificationId });
                    table.ForeignKey(
                        name: "FK_ElectrodeHasElectrodeClassifications_ElectrodeClassificatio~",
                        column: x => x.ElectrodeClassificationId,
                        principalTable: "ElectrodeClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_ElectrodeHasElectrodeClassifications_Electrodes_ElectrodeId",
                        column: x => x.ElectrodeId,
                        principalTable: "Electrodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "EquipmentHasCurrentTypes",
                columns: table => new
                {
                    EquipmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    ElectrodeClassificationId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_EquipmentHasCurrentTypes", x => new { x.EquipmentId, x.ElectrodeClassificationId });
                    table.ForeignKey(
                        name: "FK_EquipmentHasCurrentTypes_ElectrodeClassifications_Electrode~",
                        column: x => x.ElectrodeClassificationId,
                        principalTable: "ElectrodeClassifications",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_EquipmentHasCurrentTypes_Equipment_EquipmentId",
                        column: x => x.EquipmentId,
                        principalTable: "Equipment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkCenterHasElectrode",
                columns: table => new
                {
                    WorkCenterId = table.Column<Guid>(type: "uuid", nullable: false),
                    ElectrodeId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCenterHasElectrode", x => new { x.WorkCenterId, x.ElectrodeId });
                    table.ForeignKey(
                        name: "FK_WorkCenterHasElectrode_Electrodes_ElectrodeId",
                        column: x => x.ElectrodeId,
                        principalTable: "Electrodes",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkCenterHasElectrode_WorkCenter_WorkCenterId",
                        column: x => x.WorkCenterId,
                        principalTable: "WorkCenter",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkCenterHasEquipment",
                columns: table => new
                {
                    WorkCenterId = table.Column<Guid>(type: "uuid", nullable: false),
                    EquipmentId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCenterHasEquipment", x => new { x.WorkCenterId, x.EquipmentId });
                    table.ForeignKey(
                        name: "FK_WorkCenterHasEquipment_Equipment_EquipmentId",
                        column: x => x.EquipmentId,
                        principalTable: "Equipment",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkCenterHasEquipment_WorkCenter_WorkCenterId",
                        column: x => x.WorkCenterId,
                        principalTable: "WorkCenter",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkCenterHasGas",
                columns: table => new
                {
                    WorkCenterId = table.Column<Guid>(type: "uuid", nullable: false),
                    GasId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCenterHasGas", x => new { x.WorkCenterId, x.GasId });
                    table.ForeignKey(
                        name: "FK_WorkCenterHasGas_Gases_GasId",
                        column: x => x.GasId,
                        principalTable: "Gases",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkCenterHasGas_WorkCenter_WorkCenterId",
                        column: x => x.WorkCenterId,
                        principalTable: "WorkCenter",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "WorkCenterHasWaveform",
                columns: table => new
                {
                    WorkCenterId = table.Column<Guid>(type: "uuid", nullable: false),
                    WaveformId = table.Column<Guid>(type: "uuid", nullable: false),
                    CreatedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    CreatedBy = table.Column<string>(type: "text", nullable: true),
                    LastModifiedAt = table.Column<DateTime>(type: "timestamp with time zone", nullable: true),
                    LastModifiedBy = table.Column<string>(type: "text", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_WorkCenterHasWaveform", x => new { x.WorkCenterId, x.WaveformId });
                    table.ForeignKey(
                        name: "FK_WorkCenterHasWaveform_Waveform_WaveformId",
                        column: x => x.WaveformId,
                        principalTable: "Waveform",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_WorkCenterHasWaveform_WorkCenter_WorkCenterId",
                        column: x => x.WorkCenterId,
                        principalTable: "WorkCenter",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_ANumberElectrode_MetalChemicalCompositionId",
                table: "ANumberElectrode",
                column: "MetalChemicalCompositionId");

            migrationBuilder.CreateIndex(
                name: "IX_ANumberElectrode_SpecificationId",
                table: "ANumberElectrode",
                column: "SpecificationId");

            migrationBuilder.CreateIndex(
                name: "IX_CatElectrodeSpecialRules_SpecificationId",
                table: "CatElectrodeSpecialRules",
                column: "SpecificationId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFacilities_CustomerId",
                table: "CustomerFacilities",
                column: "CustomerId");

            migrationBuilder.CreateIndex(
                name: "IX_CustomerFacilities_CustomerId1",
                table: "CustomerFacilities",
                column: "CustomerId1");

            migrationBuilder.CreateIndex(
                name: "IX_Customers_Code",
                table: "Customers",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeClassificationCurrentTypes_CurrentType",
                table: "ElectrodeClassificationCurrentTypes",
                column: "CurrentType");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeClassifications_MetalChemicalCompositionId",
                table: "ElectrodeClassifications",
                column: "MetalChemicalCompositionId");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeClassifications_SpecificationId",
                table: "ElectrodeClassifications",
                column: "SpecificationId");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeClassifications_TungstenElectrodeClassificationId",
                table: "ElectrodeClassifications",
                column: "TungstenElectrodeClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeClassificationWeldingProcesses_WeldingProcess",
                table: "ElectrodeClassificationWeldingProcesses",
                column: "WeldingProcess");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeHasElectrodeClassifications_ElectrodeClassificatio~",
                table: "ElectrodeHasElectrodeClassifications",
                column: "ElectrodeClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeMadeByManufacturers_ManufacturerFacilityId",
                table: "ElectrodeMadeByManufacturers",
                column: "ManufacturerFacilityId");

            migrationBuilder.CreateIndex(
                name: "IX_ElectrodeMadeByManufacturers_ManufacturerId",
                table: "ElectrodeMadeByManufacturers",
                column: "ManufacturerId");

            migrationBuilder.CreateIndex(
                name: "IX_Electrodes_MetalChemicalCompositionId",
                table: "Electrodes",
                column: "MetalChemicalCompositionId");

            migrationBuilder.CreateIndex(
                name: "IX_Equipment_ManufacturerId",
                table: "Equipment",
                column: "ManufacturerId");

            migrationBuilder.CreateIndex(
                name: "IX_EquipmentHasCurrentTypes_ElectrodeClassificationId",
                table: "EquipmentHasCurrentTypes",
                column: "ElectrodeClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_FacilityAreaLevelOnes_FacilityId",
                table: "FacilityAreaLevelOnes",
                column: "FacilityId");

            migrationBuilder.CreateIndex(
                name: "IX_FacilityAreaLevelThrees_FacilityAreaLevelTwoId",
                table: "FacilityAreaLevelThrees",
                column: "FacilityAreaLevelTwoId");

            migrationBuilder.CreateIndex(
                name: "IX_FacilityAreaLevelTwos_FacilityAreaLevelOneId",
                table: "FacilityAreaLevelTwos",
                column: "FacilityAreaLevelOneId");

            migrationBuilder.CreateIndex(
                name: "IX_FluxChemicalCompositionLimits_FluxChemicalCompositionId",
                table: "FluxChemicalCompositionLimits",
                column: "FluxChemicalCompositionId");

            migrationBuilder.CreateIndex(
                name: "IX_Fluxes_FluxChemicalCompositionId",
                table: "Fluxes",
                column: "FluxChemicalCompositionId");

            migrationBuilder.CreateIndex(
                name: "IX_FluxHasFluxClass_FluxClassId",
                table: "FluxHasFluxClass",
                column: "FluxClassId");

            migrationBuilder.CreateIndex(
                name: "IX_Gases_GasChemicalCompositionId",
                table: "Gases",
                column: "GasChemicalCompositionId");

            migrationBuilder.CreateIndex(
                name: "IX_Gases_GasClassificationId",
                table: "Gases",
                column: "GasClassificationId");

            migrationBuilder.CreateIndex(
                name: "IX_ManufacturerFacilities_ManufacturerId",
                table: "ManufacturerFacilities",
                column: "ManufacturerId");

            migrationBuilder.CreateIndex(
                name: "IX_Materials_FulfilledById",
                table: "Materials",
                column: "FulfilledById");

            migrationBuilder.CreateIndex(
                name: "IX_MaterialSubstitutions_MaterialId",
                table: "MaterialSubstitutions",
                column: "MaterialId");

            migrationBuilder.CreateIndex(
                name: "IX_MaterialSubstitutions_SpecificationId",
                table: "MaterialSubstitutions",
                column: "SpecificationId");

            migrationBuilder.CreateIndex(
                name: "IX_MaterialSubstitutions_SubstitutedById",
                table: "MaterialSubstitutions",
                column: "SubstitutedById");

            migrationBuilder.CreateIndex(
                name: "IX_Specification_IssuingOrganizationId",
                table: "Specification",
                column: "IssuingOrganizationId");

            migrationBuilder.CreateIndex(
                name: "IX_Specification_SupersededSpecId",
                table: "Specification",
                column: "SupersededSpecId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenter_FacilityAreaLevelOneId",
                table: "WorkCenter",
                column: "FacilityAreaLevelOneId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenter_FacilityAreaLevelThreeId",
                table: "WorkCenter",
                column: "FacilityAreaLevelThreeId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenter_FacilityAreaLevelTwoId",
                table: "WorkCenter",
                column: "FacilityAreaLevelTwoId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenter_FacilityId",
                table: "WorkCenter",
                column: "FacilityId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenterHasElectrode_ElectrodeId",
                table: "WorkCenterHasElectrode",
                column: "ElectrodeId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenterHasEquipment_EquipmentId",
                table: "WorkCenterHasEquipment",
                column: "EquipmentId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenterHasGas_GasId",
                table: "WorkCenterHasGas",
                column: "GasId");

            migrationBuilder.CreateIndex(
                name: "IX_WorkCenterHasWaveform_WaveformId",
                table: "WorkCenterHasWaveform",
                column: "WaveformId");

            migrationBuilder.AddForeignKey(
                name: "FK_UserFacilities_Facilities_FacilityId",
                table: "UserFacilities",
                column: "FacilityId",
                principalTable: "Facilities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserFacilities_Users_UserId",
                table: "UserFacilities",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_UserFacilities_Facilities_FacilityId",
                table: "UserFacilities");

            migrationBuilder.DropForeignKey(
                name: "FK_UserFacilities_Users_UserId",
                table: "UserFacilities");

            migrationBuilder.DropTable(
                name: "ANumberElectrode");

            migrationBuilder.DropTable(
                name: "CatElectrodeSpecialRules");

            migrationBuilder.DropTable(
                name: "CustomerFacilities");

            migrationBuilder.DropTable(
                name: "ElectrodeClassificationCurrentTypes");

            migrationBuilder.DropTable(
                name: "ElectrodeClassificationWeldingPositions");

            migrationBuilder.DropTable(
                name: "ElectrodeClassificationWeldingProcesses");

            migrationBuilder.DropTable(
                name: "ElectrodeDiameters");

            migrationBuilder.DropTable(
                name: "ElectrodeHasElectrodeClassifications");

            migrationBuilder.DropTable(
                name: "ElectrodeMadeByManufacturers");

            migrationBuilder.DropTable(
                name: "EquipmentHasCurrentTypes");

            migrationBuilder.DropTable(
                name: "FluxChemicalCompositionLimits");

            migrationBuilder.DropTable(
                name: "FluxHasFluxClass");

            migrationBuilder.DropTable(
                name: "MaterialSubstitutions");

            migrationBuilder.DropTable(
                name: "SupplementalFillerMetals");

            migrationBuilder.DropTable(
                name: "WorkCenterHasElectrode");

            migrationBuilder.DropTable(
                name: "WorkCenterHasEquipment");

            migrationBuilder.DropTable(
                name: "WorkCenterHasGas");

            migrationBuilder.DropTable(
                name: "WorkCenterHasWaveform");

            migrationBuilder.DropTable(
                name: "Customers");

            migrationBuilder.DropTable(
                name: "ManufacturerFacilities");

            migrationBuilder.DropTable(
                name: "ElectrodeClassifications");

            migrationBuilder.DropTable(
                name: "FluxClass");

            migrationBuilder.DropTable(
                name: "Fluxes");

            migrationBuilder.DropTable(
                name: "Materials");

            migrationBuilder.DropTable(
                name: "Electrodes");

            migrationBuilder.DropTable(
                name: "Equipment");

            migrationBuilder.DropTable(
                name: "Gases");

            migrationBuilder.DropTable(
                name: "Waveform");

            migrationBuilder.DropTable(
                name: "WorkCenter");

            migrationBuilder.DropTable(
                name: "Specification");

            migrationBuilder.DropTable(
                name: "TungstenElectrodeClassifications");

            migrationBuilder.DropTable(
                name: "FluxChemicalComposition");

            migrationBuilder.DropTable(
                name: "MetalChemicalCompositions");

            migrationBuilder.DropTable(
                name: "Manufacturers");

            migrationBuilder.DropTable(
                name: "GasChemicalCompositions");

            migrationBuilder.DropTable(
                name: "GasClassifications");

            migrationBuilder.DropTable(
                name: "FacilityAreaLevelThrees");

            migrationBuilder.DropTable(
                name: "IssuingOrganizations");

            migrationBuilder.DropTable(
                name: "FacilityAreaLevelTwos");

            migrationBuilder.DropTable(
                name: "FacilityAreaLevelOnes");

            migrationBuilder.DropPrimaryKey(
                name: "PK_UserFacilities",
                table: "UserFacilities");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "LastModifiedAt",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "LastModifiedBy",
                table: "Users");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "Organizations");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Organizations");

            migrationBuilder.DropColumn(
                name: "IndustryType",
                table: "Organizations");

            migrationBuilder.DropColumn(
                name: "LastModifiedAt",
                table: "Organizations");

            migrationBuilder.DropColumn(
                name: "LastModifiedBy",
                table: "Organizations");

            migrationBuilder.DropColumn(
                name: "Status",
                table: "Organizations");

            migrationBuilder.DropColumn(
                name: "Address",
                table: "Facilities");

            migrationBuilder.DropColumn(
                name: "Code",
                table: "Facilities");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "Facilities");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "Facilities");

            migrationBuilder.DropColumn(
                name: "LastModifiedAt",
                table: "Facilities");

            migrationBuilder.DropColumn(
                name: "LastModifiedBy",
                table: "Facilities");

            migrationBuilder.DropColumn(
                name: "CreatedAt",
                table: "UserFacilities");

            migrationBuilder.DropColumn(
                name: "CreatedBy",
                table: "UserFacilities");

            migrationBuilder.DropColumn(
                name: "LastModifiedAt",
                table: "UserFacilities");

            migrationBuilder.DropColumn(
                name: "LastModifiedBy",
                table: "UserFacilities");

            migrationBuilder.DropColumn(
                name: "RoleAtFacility",
                table: "UserFacilities");

            migrationBuilder.RenameTable(
                name: "UserFacilities",
                newName: "UserFacilityRoles");

            migrationBuilder.RenameIndex(
                name: "IX_UserFacilities_FacilityId",
                table: "UserFacilityRoles",
                newName: "IX_UserFacilityRoles_FacilityId");

            migrationBuilder.AlterColumn<int>(
                name: "OrganizationId",
                table: "Users",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Users",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<string>(
                name: "Email",
                table: "Users",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "Users",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Organizations",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(255)",
                oldMaxLength: 255);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "Organizations",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AlterColumn<int>(
                name: "OrganizationId",
                table: "Facilities",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<string>(
                name: "Name",
                table: "Facilities",
                type: "text",
                nullable: false,
                oldClrType: typeof(string),
                oldType: "character varying(50)",
                oldMaxLength: 50);

            migrationBuilder.AlterColumn<int>(
                name: "Id",
                table: "Facilities",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid")
                .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn);

            migrationBuilder.AlterColumn<int>(
                name: "FacilityId",
                table: "UserFacilityRoles",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AlterColumn<int>(
                name: "UserId",
                table: "UserFacilityRoles",
                type: "integer",
                nullable: false,
                oldClrType: typeof(Guid),
                oldType: "uuid");

            migrationBuilder.AddColumn<string>(
                name: "Role",
                table: "UserFacilityRoles",
                type: "text",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddPrimaryKey(
                name: "PK_UserFacilityRoles",
                table: "UserFacilityRoles",
                columns: new[] { "UserId", "FacilityId" });

            migrationBuilder.AddForeignKey(
                name: "FK_UserFacilityRoles_Facilities_FacilityId",
                table: "UserFacilityRoles",
                column: "FacilityId",
                principalTable: "Facilities",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_UserFacilityRoles_Users_UserId",
                table: "UserFacilityRoles",
                column: "UserId",
                principalTable: "Users",
                principalColumn: "Id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
