# CurrentType Enumeration

**Source File:** [CurrentType.cs](../CurrentType.cs)

## Overview
The `CurrentType` enumeration defines the types of electrical current used in welding operations. This enumeration is fundamental to welding process control and electrode compatibility.

## Values

| Value | Description | Polarity | Applications |
|-------|-------------|----------|--------------|
| `DCEN` | Direct Current Electrode Negative | Electrode (-), Work (+) | Deep penetration, root passes, thin materials |
| `DCEP` | Direct Current Electrode Positive | Electrode (+), Work (-) | Cleaning action, aluminum welding, shallow penetration |
| `AC` | Alternating Current | Alternating polarity | Balanced heating, aluminum TIG, stick welding |

## Current Type Characteristics

### DCEN (Direct Current Electrode Negative)
- **Polarity**: Electrode connected to negative terminal, work to positive
- **Heat Distribution**: 70% heat at work, 30% at electrode
- **Penetration**: Deep, narrow penetration profile
- **Arc Characteristics**: Stable arc, good directional control
- **Applications**: 
  - Root passes in pipe welding
  - Thin material welding
  - Steel TIG welding
  - Most stick electrode welding

### DCEP (Direct Current Electrode Positive)
- **Polarity**: Electrode connected to positive terminal, work to negative
- **Heat Distribution**: 30% heat at work, 70% at electrode
- **Penetration**: Shallow, wide penetration profile
- **Arc Characteristics**: Cleaning action, oxide removal
- **Applications**:
  - Aluminum and magnesium welding
  - Stainless steel welding (some applications)
  - Electrodes requiring cleaning action
  - Flux-cored welding

### AC (Alternating Current)
- **Polarity**: Alternates between positive and negative
- **Heat Distribution**: Balanced between electrode and work
- **Penetration**: Medium penetration, balanced profile
- **Arc Characteristics**: Requires arc stabilization, cleaning action
- **Applications**:
  - Aluminum TIG welding (primary choice)
  - Stick welding with AC-capable electrodes
  - Situations requiring balanced heating
  - Magnetic arc blow mitigation

## Welding Process Applications

### SMAW (Stick Welding)
- **DCEP**: Most common for stick electrodes (E6010, E7018, etc.)
- **AC**: Alternative for some electrodes, reduces arc blow
- **DCEN**: Rarely used, limited to specific applications

### GTAW (TIG Welding)
- **DCEN**: Primary choice for steel and stainless steel
- **AC**: Primary choice for aluminum and magnesium
- **DCEP**: Rarely used due to excessive electrode consumption

### GMAW (MIG Welding)
- **DCEP**: Standard for most MIG welding applications
- **DCEN**: Special applications with specific wire types
- **AC**: Not commonly used in conventional MIG welding

### FCAW (Flux-Cored Welding)
- **DCEP**: Most common for gas-shielded flux-cored
- **DCEN**: Some self-shielded flux-cored wires
- **AC**: Limited applications

## Material Compatibility

### Carbon Steel
- **Primary**: DCEN or DCEP depending on process and electrode
- **Characteristics**: Good penetration and mechanical properties
- **Considerations**: Choose based on electrode requirements

### Stainless Steel
- **Primary**: DCEN for TIG, DCEP for stick and MIG
- **Characteristics**: Good corrosion resistance, controlled heat input
- **Considerations**: Minimize heat input to prevent sensitization

### Aluminum
- **Primary**: AC for TIG welding
- **Alternative**: DCEP for stick welding (rare)
- **Characteristics**: Cleaning action removes oxide layer
- **Considerations**: AC provides balanced heating and cleaning

### Magnesium
- **Primary**: AC for TIG welding
- **Characteristics**: Similar to aluminum, requires cleaning action
- **Considerations**: Fire safety precautions required

## Equipment Requirements

### Power Supply Capabilities
- **DC Only**: Can provide DCEN and DCEP
- **AC Only**: Limited to AC applications
- **AC/DC**: Most versatile, supports all current types

### Arc Stabilization
- **DC**: Generally stable, minimal stabilization needed
- **AC**: Requires high-frequency stabilization or other arc starting aids
- **Square Wave AC**: Advanced AC control for improved performance

### Polarity Switching
- **Manual**: Manual polarity switches on power supply
- **Remote**: Remote polarity control for convenience
- **Automatic**: Some advanced systems can switch automatically

## Quality Considerations

### Penetration Control
- **DCEN**: Deep penetration, good for thick materials
- **DCEP**: Shallow penetration, good for thin materials
- **AC**: Balanced penetration, good for medium thickness

### Bead Appearance
- **DCEN**: Narrow, high-crown beads
- **DCEP**: Wide, flat beads with good tie-in
- **AC**: Medium width beads with good appearance

### Defect Prevention
- **Arc Blow**: AC can help reduce magnetic arc blow
- **Porosity**: Proper current type selection reduces porosity
- **Lack of Fusion**: Appropriate current type ensures good fusion

## Safety Considerations

### Electrical Safety
- **DC Systems**: Generally safer, lower shock risk
- **AC Systems**: Higher shock risk, requires more safety precautions
- **Grounding**: Proper grounding essential for all current types

### Arc Characteristics
- **UV Radiation**: All current types produce harmful UV radiation
- **Spatter**: Current type affects spatter generation
- **Fume Generation**: Current type can affect fume characteristics

## Standards and Codes

### AWS Standards
- **AWS A5 Series**: Specify current types for different electrodes
- **AWS D1.1**: Structural welding current type requirements
- **AWS B2.1**: Procedure qualification current type specifications

### ASME Standards
- **ASME Section IX**: Procedure qualification current type requirements
- **Essential Variables**: Current type changes may require requalification

## Troubleshooting

### Common Issues by Current Type

#### DCEN Issues
- **Insufficient Penetration**: Check amperage settings
- **Arc Instability**: Verify electrode condition and technique
- **Porosity**: Check shielding gas or electrode condition

#### DCEP Issues
- **Excessive Electrode Consumption**: Normal for DCEP, check amperage
- **Shallow Penetration**: Expected characteristic, adjust technique
- **Cleaning Action**: Verify cleaning action is occurring

#### AC Issues
- **Arc Starting Problems**: Check high-frequency unit operation
- **Arc Instability**: Verify AC balance and frequency settings
- **Inconsistent Penetration**: Check AC waveform characteristics

## Related Entities

- [ElectrodeClassificationHasCurrentType](ElectrodeClassificationHasCurrentType.md) - Current type compatibility with electrode classifications
- [Equipment](Equipment.md) - Equipment that must support specific current types
- [WeldingProcess](WeldingProcess.md) - Welding processes using different current types
- [ElectrodeClassification](ElectrodeClassification.md) - Electrode classifications requiring specific current types

## Usage in Domain Model

The `CurrentType` enumeration is used in:
- **ElectrodeClassificationHasCurrentType**: Defines which current types are compatible with electrode classifications
- **Equipment specifications**: Defines current capabilities of welding equipment
- **Welding procedures**: Specifies required current type for welding procedures
- **Quality control**: Ensures proper current type selection for applications

## Best Practices

### Current Type Selection
1. **Material Compatibility**: Choose current type based on base material
2. **Process Requirements**: Consider welding process requirements
3. **Electrode Compatibility**: Ensure electrode supports chosen current type
4. **Equipment Capability**: Verify equipment can provide required current type
5. **Quality Requirements**: Consider quality and appearance requirements

### Documentation
- **Procedure Specifications**: Document required current type in WPS
- **Qualification Records**: Record current type used in qualification testing
- **Work Instructions**: Include current type in welding work instructions
- **Training Materials**: Include current type selection in training programs
