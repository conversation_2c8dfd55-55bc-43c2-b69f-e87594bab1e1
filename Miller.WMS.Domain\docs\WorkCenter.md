# WorkCenter

**Source File:** [WorkCenter.cs](../WorkCenter.cs)

## Overview
The `WorkCenter` entity represents physical locations within facilities where welding operations and work activities take place. Work centers contain equipment, materials, and define the capabilities available at specific locations.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the work center |
| `Name` | `string` | Yes | Work center name |
| `Description` | `string` | Yes | Detailed description of the work center |
| `Type` | `WorkCenterType` | Yes | Category/type of work center |
| `Status` | `WorkCenterStatus` | Yes | Current operational status |
| `FacilityId` | `Guid` | Yes | Foreign key to the containing facility |
| `FacilityAreaLevelOneId` | `Guid?` | No | Optional level-one area assignment |
| `FacilityAreaLevelTwoId` | `Guid?` | No | Optional level-two area assignment |
| `FacilityAreaLevelThreeId` | `Guid?` | No | Optional level-three area assignment |
| `CreatedAt` | `DateTime?` | No | Timestamp when the work center was created |
| `CreatedBy` | `Guid?` | No | User who created the work center record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the work center record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Facility` | `Facility` | The facility containing this work center |
| `FacilityAreaLevelOne` | `FacilityAreaLevelOne?` | Optional level-one area location |
| `FacilityAreaLevelTwo` | `FacilityAreaLevelTwo?` | Optional level-two area location |
| `FacilityAreaLevelThree` | `FacilityAreaLevelThree?` | Optional level-three area location |

## Relationships

### Many-to-One Relationships
- **WorkCenter → Facility**: Each work center belongs to one facility
- **WorkCenter → FacilityAreaLevelOne**: Optional hierarchical location
- **WorkCenter → FacilityAreaLevelTwo**: Optional hierarchical location  
- **WorkCenter → FacilityAreaLevelThree**: Optional hierarchical location

### Many-to-Many Relationships (via junction entities)
- **WorkCenter ↔ Equipment**: Work centers can have multiple pieces of equipment
- **WorkCenter ↔ Electrode**: Work centers can have access to multiple electrodes
- **WorkCenter ↔ Gas**: Work centers can have access to multiple gases
- **WorkCenter ↔ Waveform**: Work centers can support multiple waveforms

## Hierarchical Location

Work centers can be precisely located within facilities using a three-level hierarchy:

```
Facility
├── Level One Area (e.g., "Production Floor")
    ├── Level Two Area (e.g., "Welding Bay A")
        ├── Level Three Area (e.g., "Station 1")
            └── Work Center
```

This hierarchy allows for:
- Precise location tracking
- Organizational reporting
- Resource allocation planning
- Maintenance scheduling

## Work Center Capabilities

Work centers define their capabilities through associated resources:

### Equipment Capabilities
- **Power Supplies**: Available welding power sources
- **Torches**: Available welding torches and guns
- **Feeders**: Wire and electrode feeding equipment
- **Safety Equipment**: Fume extraction, ventilation
- **Support Equipment**: Fixtures, positioners, etc.

### Material Capabilities
- **Electrodes**: Available electrode types and classifications
- **Gases**: Available shielding and process gases
- **Consumables**: Contact tips, nozzles, etc.

### Process Capabilities
- **Waveforms**: Supported welding waveforms
- **Processes**: Supported welding processes (GTAW, GMAW, etc.)
- **Positions**: Supported welding positions

## Business Rules

1. **Facility Association**: Every work center must belong to a facility
2. **Hierarchical Consistency**: Area assignments must be hierarchically consistent
3. **Resource Management**: Equipment and materials must be properly assigned
4. **Status Management**: Work center status should reflect operational capability
5. **Capacity Planning**: Work centers should have defined capacity limits

## Usage Examples

### Creating a Basic Work Center
```csharp
var workCenter = new WorkCenter
{
    Id = Guid.NewGuid(),
    Name = "TIG Welding Station 1",
    Description = "Precision TIG welding station for aerospace components",
    Type = WorkCenterType.Welding,
    Status = WorkCenterStatus.Active,
    FacilityId = facilityId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Work Center with Hierarchical Location
```csharp
var workCenter = new WorkCenter
{
    Name = "Automated Welding Cell",
    Description = "Robotic welding cell for high-volume production",
    Type = WorkCenterType.Automated,
    Status = WorkCenterStatus.Active,
    FacilityId = facilityId,
    FacilityAreaLevelOneId = productionFloorId,
    FacilityAreaLevelTwoId = weldingBayId,
    FacilityAreaLevelThreeId = cellAreaId
};
```

## Work Center Types and Status

### WorkCenterType Enumeration
Common work center types include:
- **Manual**: Manual welding operations
- **Semi-Automated**: Semi-automated welding
- **Automated**: Fully automated welding
- **Inspection**: Quality control and inspection
- **Preparation**: Material preparation and setup
- **Assembly**: Component assembly operations

### WorkCenterStatus Enumeration
- **Active**: Work center is operational
- **Inactive**: Temporarily out of service
- **Maintenance**: Under maintenance
- **Setup**: Being configured for new work
- **Retired**: Permanently out of service

## Related Entities

- [Facility](Facility.md) - The containing facility
- [FacilityAreaLevelOne](FacilityAreaLevelOne.md) - Level-one area location
- [FacilityAreaLevelTwo](FacilityAreaLevelTwo.md) - Level-two area location
- [FacilityAreaLevelThree](FacilityAreaLevelThree.md) - Level-three area location
- [Equipment](Equipment.md) - Equipment assigned to the work center
- [WorkCenterHasEquipment](WorkCenterHasEquipment.md) - Equipment assignments
- [WorkCenterHasElectrode](WorkCenterHasElectrode.md) - Electrode availability
- [WorkCenterHasGas](WorkCenterHasGas.md) - Gas availability
- [WorkCenterHasWaveform](WorkCenterHasWaveform.md) - Waveform configurations

## Database Considerations

- Index on `FacilityId` for facility-based queries
- Index on `Type` and `Status` for work center categorization
- Consider composite indexes on area hierarchy fields
- Foreign key constraints should be properly configured
- Consider adding capacity and utilization tracking fields
