﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ANumberElectrodeConfiguration : IEntityTypeConfiguration<ANumberElectrode>
{
    public void Configure(EntityTypeBuilder<ANumberElectrode> builder)
    {
        builder.<PERSON><PERSON>ey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.Specification)
               .WithMany()
               .HasForeignKey(e => e.SpecificationId);

        builder.HasOne(e => e.MetalChemicalComposition)
               .WithMany()
               .<PERSON><PERSON><PERSON><PERSON><PERSON>(e => e.MetalChemicalCompositionId);
    }
}
