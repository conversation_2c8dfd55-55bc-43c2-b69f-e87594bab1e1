﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Data.Configurations;

public class CustomerFacilityConfiguration : IEntityTypeConfiguration<CustomerFacility>
{
    public void Configure(EntityTypeBuilder<CustomerFacility> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Address).HasMaxLength(255);
        builder.Property(e => e.AddressAdditionalInformation).HasMaxLength(255);
        builder.Property(e => e.City).HasMaxLength(255);
        builder.Property(e => e.State).HasMaxLength(255);
        builder.Property(e => e.ZipCode).HasMaxLength(20);
        builder.Property(e => e.Country).HasMaxLength(255);

        builder.HasOne(e => e.Customer)
               .WithMany()
               .HasForeignKey(e => e.CustomerId)
               .OnDelete(DeleteBehavior.Restrict);
    }
}