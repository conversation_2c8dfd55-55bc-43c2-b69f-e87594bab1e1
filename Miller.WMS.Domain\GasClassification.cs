﻿using System;

namespace Miller.WMS.Domain;

public class GasClassification : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public string Group { get; set; } = null!;
    public string Subgroup { get; set; } = null!;
}
