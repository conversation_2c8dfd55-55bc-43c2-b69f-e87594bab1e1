﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FacilityAreaLevelOneConfiguration : IEntityTypeConfiguration<FacilityAreaLevelOne>
{
    public void Configure(EntityTypeBuilder<FacilityAreaLevelOne> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.FacilityAreaName)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.Facility)
               .WithMany()
               .HasForeignKey(e => e.FacilityId);
    }
}
