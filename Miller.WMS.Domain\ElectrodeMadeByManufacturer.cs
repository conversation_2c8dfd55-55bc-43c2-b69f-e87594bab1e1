﻿using System;

namespace Miller.WMS.Domain;

public class ElectrodeMadeByManufacturer : IEntityWithAudit
{

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }
    public Guid ElectrodeId { get; set; }
    public Electrode Electrode { get; set; } = null!;

    public Guid ManufacturerId { get; set; }
    public Manufacturer Manufacturer { get; set; } = null!;

    public Guid? ManufacturerFacilityId { get; set; }
    public ManufacturerFacility? ManufacturerFacility { get; set; }
}
