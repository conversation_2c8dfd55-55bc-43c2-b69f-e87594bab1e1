﻿using System;

namespace Miller.WMS.Domain;

public class Manufacturer : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public string Name { get; set; } = null!;
    public bool IsActive { get; set; }
    public ManufacturerType Type { get; set; }
}
