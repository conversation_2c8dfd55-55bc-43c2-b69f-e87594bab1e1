# CustomerFacility

**Source File:** [CustomerFacility.cs](../CustomerFacility.cs)

## Overview
The `CustomerFacility` entity represents specific facilities or locations belonging to customers. This entity provides detailed location information for customer sites where welding services may be provided or products delivered.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the customer facility |
| `CustomerId` | `Guid` | Yes | Foreign key to the customer |
| `Name` | `string` | Yes | Facility name (max 255 characters) |
| `Address` | `string?` | No | Street address (max 255 characters) |
| `AddressAdditionalInformation` | `string?` | No | Additional address information (max 255 characters) |
| `City` | `string?` | No | City name (max 255 characters) |
| `State` | `string?` | No | State or province (max 255 characters) |
| `ZipCode` | `string?` | No | Postal/ZIP code (max 20 characters) |
| `Country` | `string?` | No | Country name (max 255 characters) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the facility was created |
| `CreatedBy` | `Guid?` | No | User who created the facility record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the facility record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Customer` | `Customer` | The customer that owns this facility |

## Relationships

### Many-to-One Relationships
- **CustomerFacility → Customer**: Each facility belongs to one customer

## Business Rules

1. **Customer Association**: Every customer facility must belong to a customer
2. **Name Requirements**: Facility names are required and should be descriptive
3. **Address Validation**: Address information should be validated for accuracy
4. **Unique Identification**: Facilities should be uniquely identifiable within a customer
5. **Contact Information**: Consider maintaining contact information for each facility

## Usage Examples

### Creating a Customer Facility
```csharp
var customerFacility = new CustomerFacility
{
    Id = Guid.NewGuid(),
    CustomerId = customerId,
    Name = "Boeing Everett Factory",
    Address = "8415 Paine Field Blvd",
    City = "Everett",
    State = "Washington",
    ZipCode = "98204",
    Country = "United States",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "account_manager"
};
```

### Creating Multiple Facilities for a Customer
```csharp
var facilities = new List<CustomerFacility>
{
    new CustomerFacility
    {
        CustomerId = gmCustomerId,
        Name = "Detroit-Hamtramck Assembly Center",
        Address = "2500 E Grand Blvd",
        City = "Detroit",
        State = "Michigan",
        ZipCode = "48202",
        Country = "United States"
    },
    new CustomerFacility
    {
        CustomerId = gmCustomerId,
        Name = "Spring Hill Manufacturing Plant",
        Address = "100 Saturn Pkwy",
        City = "Spring Hill",
        State = "Tennessee",
        ZipCode = "37174",
        Country = "United States"
    }
};
```

## Facility Types

Customer facilities can serve different purposes:

### Manufacturing Facilities
- **Primary Production**: Main manufacturing locations
- **Secondary Production**: Satellite manufacturing facilities
- **Assembly Plants**: Final assembly operations
- **Component Manufacturing**: Specialized component production

### Service Facilities
- **Maintenance Centers**: Equipment maintenance and repair
- **Distribution Centers**: Product distribution and warehousing
- **Research Facilities**: R&D and testing facilities
- **Training Centers**: Employee training and development

### Administrative Facilities
- **Corporate Headquarters**: Main administrative offices
- **Regional Offices**: Regional administrative centers
- **Sales Offices**: Customer-facing sales locations
- **Support Centers**: Technical and customer support

## Address Management

### Address Standardization
- **Street Address**: Primary street address
- **Additional Information**: Suite, building, or unit information
- **City/State/ZIP**: Standard postal addressing
- **Country**: International address support

### Address Validation
Consider implementing address validation:
- **Postal Service APIs**: Validate against postal service databases
- **Geocoding**: Convert addresses to coordinates
- **Standardization**: Ensure consistent address formatting
- **International Support**: Handle various international address formats

## Service Delivery

Customer facilities are important for:

### Logistics Planning
- **Delivery Scheduling**: Plan deliveries to specific facilities
- **Route Optimization**: Optimize delivery routes
- **Inventory Management**: Track inventory at customer locations
- **Service Coordination**: Coordinate on-site services

### Project Management
- **Site-Specific Requirements**: Track facility-specific requirements
- **Local Regulations**: Manage local regulatory compliance
- **Contact Management**: Maintain facility-specific contacts
- **Documentation**: Store facility-specific documentation

## Geographic Considerations

### Regional Management
- **Time Zones**: Consider time zone differences
- **Local Regulations**: Comply with local regulations
- **Cultural Considerations**: Understand local business practices
- **Language Support**: Provide appropriate language support

### Logistics Optimization
- **Distance Calculations**: Calculate distances for logistics
- **Service Areas**: Define service coverage areas
- **Resource Allocation**: Allocate resources based on geography
- **Emergency Response**: Plan emergency response procedures

## Related Entities

- [Customer](Customer.md) - The owning customer
- [Organization](Organization.md) - The organization serving the customer
- [Facility](Facility.md) - Internal facilities that may serve this customer facility

## Database Considerations

- The `Name` property is required with a maximum length of 255 characters
- Address fields have appropriate length constraints
- Index on `CustomerId` for customer-based queries
- Consider indexing on geographic fields for location-based queries
- Foreign key constraints should be properly configured with restrict delete behavior
- Consider adding fields for contact information, facility type, and operational status

## Integration Points

### CRM Systems
- **Customer Management**: Integration with customer relationship management
- **Contact Management**: Facility-specific contact information
- **Opportunity Tracking**: Track opportunities by facility
- **Service History**: Maintain service history by facility

### ERP Systems
- **Order Management**: Facility-specific order processing
- **Inventory Management**: Track inventory by customer facility
- **Billing**: Facility-specific billing and invoicing
- **Logistics**: Delivery and service coordination

### Geographic Information Systems
- **Mapping**: Display facilities on maps
- **Route Planning**: Optimize service routes
- **Territory Management**: Manage sales territories
- **Site Analysis**: Analyze facility locations and characteristics
