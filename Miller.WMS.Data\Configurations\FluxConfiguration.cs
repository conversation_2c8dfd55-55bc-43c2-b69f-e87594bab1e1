﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FluxConfiguration : IEntityTypeConfiguration<Flux>
{
    public void Configure(EntityTypeBuilder<Flux> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.TradeName)
               .HasMaxLength(255)
               .IsRequired();
    }
}
