﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class SupplementalFillerMetalConfiguration : IEntityTypeConfiguration<SupplementalFillerMetal>
{
    public void Configure(EntityTypeBuilder<SupplementalFillerMetal> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.TypeName)
               .HasMaxLength(255)
               .IsRequired();
    }
}
