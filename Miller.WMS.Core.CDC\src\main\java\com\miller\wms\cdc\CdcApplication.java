package com.miller.wms.cdc;

import com.sun.net.httpserver.HttpServer;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpExchange;
import io.debezium.embedded.Connect;
import io.debezium.engine.DebeziumEngine;
import io.debezium.engine.RecordChangeEvent;
import io.debezium.engine.format.ChangeEventFormat;
import org.apache.kafka.connect.source.SourceRecord;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.OutputStream;
import java.net.InetSocketAddress;
import java.util.Properties;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * Main CDC Application that embeds Debezium Engine to capture changes
 * from PostgreSQL and forward them to Elasticsearch.
 */
public class CdcApplication {
    private static final Logger logger = LoggerFactory.getLogger(CdcApplication.class);
    
    private DebeziumEngine<RecordChangeEvent<SourceRecord>> engine;
    private ExecutorService executor;
    private HttpServer healthServer;
    private ElasticsearchSink elasticsearchSink;
    
    public static void main(String[] args) {
        CdcApplication app = new CdcApplication();
        
        // Add shutdown hook
        Runtime.getRuntime().addShutdownHook(new Thread(app::shutdown));
        
        try {
            app.start();
        } catch (Exception e) {
            logger.error("Failed to start CDC application", e);
            System.exit(1);
        }
    }
    
    public void start() throws Exception {
        logger.info("Starting Miller WMS CDC Application...");
        
        // Initialize Elasticsearch sink
        elasticsearchSink = new ElasticsearchSink();
        
        // Start health check server
        startHealthServer();
        
        // Configure and start Debezium engine
        Properties props = loadDebeziumProperties();
        
        engine = DebeziumEngine.create(ChangeEventFormat.of(Connect.class))
                .using(props)
                .notifying(this::handleChangeEvent)
                .build();
        
        executor = Executors.newSingleThreadExecutor();
        executor.execute(engine);
        
        logger.info("CDC Application started successfully");
        
        // Keep the application running
        try {
            Thread.currentThread().join();
        } catch (InterruptedException e) {
            logger.info("Application interrupted, shutting down...");
            Thread.currentThread().interrupt();
        }
    }
    
    private void startHealthServer() throws IOException {
        int port = Integer.parseInt(System.getenv().getOrDefault("HEALTH_PORT", "8080"));
        healthServer = HttpServer.create(new InetSocketAddress(port), 0);
        
        healthServer.createContext("/ping", new HealthHandler());
        healthServer.setExecutor(null);
        healthServer.start();
        
        logger.info("Health check server started on port {}", port);
    }
    
    private Properties loadDebeziumProperties() {
        Properties props = new Properties();
        
        // Database connection
        String dbHost = System.getenv().getOrDefault("DB_HOST", "localhost");
        String dbPort = System.getenv().getOrDefault("DB_PORT", "5432");
        String dbName = System.getenv().getOrDefault("DB_NAME", "wms");
        String dbUser = System.getenv().getOrDefault("DB_USER", "guest");
        String dbPassword = System.getenv().getOrDefault("DB_PASSWORD", "guest");
        
        // Debezium configuration
        props.setProperty("name", "miller-wms-cdc-connector");
        props.setProperty("connector.class", "io.debezium.connector.postgresql.PostgresConnector");
        props.setProperty("offset.storage", "org.apache.kafka.connect.storage.FileOffsetBackingStore");
        props.setProperty("offset.storage.file.filename", "/tmp/offsets.dat");
        props.setProperty("offset.flush.interval.ms", "60000");
        
        // Database connection properties
        props.setProperty("database.hostname", dbHost);
        props.setProperty("database.port", dbPort);
        props.setProperty("database.user", dbUser);
        props.setProperty("database.password", dbPassword);
        props.setProperty("database.dbname", dbName);
        props.setProperty("database.server.name", "miller-wms");
        
        // Table whitelist - only capture Organization table
        props.setProperty("table.include.list", "public.Organizations");
        
        // Plugin configuration
        props.setProperty("plugin.name", "pgoutput");
        props.setProperty("slot.name", "miller_wms_cdc_slot");
        
        // Schema history (required for PostgreSQL)
        props.setProperty("schema.history.internal", "io.debezium.storage.file.history.FileSchemaHistory");
        props.setProperty("schema.history.internal.file.filename", "/tmp/schema-history.dat");
        
        logger.info("Debezium properties configured for database {}:{}/{}", dbHost, dbPort, dbName);
        return props;
    }
    
    private void handleChangeEvent(RecordChangeEvent<SourceRecord> event) {
        try {
            SourceRecord record = event.record();
            logger.debug("Received change event: {}", record);
            
            // Forward to Elasticsearch
            elasticsearchSink.processRecord(record);
            
        } catch (Exception e) {
            logger.error("Error processing change event", e);
        }
    }
    
    public void shutdown() {
        logger.info("Shutting down CDC Application...");
        
        if (engine != null) {
            try {
                engine.close();
            } catch (IOException e) {
                logger.error("Error closing Debezium engine", e);
            }
        }
        
        if (executor != null) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (healthServer != null) {
            healthServer.stop(0);
        }
        
        if (elasticsearchSink != null) {
            elasticsearchSink.close();
        }
        
        logger.info("CDC Application shutdown complete");
    }
    
    private static class HealthHandler implements HttpHandler {
        @Override
        public void handle(HttpExchange exchange) throws IOException {
            String response = "true";
            exchange.sendResponseHeaders(200, response.length());
            try (OutputStream os = exchange.getResponseBody()) {
                os.write(response.getBytes());
            }
        }
    }
}
