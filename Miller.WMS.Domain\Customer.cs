﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class Customer : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public string Name { get; set; } = null!;
    public string Code { get; set; } = null!;

    // Navigation property
    public ICollection<CustomerFacility> Facilities { get; set; } = [];
}