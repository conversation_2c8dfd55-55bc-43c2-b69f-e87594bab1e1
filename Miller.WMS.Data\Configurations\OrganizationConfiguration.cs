﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class OrganizationConfiguration : IEntityTypeConfiguration<Organization>
{
    public void Configure(EntityTypeBuilder<Organization> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.IndustryType)
               .HasConversion<int?>();

        builder.Property(e => e.Status)
               .HasConversion<int>()
               .IsRequired();

        builder.HasMany(e => e.Facilities)
               .WithOne(f => f.Organization)
               .HasForeignKey(f => f.OrganizationId);

        builder.HasMany(e => e.Users)
               .WithOne(u => u.Organization)
               .HasForeignKey(u => u.OrganizationId);
    }
}
