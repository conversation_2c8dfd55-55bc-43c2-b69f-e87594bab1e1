﻿namespace Miller.WMS.Domain;

public class IssuingOrganization : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public string Name { get; set; } = null!;
    public string Abbreviation { get; set; } = null!;
    public string? Website { get; set; }
    public string? Description { get; set; }
}
