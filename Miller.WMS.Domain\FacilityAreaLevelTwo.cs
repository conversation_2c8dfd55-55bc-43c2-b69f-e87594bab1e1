﻿using System;

namespace Miller.WMS.Domain;

public class FacilityAreaLevelTwo : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid FacilityAreaLevelOneId { get; set; }
    public FacilityAreaLevelOne FacilityAreaLevelOne { get; set; } = null!;

    public string FacilityAreaName { get; set; } = null!;
}
