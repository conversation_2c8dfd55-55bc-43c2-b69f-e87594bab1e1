﻿namespace Miller.WMS.Domain;

public class WorkCenterHasElectrode : IEntityWithAudit
{

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid WorkCenterId { get; set; }
    public required WorkCenter WorkCenter { get; set; }

    public Guid ElectrodeId { get; set; }
    public required Electrode Electrode { get; set; }
}
