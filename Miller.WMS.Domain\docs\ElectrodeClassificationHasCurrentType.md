# ElectrodeClassificationHasCurrentType

**Source File:** [ElectrodeClassificationHasCurrentType.cs](../ElectrodeClassificationHasCurrentType.cs)

## Overview
The `ElectrodeClassificationHasCurrentType` entity represents the many-to-many relationship between electrode classifications and the current types they support. This junction entity defines which current types (AC, DCEN, DCEP) are compatible with specific electrode classifications.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `ElectrodeClassificationId` | `Guid` | Yes | Foreign key to the electrode classification |
| `CurrentType` | `CurrentType` | Yes | The current type (enumeration) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the relationship was created |
| `CreatedBy` | `Guid?` | No | User who created this relationship |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this relationship |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `ElectrodeClassification` | `ElectrodeClassification` | The electrode classification |

## Relationships

### Many-to-One Relationships
- **ElectrodeClassificationHasCurrentType → ElectrodeClassification**: Each relationship belongs to one classification

## Composite Key

This entity uses a composite primary key consisting of:
- `ElectrodeClassificationId`
- `CurrentType`

This ensures that each electrode classification can only be associated once with each current type.

## Current Types

### DCEN (Direct Current Electrode Negative)
- **Polarity**: Electrode negative, work positive
- **Characteristics**: Deep penetration, narrow bead
- **Applications**: Root passes, thin materials
- **Heat Distribution**: 70% heat at work, 30% at electrode

### DCEP (Direct Current Electrode Positive)
- **Polarity**: Electrode positive, work negative
- **Characteristics**: Shallow penetration, wide bead, cleaning action
- **Applications**: Aluminum welding, cleaning action required
- **Heat Distribution**: 30% heat at work, 70% at electrode

### AC (Alternating Current)
- **Polarity**: Alternating between positive and negative
- **Characteristics**: Balanced heat distribution, cleaning action
- **Applications**: Aluminum TIG welding, stick welding
- **Heat Distribution**: Balanced between electrode and work

## Current Type Applications

### SMAW (Stick Welding) Current Types

#### E6010 Classification
```csharp
var e6010CurrentTypes = new List<ElectrodeClassificationHasCurrentType>
{
    new ElectrodeClassificationHasCurrentType
    {
        ElectrodeClassificationId = e6010ClassificationId,
        CurrentType = CurrentType.DCEP,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = welding_engineer_id
    }
};
```

#### E7018 Classification
```csharp
var e7018CurrentTypes = new List<ElectrodeClassificationHasCurrentType>
{
    new ElectrodeClassificationHasCurrentType
    {
        ElectrodeClassificationId = e7018ClassificationId,
        CurrentType = CurrentType.DCEP,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = welding_engineer_id
    },
    new ElectrodeClassificationHasCurrentType
    {
        ElectrodeClassificationId = e7018ClassificationId,
        CurrentType = CurrentType.AC,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = welding_engineer_id
    }
};
```

### GTAW (TIG Welding) Current Types

#### Steel Electrodes
- **DCEN**: Most common for steel welding
- **DCEP**: Rarely used, causes excessive electrode consumption
- **AC**: Not typically used for steel

#### Aluminum Electrodes
- **AC**: Primary choice for aluminum welding
- **DCEP**: Used for cleaning action but high electrode consumption
- **DCEN**: Not suitable for aluminum welding

#### Stainless Steel Electrodes
- **DCEN**: Primary choice for stainless steel
- **AC**: Sometimes used for specific applications
- **DCEP**: Rarely used

## Business Rules

1. **Classification Association**: Every current type relationship must belong to an electrode classification
2. **Current Type Validation**: Current type must be valid for the electrode type
3. **Process Compatibility**: Current type must be compatible with intended welding process
4. **Equipment Compatibility**: Current type must be supported by available equipment
5. **Standard Compliance**: Current type assignments must comply with applicable standards

## Usage Examples

### Creating Current Type Relationships for Multiple Classifications
```csharp
var currentTypeRelationships = new List<ElectrodeClassificationHasCurrentType>
{
    // E6010 - DCEP only
    new ElectrodeClassificationHasCurrentType
    {
        ElectrodeClassificationId = e6010Id,
        CurrentType = CurrentType.DCEP,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = standards_engineer_id
    },
    
    // E7018 - DCEP and AC
    new ElectrodeClassificationHasCurrentType
    {
        ElectrodeClassificationId = e7018Id,
        CurrentType = CurrentType.DCEP,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = standards_engineer_id
    },
    new ElectrodeClassificationHasCurrentType
    {
        ElectrodeClassificationId = e7018Id,
        CurrentType = CurrentType.AC,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = standards_engineer_id
    }
};
```

## Current Type Selection Criteria

### Material Considerations
- **Carbon Steel**: DCEN or DCEP depending on application
- **Stainless Steel**: Primarily DCEN for TIG, DCEP for stick
- **Aluminum**: AC for TIG welding, DCEP for stick (rare)
- **Cast Iron**: AC or DCEN depending on electrode type

### Application Considerations
- **Root Passes**: Often DCEN for deep penetration
- **Fill Passes**: DCEP or AC depending on electrode
- **Cap Passes**: Current type for desired bead appearance
- **Repair Welding**: Current type based on accessibility and position

### Equipment Considerations
- **Power Supply Capability**: Must support required current type
- **Electrode Holder**: Must be suitable for current type
- **Grounding**: Proper grounding for DC applications
- **Safety**: Safety considerations for different current types

## Welding Characteristics by Current Type

### Penetration Characteristics
- **DCEN**: Deep, narrow penetration
- **DCEP**: Shallow, wide penetration
- **AC**: Medium penetration, balanced

### Bead Appearance
- **DCEN**: Narrow, high crown
- **DCEP**: Wide, flat bead
- **AC**: Medium width, good appearance

### Arc Characteristics
- **DCEN**: Stable arc, easy to control
- **DCEP**: Stable arc, cleaning action
- **AC**: Requires arc stabilization, balanced heating

## Quality Considerations

### Mechanical Properties
- **Strength**: Current type can affect weld metal strength
- **Ductility**: Impact on weld metal ductility
- **Toughness**: Effect on impact toughness
- **Hardness**: Influence on weld metal hardness

### Weld Quality
- **Porosity**: Current type effect on porosity formation
- **Inclusions**: Impact on inclusion formation
- **Cracking**: Influence on hot and cold cracking
- **Distortion**: Effect on welding distortion

## Related Entities

- [ElectrodeClassification](ElectrodeClassification.md) - The electrode classification
- [CurrentType](CurrentType.md) - The current type enumeration
- [Equipment](Equipment.md) - Equipment that must support the current type
- [WeldingProcess](WeldingProcess.md) - Welding processes using the current type

## Database Considerations

- Composite primary key on `ElectrodeClassificationId` and `CurrentType`
- Index on `ElectrodeClassificationId` for classification-based queries
- Index on `CurrentType` for current type-based queries
- Foreign key constraints should be properly configured
- Consider adding fields for recommended parameters (voltage, amperage ranges)

## Standards Compliance

### AWS Standards
- **AWS A5.1**: Stick electrode current type requirements
- **AWS A5.18**: Wire electrode current type specifications
- **AWS D1.1**: Structural welding current type requirements

### ASME Standards
- **ASME Section IX**: Procedure qualification current type requirements
- **ASME Section II**: Material specification current type compatibility

## Equipment Integration

### Power Supply Requirements
- **DC Capability**: Must provide stable DC output
- **AC Capability**: Must provide stable AC output with arc stabilization
- **Polarity Switching**: Easy polarity switching for DC applications
- **Current Control**: Precise current control for all types

### Safety Considerations
- **Electrical Safety**: Proper electrical safety for all current types
- **Arc Stability**: Ensure stable arc for all supported current types
- **Operator Training**: Train operators on current type selection and use
- **Equipment Maintenance**: Maintain equipment for optimal current type performance
