# MetalChemicalComposition

**Source File:** [MetalChemicalComposition.cs](../MetalChemicalComposition.cs)

## Overview
The `MetalChemicalComposition` entity defines the chemical composition of metals used in welding electrodes, filler metals, and base materials. This information is critical for determining material properties, compatibility, and welding procedures.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the composition |
| `MinCarbon` | `decimal?` | No | Minimum carbon content percentage |
| `MaxCarbon` | `decimal?` | No | Maximum carbon content percentage |
| `MinChromium` | `decimal?` | No | Minimum chromium content percentage |
| `MaxChromium` | `decimal?` | No | Maximum chromium content percentage |
| `MinMolybdenum` | `decimal?` | No | Minimum molybdenum content percentage |
| `MaxMolybdenum` | `decimal?` | No | Maximum molybdenum content percentage |
| `MinNickel` | `decimal?` | No | Minimum nickel content percentage |
| `MaxNickel` | `decimal?` | No | Maximum nickel content percentage |
| `MinManganese` | `decimal?` | No | Minimum manganese content percentage |
| `MaxManganese` | `decimal?` | No | Maximum manganese content percentage |
| `MinSilicon` | `decimal?` | No | Minimum silicon content percentage |
| `MaxSilicon` | `decimal?` | No | Maximum silicon content percentage |
| `CreatedAt` | `DateTime?` | No | Timestamp when the composition was created |
| `CreatedBy` | `Guid?` | No | User who created the composition record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the composition record |

## Relationships

### One-to-Many Relationships
- **MetalChemicalComposition → Electrode**: Compositions can be used by multiple electrodes
- **MetalChemicalComposition → ANumberElectrode**: Compositions can be used by A-number electrodes

## Chemical Elements

### Carbon (C)
- **Purpose**: Controls strength and hardness
- **Typical Range**: 0.05% - 1.5%
- **Effects**: Higher carbon increases strength but reduces ductility and weldability

### Chromium (Cr)
- **Purpose**: Provides corrosion resistance and hardenability
- **Typical Range**: 0.5% - 30%
- **Effects**: Essential for stainless steels, improves high-temperature properties

### Molybdenum (Mo)
- **Purpose**: Enhances high-temperature strength and corrosion resistance
- **Typical Range**: 0.2% - 6%
- **Effects**: Improves creep resistance and toughness

### Nickel (Ni)
- **Purpose**: Improves toughness and corrosion resistance
- **Typical Range**: 0.5% - 35%
- **Effects**: Stabilizes austenitic structure in stainless steels

### Manganese (Mn)
- **Purpose**: Deoxidizer and strengthening element
- **Typical Range**: 0.3% - 2.0%
- **Effects**: Improves strength and hardenability, counteracts sulfur effects

### Silicon (Si)
- **Purpose**: Deoxidizer and strengthening element
- **Typical Range**: 0.1% - 1.0%
- **Effects**: Improves fluidity and reduces porosity in welds

## Material Categories

### Carbon Steels
```csharp
var carbonSteel = new MetalChemicalComposition
{
    MinCarbon = 0.05m,
    MaxCarbon = 0.30m,
    MinManganese = 0.30m,
    MaxManganese = 1.60m,
    MinSilicon = 0.10m,
    MaxSilicon = 0.35m
    // Chromium, Molybdenum, Nickel typically null or very low
};
```

### Low-Alloy Steels
```csharp
var lowAlloySteel = new MetalChemicalComposition
{
    MinCarbon = 0.05m,
    MaxCarbon = 0.15m,
    MinChromium = 0.80m,
    MaxChromium = 1.25m,
    MinMolybdenum = 0.45m,
    MaxMolybdenum = 0.65m,
    MinManganese = 0.30m,
    MaxManganese = 1.60m
};
```

### Stainless Steels
```csharp
var stainlessSteel = new MetalChemicalComposition
{
    MaxCarbon = 0.08m,
    MinChromium = 18.0m,
    MaxChromium = 20.0m,
    MinNickel = 8.0m,
    MaxNickel = 12.0m,
    MaxManganese = 2.0m,
    MaxSilicon = 1.0m
};
```

## Business Rules

1. **Range Validation**: Minimum values must be less than or equal to maximum values
2. **Percentage Limits**: All values should be between 0 and 100 percent
3. **Composition Logic**: Total composition should not exceed 100% when all elements are considered
4. **Material Classification**: Compositions should align with standard material classifications
5. **Specification Compliance**: Compositions must meet relevant specification requirements

## Usage Examples

### Creating a Carbon Steel Composition
```csharp
var carbonSteelComp = new MetalChemicalComposition
{
    Id = Guid.NewGuid(),
    MinCarbon = 0.06m,
    MaxCarbon = 0.14m,
    MinManganese = 0.80m,
    MaxManganese = 1.60m,
    MinSilicon = 0.15m,
    MaxSilicon = 0.35m,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "materials_engineer"
};
```

### Creating a Stainless Steel Composition
```csharp
var stainlessComp = new MetalChemicalComposition
{
    MaxCarbon = 0.03m,
    MinChromium = 18.0m,
    MaxChromium = 20.0m,
    MinNickel = 10.0m,
    MaxNickel = 14.0m,
    MaxManganese = 2.0m,
    MaxSilicon = 1.0m,
    MinMolybdenum = 2.0m,
    MaxMolybdenum = 3.0m
};
```

## Metallurgical Considerations

### Weldability
- **Carbon Equivalent**: Higher carbon and alloy content reduces weldability
- **Preheating**: High-alloy compositions may require preheating
- **Post-Weld Heat Treatment**: Some compositions require PWHT

### Mechanical Properties
- **Strength**: Controlled by carbon and alloy content
- **Toughness**: Affected by carbon, nickel, and grain refiners
- **Ductility**: Generally decreases with increasing carbon and alloy content

### Corrosion Resistance
- **Chromium**: Primary element for corrosion resistance
- **Nickel**: Enhances corrosion resistance in certain environments
- **Molybdenum**: Improves pitting and crevice corrosion resistance

## Quality Control

### Chemical Analysis
- **Ladle Analysis**: Composition during melting
- **Product Analysis**: Final product composition verification
- **Tolerance Limits**: Acceptable variations from target composition

### Testing Requirements
- **Spectroscopic Analysis**: For accurate composition determination
- **Wet Chemical Analysis**: For verification of critical elements
- **Certification**: Material test certificates (MTCs) for traceability

## Related Entities

- [Electrode](Electrode.md) - Electrodes using this composition
- [ANumberElectrode](ANumberElectrode.md) - A-number electrodes using this composition
- [Material](Material.md) - Base materials with this composition

## Database Considerations

- All decimal properties should have appropriate precision for chemical analysis
- Consider adding indexes for common composition queries
- Implement check constraints to ensure min ≤ max for each element
- Consider adding computed columns for carbon equivalent calculations
- Foreign key relationships should be properly configured

## Extended Composition

Consider adding additional elements for comprehensive composition tracking:

- **Phosphorus (P)**: Typically limited for weldability
- **Sulfur (S)**: Typically limited for weldability and toughness
- **Aluminum (Al)**: Deoxidizer and grain refiner
- **Titanium (Ti)**: Carbide former and grain refiner
- **Vanadium (V)**: Strengthening element
- **Copper (Cu)**: Corrosion resistance in certain environments
