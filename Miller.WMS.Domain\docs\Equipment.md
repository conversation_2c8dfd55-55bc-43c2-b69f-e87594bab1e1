# Equipment

**Source File:** [Equipment.cs](../Equipment.cs)

## Overview
The `Equipment` entity represents welding equipment, machinery, and tools used in welding operations. This includes power supplies, torches, feeders, and other welding-related equipment.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the equipment |
| `ManufacturerId` | `Guid` | Yes | Foreign key to the equipment manufacturer |
| `Type` | `EquipmentType` | Yes | Primary category of equipment |
| `SubType` | `EquipmentSubType` | Yes | Specific subcategory of equipment |
| `Model` | `string` | Yes | Equipment model name/number |
| `Status` | `EquipmentStatus` | Yes | Current operational status |
| `CreatedAt` | `DateTime?` | No | Timestamp when the equipment was created |
| `CreatedBy` | `Guid?` | No | User who created the equipment record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the equipment record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Manufacturer` | `Manufacturer` | The manufacturer of this equipment |

## Relationships

### Many-to-One Relationships
- **Equipment → Manufacturer**: Each piece of equipment has one manufacturer

### Many-to-Many Relationships (via junction entities)
- **Equipment ↔ WorkCenter**: Equipment can be assigned to multiple work centers
- **Equipment ↔ ElectrodeClassification**: Equipment may be compatible with specific electrode classifications

## Equipment Types

### EquipmentType Enumeration
- **Welding**: Primary welding equipment
- **Machining**: Machining and preparation equipment
- **Assembly**: Assembly and fixturing equipment
- **Inspection**: Quality control and inspection equipment
- **Safety**: Safety and protection equipment
- **Induction**: Induction heating equipment
- **Cleaning**: Cleaning and surface preparation equipment
- **Other**: Miscellaneous equipment

### EquipmentSubType Enumeration
- **PowerSupply**: Welding power sources
- **Torch**: Welding torches and guns
- **Feeder**: Wire feeders and electrode feeders
- **ContactTip**: Contact tips and consumables
- **PreHeat**: Pre-heating equipment
- **PostHeat**: Post-weld heat treatment equipment
- **Cleaning**: Cleaning equipment
- **ToeTreatment**: Weld toe treatment equipment
- **FumeExtraction**: Fume extraction systems
- **GrindingSanding**: Grinding and sanding equipment
- **Filtering**: Filtration systems

### EquipmentStatus Enumeration
- **Active**: Equipment is operational and available
- **Inactive**: Equipment is temporarily out of service
- **Retired**: Equipment is permanently out of service

## Business Rules

1. **Manufacturer Association**: All equipment must have an associated manufacturer
2. **Model Identification**: Equipment models should be unique within a manufacturer
3. **Status Management**: Equipment status should be actively managed and updated
4. **Type Consistency**: Equipment type and subtype should be logically consistent
5. **Work Center Assignment**: Equipment can be assigned to multiple work centers

## Usage Examples

### Creating New Equipment
```csharp
var powerSupply = new Equipment
{
    Id = Guid.NewGuid(),
    ManufacturerId = millerId,
    Type = EquipmentType.Welding,
    SubType = EquipmentSubType.PowerSupply,
    Model = "Dynasty 350",
    Status = EquipmentStatus.Active,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Equipment with Manufacturer
```csharp
var torch = new Equipment
{
    Type = EquipmentType.Welding,
    SubType = EquipmentSubType.Torch,
    Model = "TIG-200",
    Status = EquipmentStatus.Active,
    Manufacturer = new Manufacturer
    {
        Name = "Miller Electric",
        Type = ManufacturerType.PowerSupply,
        IsActive = true
    }
};
```

## Equipment Capabilities

Equipment can have specific capabilities defined through relationships:

### Electrode Classification Compatibility
Equipment may be compatible with specific electrode classifications, defined through the `EquipmentHasElectrodeClassification` relationship.

### Work Center Assignments
Equipment is assigned to work centers through the `WorkCenterHasEquipment` relationship, allowing tracking of equipment location and availability.

## Maintenance and Lifecycle

Consider implementing additional properties for equipment management:

- **Serial Number**: Unique equipment identification
- **Purchase Date**: When equipment was acquired
- **Warranty Information**: Warranty status and expiration
- **Maintenance Schedule**: Planned maintenance intervals
- **Calibration Status**: For precision equipment
- **Location Tracking**: Current physical location

## Related Entities

- [Manufacturer](Manufacturer.md) - Equipment manufacturer information
- [WorkCenter](WorkCenter.md) - Work centers where equipment is located
- [WorkCenterHasEquipment](WorkCenterHasEquipment.md) - Equipment assignments to work centers
- [EquipmentHasElectrodeClassification](EquipmentHasElectrodeClassification.md) - Equipment electrode compatibility
- [ElectrodeClassification](ElectrodeClassification.md) - Compatible electrode classifications

## Database Considerations

- Index on `ManufacturerId` for manufacturer lookups
- Index on `Type` and `SubType` for equipment categorization queries
- Index on `Status` for active equipment queries
- Consider full-text search on `Model` for equipment searches
- Foreign key constraints should be properly configured
- Consider adding unique constraints on manufacturer + model combinations
