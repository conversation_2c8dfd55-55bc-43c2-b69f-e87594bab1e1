# WorkCenterStatus Enumeration

**Source File:** [WorkCenterStatus.cs](../WorkCenterStatus.cs)

## Overview
The `WorkCenterStatus` enumeration defines the operational status of work centers within manufacturing facilities. This status tracking is essential for production planning, capacity management, and operational efficiency monitoring.

## Values

| Value | Description | Operational State | Production Impact |
|-------|-------------|-------------------|-------------------|
| `Active` | Work center is operational and available | Fully operational | Available for production scheduling |
| `Maintenance` | Work center is undergoing maintenance | Temporarily unavailable | Not available for production |
| `Idle` | Work center is idle but available | Available but not in use | Available for immediate production |
| `Inactive` | Work center is not operational | Not operational | Not available for production |

## Status Characteristics

### Active
- **Operational State**: Fully operational and engaged in production activities
- **Production Availability**: Available for production scheduling and work assignments
- **Equipment Status**: All equipment operational and ready for use
- **Staffing**: Properly staffed with qualified operators
- **Utilities**: All utilities (power, gas, air, water) available and functioning
- **Typical Scenarios**:
  - Work centers in normal production operation
  - Work centers with active work orders
  - Work centers meeting production schedules
  - Work centers with qualified operators on duty
- **Performance Monitoring**: Full performance monitoring and data collection
- **Quality Control**: Standard quality control procedures in effect

### Maintenance
- **Operational State**: Undergoing scheduled or unscheduled maintenance
- **Production Availability**: Not available for production activities
- **Equipment Status**: Equipment under maintenance or repair
- **Staffing**: Maintenance personnel assigned, production staff may be reassigned
- **Utilities**: Utilities may be isolated for safety during maintenance
- **Types of Maintenance**:
  - **Preventive Maintenance**: Scheduled routine maintenance
  - **Predictive Maintenance**: Condition-based maintenance activities
  - **Corrective Maintenance**: Repair of identified problems
  - **Emergency Maintenance**: Unscheduled maintenance due to failures
- **Safety Procedures**: Lockout/tagout procedures in effect
- **Duration**: Temporary status, returns to Active when maintenance complete

### Idle
- **Operational State**: Available but not currently in use
- **Production Availability**: Available for immediate production assignment
- **Equipment Status**: Equipment ready and available for operation
- **Staffing**: May have reduced staffing or operators on standby
- **Utilities**: All utilities available and ready for use
- **Typical Scenarios**:
  - Work centers between production runs
  - Work centers awaiting work assignments
  - Work centers during shift changes
  - Work centers during production planning periods
  - Backup work centers not currently needed
- **Readiness**: Maintained in ready state for quick activation
- **Monitoring**: Basic monitoring to ensure readiness

### Inactive
- **Operational State**: Not operational and not available for production
- **Production Availability**: Not available for production scheduling
- **Equipment Status**: Equipment may be shut down or removed
- **Staffing**: No production staff assigned
- **Utilities**: Utilities may be disconnected or isolated
- **Typical Scenarios**:
  - Work centers undergoing major renovation or reconfiguration
  - Work centers with obsolete or failed equipment
  - Work centers eliminated due to process changes
  - Work centers temporarily closed due to business conditions
  - Work centers awaiting equipment installation
- **Long-term Status**: May be long-term or permanent status
- **Resource Reallocation**: Resources may be reallocated to other work centers

## Status Transition Rules

### Typical Status Flow
```
Active ↔ Idle
Active → Maintenance → Active
Active → Maintenance → Idle
Idle → Active
Idle → Maintenance → Active
Idle → Maintenance → Idle
Active → Inactive
Idle → Inactive
Inactive → Idle (with preparation)
Inactive → Active (with full preparation)
```

### Transition Triggers
- **Active to Maintenance**: Scheduled maintenance, equipment failure, safety issue
- **Active to Idle**: Production completion, shift end, work order completion
- **Idle to Active**: Work assignment, production start, shift beginning
- **Maintenance to Active**: Maintenance completion, equipment ready
- **Any Status to Inactive**: Major equipment failure, business decision, renovation

## Business Rules

### Status Management Rules
1. **Authority**: Only authorized personnel can change work center status
2. **Documentation**: All status changes must be documented with reason and timestamp
3. **Safety**: Safety procedures must be followed for all status changes
4. **Notification**: Status changes trigger appropriate notifications to planning and operations
5. **Approval**: Inactive status changes require management approval

### Operational Rules
- **Active Work Centers**: Must meet all safety, quality, and operational requirements
- **Maintenance Work Centers**: Must follow lockout/tagout and safety procedures
- **Idle Work Centers**: Must be maintained in ready state for quick activation
- **Inactive Work Centers**: Must be properly secured and isolated

## Impact on Production Planning

### Active Status Impact
- **Capacity Planning**: Full capacity available for production planning
- **Scheduling**: Available for all production scheduling activities
- **Resource Allocation**: Full resource allocation and utilization
- **Performance Metrics**: Full performance monitoring and reporting

### Maintenance Status Impact
- **Capacity Planning**: Temporary capacity reduction during maintenance
- **Scheduling**: Not available for production scheduling
- **Resource Allocation**: Resources temporarily unavailable
- **Performance Metrics**: Maintenance metrics and downtime tracking

### Idle Status Impact
- **Capacity Planning**: Available capacity but not currently utilized
- **Scheduling**: Available for immediate scheduling if needed
- **Resource Allocation**: Resources available but not allocated
- **Performance Metrics**: Availability metrics and utilization tracking

### Inactive Status Impact
- **Capacity Planning**: Permanent or long-term capacity reduction
- **Scheduling**: Not available for production scheduling
- **Resource Allocation**: Resources not available for allocation
- **Performance Metrics**: Historical metrics only

## Maintenance Management

### Status-Based Maintenance
- **Active**: Condition monitoring and preventive maintenance scheduling
- **Maintenance**: Active maintenance execution and progress tracking
- **Idle**: Preservation maintenance and readiness checks
- **Inactive**: Mothballing procedures and long-term preservation

### Maintenance Planning
- **Preventive**: Scheduled based on operating hours, calendar time, or condition
- **Predictive**: Based on condition monitoring and predictive analytics
- **Corrective**: Based on failure analysis and repair requirements
- **Emergency**: Immediate response for critical failures

## Performance Metrics

### Status-Based Metrics
- **Availability**: Percentage of time work center is Active or Idle
- **Utilization**: Percentage of available time work center is Active
- **Reliability**: Mean time between maintenance events
- **Maintainability**: Mean time to complete maintenance activities

### Key Performance Indicators
- **Overall Equipment Effectiveness (OEE)**: Availability × Performance × Quality
- **Planned Maintenance Percentage**: Percentage of maintenance that is planned
- **Emergency Maintenance Percentage**: Percentage of maintenance that is emergency
- **Work Center Efficiency**: Actual output versus planned output

## Safety Considerations

### Status-Specific Safety
- **Active**: Standard operational safety procedures
- **Maintenance**: Enhanced safety procedures, lockout/tagout, confined space
- **Idle**: Basic safety procedures, security measures
- **Inactive**: Isolation procedures, access control, environmental safety

### Safety Management
- **Risk Assessment**: Status-specific risk assessments
- **Safety Procedures**: Documented safety procedures for each status
- **Training**: Status-specific safety training for personnel
- **Monitoring**: Continuous safety monitoring and incident tracking

## Related Entities

- [WorkCenter](WorkCenter.md) - Work centers with operational status
- [WorkCenterType](WorkCenterType.md) - Types of work centers with different status implications
- [Equipment](Equipment.md) - Equipment within work centers affecting status
- [EquipmentStatus](EquipmentStatus.md) - Equipment status affecting work center status

## Usage in Domain Model

The `WorkCenterStatus` enumeration is used in:
- **WorkCenter entity**: Tracks current operational status of work centers
- **Production planning**: Determines available capacity for production scheduling
- **Maintenance planning**: Schedules maintenance activities based on status
- **Performance reporting**: Reports work center availability and utilization

## Integration with Systems

### Manufacturing Execution Systems (MES)
- **Real-Time Status**: Real-time work center status updates
- **Production Scheduling**: Integration with production scheduling systems
- **Work Order Management**: Work order routing based on work center status
- **Performance Monitoring**: Real-time performance monitoring and reporting

### Enterprise Resource Planning (ERP)
- **Capacity Planning**: Integration with capacity planning modules
- **Maintenance Management**: Integration with maintenance management systems
- **Cost Accounting**: Cost allocation based on work center status
- **Resource Planning**: Resource planning based on work center availability

## Best Practices

### Status Management
1. **Real-Time Updates**: Maintain real-time work center status information
2. **Clear Procedures**: Establish clear procedures for status changes
3. **Automated Monitoring**: Use automated systems for status monitoring where possible
4. **Regular Review**: Regular review of work center status and performance
5. **Continuous Improvement**: Continuously improve status management processes

### Operational Excellence
- **Proactive Maintenance**: Use predictive maintenance to minimize unplanned downtime
- **Quick Changeover**: Minimize time for status transitions
- **Cross-Training**: Cross-train operators to maximize work center utilization
- **Performance Optimization**: Continuously optimize work center performance
