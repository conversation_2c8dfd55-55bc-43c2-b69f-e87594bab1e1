﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Design;
using Miller.WMS.Domain;

namespace Miller.WMS.Shared.Data;

public class WmsContext(DbContextOptions<WmsContext> options) : DbContext(options)
{
    public DbSet<CatElectrodeSpecialRule> CatElectrodeSpecialRules => Set<CatElectrodeSpecialRule>();
    public DbSet<Customer> Customers => Set<Customer>();
    public DbSet<CustomerFacility> CustomerFacilities => Set<CustomerFacility>();
    public DbSet<Electrode> Electrodes => Set<Electrode>();
    public DbSet<ElectrodeClassification> ElectrodeClassifications => Set<ElectrodeClassification>();
    public DbSet<ElectrodeClassificationCurrentType> ElectrodeClassificationCurrentTypes => Set<ElectrodeClassificationCurrentType>();
    public DbSet<ElectrodeClassificationHasWeldingPosition> ElectrodeClassificationWeldingPositions => Set<ElectrodeClassificationHasWeldingPosition>();
    public DbSet<ElectrodeClassificationHasWeldingProcess> ElectrodeClassificationWeldingProcesses => Set<ElectrodeClassificationHasWeldingProcess>();
    public DbSet<ElectrodeDiameter> ElectrodeDiameters => Set<ElectrodeDiameter>();
    public DbSet<ElectrodeHasElectrodeClassification> ElectrodeHasElectrodeClassifications => Set<ElectrodeHasElectrodeClassification>();
    public DbSet<ElectrodeMadeByManufacturer> ElectrodeMadeByManufacturers => Set<ElectrodeMadeByManufacturer>();
    public DbSet<Equipment> Equipment => Set<Equipment>();
    public DbSet<EquipmentHasCurrentType> EquipmentHasCurrentTypes => Set<EquipmentHasCurrentType>();
    public DbSet<Facility> Facilities => Set<Facility>();
    public DbSet<FacilityAreaLevelOne> FacilityAreaLevelOnes => Set<FacilityAreaLevelOne>();
    public DbSet<FacilityAreaLevelTwo> FacilityAreaLevelTwos => Set<FacilityAreaLevelTwo>();
    public DbSet<FacilityAreaLevelThree> FacilityAreaLevelThrees => Set<FacilityAreaLevelThree>();
    public DbSet<Flux> Fluxes => Set<Flux>();
    public DbSet<Gas> Gases => Set<Gas>();
    public DbSet<GasChemicalComposition> GasChemicalCompositions => Set<GasChemicalComposition>();
    public DbSet<GasClassification> GasClassifications => Set<GasClassification>();
    public DbSet<IssuingOrganization> IssuingOrganizations => Set<IssuingOrganization>();
    public DbSet<Manufacturer> Manufacturers => Set<Manufacturer>();
    public DbSet<ManufacturerFacility> ManufacturerFacilities => Set<ManufacturerFacility>();
    public DbSet<Material> Materials => Set<Material>();
    public DbSet<MaterialSubstitution> MaterialSubstitutions => Set<MaterialSubstitution>();
    public DbSet<MetalChemicalComposition> MetalChemicalCompositions => Set<MetalChemicalComposition>();
    public DbSet<Organization> Organizations => Set<Organization>();
    public DbSet<SupplementalFillerMetal> SupplementalFillerMetals => Set<SupplementalFillerMetal>();
    public DbSet<TungstenElectrodeClassification> TungstenElectrodeClassifications => Set<TungstenElectrodeClassification>();
    public DbSet<User> Users => Set<User>();
    public DbSet<UserFacilityRole> UserFacilities => Set<UserFacilityRole>();

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(WmsContext).Assembly);
        base.OnModelCreating(modelBuilder);
    }
}

/// <summary>
/// Used purely for design-time services such as migrations.
/// </summary>
public class WmsContextFactory : IDesignTimeDbContextFactory<WmsContext>
{
    public WmsContext CreateDbContext(string[] args)
    {
        var optionsBuilder = new DbContextOptionsBuilder<WmsContext>();
        optionsBuilder.UseNpgsql();

        return new WmsContext(optionsBuilder.Options);
    }
}