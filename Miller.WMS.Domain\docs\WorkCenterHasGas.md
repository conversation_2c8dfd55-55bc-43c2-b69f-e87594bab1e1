# WorkCenterHasGas

**Source File:** [WorkCenterHasGas.cs](../WorkCenterHasGas.cs)

## Overview
The `WorkCenterHasGas` entity represents the many-to-many relationship between work centers and gases, tracking which gases are available or assigned to specific work centers for welding operations.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `WorkCenterId` | `Guid` | Yes | Foreign key to the work center |
| `GasId` | `Guid` | Yes | Foreign key to the gas |
| `CreatedAt` | `DateTime?` | No | Timestamp when the gas was assigned |
| `CreatedBy` | `Guid?` | No | User who created this assignment |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this assignment |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `WorkCenter` | `WorkCenter` | The work center where gas is available |
| `Gas` | `Gas` | The gas available at the work center |

## Relationships

### Many-to-One Relationships
- **WorkCenterHasGas → WorkCenter**: Each assignment belongs to one work center
- **WorkCenterHasGas → Gas**: Each assignment is for one gas

## Composite Key

This entity uses a composite primary key consisting of:
- `WorkCenterId`
- `GasId`

This ensures that the same gas can only be assigned once to a specific work center, but gases can be available at multiple work centers.

## Gas Assignment Types

Gas assignments can represent different availability scenarios:

### Permanent Supply
- Gases permanently supplied to the work center
- Primary gases for the work center's typical operations
- High-volume gases for production work

### Temporary Assignment
- Gases temporarily assigned for specific jobs
- Special gas mixtures for unique applications
- Project-specific gas assignments

### Shared Supply
- Gases shared between multiple work centers
- Common gases available across the facility
- Backup gases for emergency use

## Business Rules

1. **Work Center Status**: Only active work centers should have gas assignments
2. **Gas Compatibility**: Gases should be compatible with work center equipment
3. **Process Compatibility**: Gases should match work center welding processes
4. **Safety Requirements**: Gas assignments must meet safety requirements
5. **Supply Management**: Gas supply capacity should be adequate for work center needs

## Usage Examples

### Assigning Gases to a TIG Work Center
```csharp
var tigGases = new List<WorkCenterHasGas>
{
    new WorkCenterHasGas
    {
        WorkCenterId = tigStationId,
        GasId = pureArgonId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = supervisor_id
    },
    new WorkCenterHasGas
    {
        WorkCenterId = tigStationId,
        GasId = argonHeliumMixId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = supervisor_id
    }
};
```

### Assigning Gases to a MIG Work Center
```csharp
var migGases = new List<WorkCenterHasGas>
{
    new WorkCenterHasGas
    {
        WorkCenterId = migStationId,
        GasId = argonCO2_75_25Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_coordinator_id
    },
    new WorkCenterHasGas
    {
        WorkCenterId = migStationId,
        GasId = argonCO2_90_10Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_coordinator_id
    },
    new WorkCenterHasGas
    {
        WorkCenterId = migStationId,
        GasId = pureArgonId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_coordinator_id
    }
};
```

## Work Center Gas Capabilities

Gas assignments define work center welding capabilities:

### Process Capabilities
- **GTAW (TIG)**: Pure argon, argon/helium mixtures
- **GMAW (MIG)**: Argon/CO₂ mixtures, pure argon, argon/oxygen mixtures
- **FCAW**: CO₂, argon/CO₂ mixtures
- **Plasma Cutting**: Compressed air, nitrogen, argon/hydrogen mixtures

### Material Capabilities
- **Carbon Steel**: Argon/CO₂ mixtures, pure CO₂
- **Stainless Steel**: Pure argon, argon/oxygen mixtures
- **Aluminum**: Pure argon, argon/helium mixtures
- **Specialty Alloys**: Pure argon, specialized gas mixtures

### Application Capabilities
- **Structural Welding**: Argon/CO₂ mixtures for productivity
- **Precision Welding**: Pure argon for quality
- **Heavy Section Welding**: Argon/helium mixtures for heat input
- **Thin Material Welding**: Pure argon for control

## Gas Supply Management

This relationship enables comprehensive gas supply management:

### Supply Planning
- **Consumption Rates**: Historical gas consumption patterns
- **Demand Forecasting**: Predict gas requirements
- **Supply Capacity**: Ensure adequate supply capacity
- **Backup Planning**: Plan backup gas supplies

### Inventory Management
- **Cylinder Tracking**: Track gas cylinders by work center
- **Usage Monitoring**: Monitor gas usage rates
- **Reorder Management**: Automatic reorder triggers
- **Cost Allocation**: Allocate gas costs by work center

### Safety Management
- **Gas Compatibility**: Ensure gas compatibility with equipment
- **Ventilation Requirements**: Adequate ventilation for gas use
- **Emergency Procedures**: Gas-specific emergency procedures
- **Training Requirements**: Gas handling training

## Gas Distribution Systems

### Centralized Systems
- **Bulk Supply**: Central gas storage with distribution
- **Manifold Systems**: Multiple cylinder manifolds
- **Pipeline Distribution**: Permanent gas pipelines
- **Automatic Switching**: Automatic supply switching

### Decentralized Systems
- **Individual Cylinders**: Cylinders at each work center
- **Portable Supply**: Mobile gas supply units
- **Point-of-Use**: Gas generation at point of use
- **Flexible Supply**: Adaptable supply configurations

## Quality Control

### Gas Purity Management
- **Purity Specifications**: Maintain required gas purity
- **Contamination Prevention**: Prevent gas contamination
- **Testing Protocols**: Regular gas purity testing
- **Certification**: Gas purity certification requirements

### Supply Quality
- **Pressure Regulation**: Maintain proper gas pressures
- **Flow Control**: Ensure adequate gas flow rates
- **Moisture Control**: Control moisture in gas supply
- **Temperature Control**: Maintain proper gas temperatures

## Usage Examples

### Creating Gas Assignments for Different Work Center Types
```csharp
var gasAssignments = new List<WorkCenterHasGas>
{
    // TIG Station - Pure argon and Ar/He mixture
    new WorkCenterHasGas
    {
        WorkCenterId = tigStationId,
        GasId = pureArgonId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_manager_id
    },
    new WorkCenterHasGas
    {
        WorkCenterId = tigStationId,
        GasId = argonHelium75_25Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_manager_id
    },
    
    // MIG Station - Ar/CO₂ mixtures
    new WorkCenterHasGas
    {
        WorkCenterId = migStationId,
        GasId = argonCO2_75_25Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_manager_id
    },
    new WorkCenterHasGas
    {
        WorkCenterId = migStationId,
        GasId = argonCO2_90_10Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_manager_id
    },
    
    // Flux-Cored Station - CO₂
    new WorkCenterHasGas
    {
        WorkCenterId = fcawStationId,
        GasId = pureCO2Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = gas_manager_id
    }
};
```

## Cost Management

### Gas Cost Tracking
- **Usage-Based Costing**: Track costs by actual usage
- **Work Center Allocation**: Allocate costs to work centers
- **Job Costing**: Track gas costs by specific jobs
- **Efficiency Analysis**: Analyze gas usage efficiency

### Cost Optimization
- **Bulk Purchasing**: Optimize gas purchasing strategies
- **Usage Optimization**: Optimize gas usage patterns
- **Waste Reduction**: Minimize gas waste and losses
- **Alternative Gases**: Evaluate alternative gas options

## Environmental Considerations

### Environmental Impact
- **Greenhouse Gases**: Consider environmental impact of gases
- **Waste Management**: Proper disposal of empty cylinders
- **Energy Efficiency**: Optimize energy use in gas systems
- **Sustainability**: Sustainable gas supply practices

### Regulatory Compliance
- **Environmental Regulations**: Comply with environmental regulations
- **Safety Regulations**: Meet safety regulatory requirements
- **Transportation**: Comply with gas transportation regulations
- **Storage**: Meet gas storage regulatory requirements

## Related Entities

- [WorkCenter](WorkCenter.md) - The work center where gases are available
- [Gas](Gas.md) - The gases available at work centers
- [GasClassification](GasClassification.md) - Classifications of available gases
- [GasChemicalComposition](GasChemicalComposition.md) - Chemical composition of gases
- [Equipment](Equipment.md) - Equipment that uses the gases (via WorkCenter)

## Database Considerations

- Composite primary key on `WorkCenterId` and `GasId`
- Index on `WorkCenterId` for work center gas queries
- Index on `GasId` for gas location queries
- Foreign key constraints should be properly configured
- Consider adding fields for supply capacity, flow rates, and pressure requirements
- Implement audit triggers for assignment change tracking

## Operational Benefits

1. **Resource Planning**: Understand gas distribution and availability
2. **Supply Optimization**: Optimize gas supply and distribution
3. **Cost Control**: Track and control gas costs by work center
4. **Safety Management**: Ensure proper gas handling and safety
5. **Quality Assurance**: Maintain gas quality and purity standards
6. **Production Planning**: Plan production based on gas availability

## Future Enhancements

Consider adding additional properties for comprehensive gas management:

### Supply Properties
- **Supply Capacity**: Maximum gas supply capacity
- **Flow Rate**: Required gas flow rates
- **Pressure**: Operating pressure requirements
- **Backup Supply**: Backup gas supply availability

### Monitoring Properties
- **Usage Rate**: Real-time gas usage monitoring
- **Efficiency Metrics**: Gas usage efficiency tracking
- **Alert Thresholds**: Low supply alert thresholds
- **Maintenance Schedule**: Gas system maintenance schedules
