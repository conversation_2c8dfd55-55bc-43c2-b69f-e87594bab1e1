﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeClassificationCurrentTypeConfiguration : IEntityTypeConfiguration<ElectrodeClassificationCurrentType>
{
    public void Configure(EntityTypeBuilder<ElectrodeClassificationCurrentType> builder)
    {
        builder.HasKey(e => new { e.ElectrodeClassificationId, e.CurrentType });

        builder.HasIndex(e => e.CurrentType);

        builder.HasOne(e => e.ElectrodeClassification)
               .WithMany()
               .<PERSON>F<PERSON>ign<PERSON>ey(e => e.ElectrodeClassificationId);

    }
}
