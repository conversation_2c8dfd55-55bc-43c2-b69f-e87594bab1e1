# Gas

**Source File:** [Gas.cs](../Gas.cs)

## Overview
The `Gas` entity represents shielding gases, process gases, and gas mixtures used in welding operations. This includes pure gases like argon and helium, as well as gas mixtures for specific welding applications.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the gas |
| `Name` | `string` | Yes | Gas name or designation (max 255 characters) |
| `GasClassificationId` | `Guid` | Yes | Foreign key to the gas classification |
| `GasChemicalCompositionId` | `Guid` | Yes | Foreign key to the chemical composition |
| `CreatedAt` | `DateTime?` | No | Timestamp when the gas was created |
| `CreatedBy` | `Guid?` | No | User who created the gas record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the gas record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `GasClassification` | `GasClassification` | The classification category of this gas |
| `GasChemicalComposition` | `GasChemicalComposition` | The chemical composition of this gas |

## Relationships

### Many-to-One Relationships
- **Gas → GasClassification**: Each gas belongs to one classification
- **Gas → GasChemicalComposition**: Each gas has one chemical composition

### Many-to-Many Relationships (via junction entities)
- **Gas ↔ WorkCenter**: Gases can be available at multiple work centers

## Gas Classifications

Gases are categorized through the `GasClassification` entity which defines:

- **Group**: Primary gas category (e.g., "Inert", "Active", "Mixed")
- **Subgroup**: Specific subcategory within the group

Common gas classifications include:
- **Inert Gases**: Argon, Helium
- **Active Gases**: CO₂, Oxygen
- **Mixed Gases**: Argon/CO₂ mixtures, Argon/Helium mixtures

## Chemical Composition

The `GasChemicalComposition` defines the precise makeup of gases:

| Component | Type | Description |
|-----------|------|-------------|
| `CarbonDioxide` | `decimal?` | CO₂ percentage (5,4 precision) |
| `Oxygen` | `decimal?` | O₂ percentage (5,4 precision) |
| `Argon` | `decimal?` | Ar percentage (5,4 precision) |
| `Helium` | `decimal?` | He percentage (5,4 precision) |

For pure gases, one component will be 100% while others are null or 0. For mixtures, multiple components will have values that sum to 100%.

## Common Gas Types

### Pure Gases
- **Argon (Ar)**: Most common inert shielding gas
- **Helium (He)**: High heat input inert gas
- **Carbon Dioxide (CO₂)**: Active gas for steel welding
- **Nitrogen (N₂)**: Backing gas and plasma cutting

### Gas Mixtures
- **Ar/CO₂ (75/25)**: Common mixture for steel GMAW
- **Ar/CO₂ (90/10)**: Low spatter mixture for steel
- **Ar/He (75/25)**: High heat input mixture for aluminum
- **Ar/O₂ (98/2)**: Stainless steel welding mixture

## Business Rules

1. **Classification Requirement**: Every gas must have a classification
2. **Composition Requirement**: Every gas must have a chemical composition
3. **Composition Validation**: Chemical composition percentages should sum to 100%
4. **Name Uniqueness**: Gas names should be unique within classifications
5. **Purity Standards**: Pure gases should have appropriate purity specifications

## Usage Examples

### Creating a Pure Gas
```csharp
var argon = new Gas
{
    Id = Guid.NewGuid(),
    Name = "Argon 99.99%",
    GasClassificationId = inertGasClassificationId,
    GasChemicalComposition = new GasChemicalComposition
    {
        Argon = 99.99m,
        CarbonDioxide = null,
        Oxygen = null,
        Helium = null
    },
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Creating a Gas Mixture
```csharp
var mixture = new Gas
{
    Name = "Ar/CO₂ 75/25",
    GasClassificationId = mixedGasClassificationId,
    GasChemicalComposition = new GasChemicalComposition
    {
        Argon = 75.0m,
        CarbonDioxide = 25.0m,
        Oxygen = null,
        Helium = null
    }
};
```

## Gas Applications

Different gases are suited for different welding applications:

### GTAW (TIG) Welding
- **Argon**: General purpose, all materials
- **Helium**: High heat input applications
- **Ar/He mixtures**: Balanced heat input and arc stability

### GMAW (MIG) Welding
- **Ar/CO₂ mixtures**: Carbon and low-alloy steels
- **Pure Argon**: Aluminum and non-ferrous metals
- **Ar/O₂ mixtures**: Stainless steels

### Plasma Cutting
- **Compressed Air**: General cutting
- **Nitrogen**: High-quality cuts
- **Argon/Hydrogen**: Stainless steel cutting

## Storage and Safety

Consider implementing additional properties for gas management:

- **Cylinder Size**: Standard cylinder sizes and capacities
- **Pressure Specifications**: Operating pressure ranges
- **Safety Data**: Hazard classifications and handling requirements
- **Storage Requirements**: Temperature and ventilation requirements
- **Purity Specifications**: Minimum purity levels and contaminant limits

## Related Entities

- [GasClassification](GasClassification.md) - Gas category and classification
- [GasChemicalComposition](GasChemicalComposition.md) - Detailed chemical composition
- [WorkCenter](WorkCenter.md) - Work centers where gas is available
- [WorkCenterHasGas](WorkCenterHasGas.md) - Gas availability at work centers

## Database Considerations

- The `Name` property has a maximum length of 255 characters
- Index on `GasClassificationId` for classification-based queries
- Index on `Name` for gas searches
- Foreign key constraints should be properly configured
- Consider adding fields for cylinder tracking, inventory management
- Implement validation to ensure composition percentages are valid
