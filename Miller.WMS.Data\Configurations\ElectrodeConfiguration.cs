﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeConfiguration : IEntityTypeConfiguration<Electrode>
{
    public void Configure(EntityTypeBuilder<Electrode> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.TradeName)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.MetalChemicalComposition)
               .WithMany()
               .HasForeignKey(e => e.MetalChemicalCompositionId);
    }
}