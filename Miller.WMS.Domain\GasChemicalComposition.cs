﻿namespace Miller.WMS.Domain;

public class GasChemicalComposition : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public decimal? CarbonDioxide { get; set; } // (5,4)
    public decimal? Oxygen { get; set; }        // (5,4)
    public decimal? Argon { get; set; }         // (5,4)
    public decimal? Helium { get; set; }        // (5,4)
}
