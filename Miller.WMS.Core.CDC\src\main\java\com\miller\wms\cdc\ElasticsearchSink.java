package com.miller.wms.cdc;

import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch.core.IndexRequest;
import co.elastic.clients.elasticsearch.core.DeleteRequest;
import co.elastic.clients.json.jackson.JacksonJsonpMapper;
import co.elastic.clients.transport.rest_client.RestClientTransport;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.apache.http.HttpHost;
import org.apache.kafka.connect.data.Struct;
import org.apache.kafka.connect.source.SourceRecord;
import org.elasticsearch.client.RestClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * Elasticsearch sink for processing Debezium change events
 * and indexing them into Elasticsearch.
 */
public class ElasticsearchSink {
    private static final Logger logger = LoggerFactory.getLogger(ElasticsearchSink.class);
    
    private final ElasticsearchClient client;
    private final ObjectMapper objectMapper;
    private final String indexName;
    
    public ElasticsearchSink() {
        this.indexName = System.getenv().getOrDefault("ES_INDEX_NAME", "organizations");
        this.objectMapper = new ObjectMapper();
        this.objectMapper.registerModule(new JavaTimeModule());
        
        // Initialize Elasticsearch client
        String esHost = System.getenv().getOrDefault("ES_HOST", "localhost");
        int esPort = Integer.parseInt(System.getenv().getOrDefault("ES_PORT", "9200"));
        
        RestClient restClient = RestClient.builder(
                new HttpHost(esHost, esPort, "http")
        ).build();
        
        RestClientTransport transport = new RestClientTransport(
                restClient, new JacksonJsonpMapper()
        );
        
        this.client = new ElasticsearchClient(transport);
        
        logger.info("Elasticsearch sink initialized for {}:{}, index: {}", esHost, esPort, indexName);
    }
    
    public void processRecord(SourceRecord record) {
        try {
            if (record.value() == null) {
                logger.debug("Skipping null record");
                return;
            }
            
            Struct value = (Struct) record.value();
            String operation = value.getString("op");
            
            switch (operation) {
                case "c": // Create
                case "u": // Update
                    handleUpsert(value);
                    break;
                case "d": // Delete
                    handleDelete(value);
                    break;
                case "r": // Read (initial snapshot)
                    handleUpsert(value);
                    break;
                default:
                    logger.warn("Unknown operation: {}", operation);
            }
            
        } catch (Exception e) {
            logger.error("Error processing record: {}", record, e);
        }
    }
    
    private void handleUpsert(Struct value) throws IOException {
        Struct after = value.getStruct("after");
        if (after == null) {
            logger.warn("No 'after' data in upsert operation");
            return;
        }
        
        // Extract organization data
        Map<String, Object> document = extractOrganizationData(after);
        String id = String.valueOf(document.get("id"));
        
        IndexRequest<Map<String, Object>> request = IndexRequest.of(i -> i
                .index(indexName)
                .id(id)
                .document(document)
        );
        
        client.index(request);
        logger.info("Indexed organization with ID: {}", id);
    }
    
    private void handleDelete(Struct value) throws IOException {
        Struct before = value.getStruct("before");
        if (before == null) {
            logger.warn("No 'before' data in delete operation");
            return;
        }
        
        String id = String.valueOf(before.get("Id"));
        
        DeleteRequest request = DeleteRequest.of(d -> d
                .index(indexName)
                .id(id)
        );
        
        client.delete(request);
        logger.info("Deleted organization with ID: {}", id);
    }
    
    private Map<String, Object> extractOrganizationData(Struct after) {
        Map<String, Object> document = new HashMap<>();
        
        // Extract fields from the Organization table
        // Adjust field names based on your actual Organization entity structure
        if (after.get("Id") != null) {
            document.put("id", after.get("Id"));
        }
        if (after.get("Name") != null) {
            document.put("name", after.get("Name"));
        }
        
        // Add timestamp for indexing
        document.put("indexed_at", System.currentTimeMillis());
        
        logger.debug("Extracted organization document: {}", document);
        return document;
    }
    
    public void close() {
        try {
            if (client != null) {
                client._transport().close();
            }
            logger.info("Elasticsearch sink closed");
        } catch (IOException e) {
            logger.error("Error closing Elasticsearch client", e);
        }
    }
}
