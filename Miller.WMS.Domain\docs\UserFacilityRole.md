# UserFacilityRole

**Source File:** [UserFacilityRole.cs](../UserFacilityRole.cs)

## Overview
The `UserFacilityRole` entity represents the many-to-many relationship between users and facilities, defining what role a user has at a specific facility. This allows users to have different roles at different facilities within an organization.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `UserId` | `Guid` | Yes | Foreign key to the user |
| `FacilityId` | `Guid` | Yes | Foreign key to the facility |
| `RoleAtFacility` | `string` | Yes | The role the user has at this facility |
| `CreatedAt` | `DateTime?` | No | Timestamp when the role was assigned |
| `CreatedBy` | `Guid?` | No | User who created this role assignment |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this role assignment |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `User` | `User` | The user who has this role |
| `Facility` | `Facility` | The facility where the user has this role |

## Relationships

### Many-to-One Relationships
- **UserFacilityRole → User**: Each role assignment belongs to one user
- **UserFacilityRole → Facility**: Each role assignment is for one facility

## Composite Key

This entity uses a composite primary key consisting of:
- `UserId`
- `FacilityId`

This ensures that a user can have only one role per facility, but can have roles at multiple facilities.

## Common Roles

While roles are stored as free-form strings, common standardized roles include:

### Management Roles
- **Administrator**: Full system access and configuration
- **Manager**: Facility-level management and oversight
- **Supervisor**: Work center supervision and coordination

### Operational Roles
- **Lead Operator**: Senior welding operator with training responsibilities
- **Operator**: Certified welding operator
- **Trainee**: Operator in training
- **Helper**: Assistant to operators

### Technical Roles
- **Inspector**: Quality control and inspection duties
- **Engineer**: Process engineering and optimization
- **Technician**: Equipment maintenance and calibration
- **Programmer**: Automated system programming

### Support Roles
- **Maintenance**: Equipment maintenance and repair
- **Safety Officer**: Safety compliance and training
- **Scheduler**: Production planning and scheduling
- **Viewer**: Read-only access for reporting and monitoring

## Business Rules

1. **Unique Role per Facility**: A user can have only one role per facility
2. **Role Validation**: Roles should be validated against approved role lists
3. **Facility Access**: Users must have a role to access a facility
4. **Role Hierarchy**: Consider implementing role hierarchies for permissions
5. **Audit Trail**: Role changes should be tracked for compliance

## Usage Examples

### Assigning a User to Multiple Facilities
```csharp
var userRoles = new List<UserFacilityRole>
{
    new UserFacilityRole
    {
        UserId = userId,
        FacilityId = mainPlantId,
        RoleAtFacility = "Supervisor",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "admin"
    },
    new UserFacilityRole
    {
        UserId = userId,
        FacilityId = warehouseId,
        RoleAtFacility = "Operator",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "admin"
    }
};
```

### Creating Role Assignment with User and Facility
```csharp
var roleAssignment = new UserFacilityRole
{
    User = new User 
    { 
        Name = "John Smith", 
        Email = "<EMAIL>" 
    },
    Facility = new Facility 
    { 
        Name = "Main Plant", 
        Code = "MP001" 
    },
    RoleAtFacility = "Inspector",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "hr_admin"
};
```

## Role-Based Access Control

This entity enables role-based access control (RBAC) by:

1. **Facility-Specific Permissions**: Users have different permissions at different facilities
2. **Role-Based Authorization**: System features are accessible based on user roles
3. **Multi-Facility Support**: Users can work across multiple facilities with appropriate roles
4. **Granular Control**: Fine-grained control over user access and capabilities

## Permission Mapping

Consider implementing a permission mapping system:

```csharp
public static class RolePermissions
{
    public static readonly Dictionary<string, List<string>> Permissions = new()
    {
        ["Administrator"] = new() { "All" },
        ["Manager"] = new() { "ViewReports", "ManageUsers", "ConfigureWorkCenters" },
        ["Supervisor"] = new() { "ViewReports", "ManageOperations", "AssignWork" },
        ["Operator"] = new() { "OperateEquipment", "ViewWorkInstructions", "RecordData" },
        ["Inspector"] = new() { "ViewReports", "RecordInspections", "ApproveWork" },
        ["Viewer"] = new() { "ViewReports", "ViewData" }
    };
}
```

## Temporal Considerations

Consider adding temporal aspects to role assignments:

- **Effective Date**: When the role assignment becomes active
- **Expiration Date**: When the role assignment expires
- **Temporary Assignments**: Short-term role assignments
- **Role History**: Track role changes over time

## Related Entities

- [User](User.md) - The user who has the role
- [Facility](Facility.md) - The facility where the role applies
- [Organization](Organization.md) - The organization context (via User and Facility)

## Database Considerations

- Composite primary key on `UserId` and `FacilityId`
- Index on `UserId` for user-based queries
- Index on `FacilityId` for facility-based queries
- Index on `RoleAtFacility` for role-based queries
- Foreign key constraints should be properly configured
- Consider adding check constraints for valid role values
- Implement audit triggers for role change tracking

## Security Implications

1. **Principle of Least Privilege**: Users should have minimum necessary permissions
2. **Role Segregation**: Conflicting roles should not be assigned to the same user
3. **Regular Review**: Role assignments should be reviewed periodically
4. **Approval Process**: Role changes should require appropriate approval
5. **Emergency Access**: Consider emergency access procedures for critical situations
