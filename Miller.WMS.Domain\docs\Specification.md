# Specification

**Source File:** [Specification.cs](../Specification.cs)

## Overview
The `Specification` entity represents industry standards, codes, and specifications that govern welding operations, materials, and procedures. This includes AWS, ASME, ISO, and other industry standards.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the specification |
| `SpecificationType` | `SpecificationType` | Yes | Category of specification |
| `OtherSpecification` | `string?` | Conditional | Required if Type is "Other" |
| `Code` | `string` | Yes | Specification code or number |
| `Title` | `string` | Yes | Full title of the specification |
| `Description` | `string?` | No | Detailed description of the specification |
| `IssuingOrganizationId` | `Guid` | Yes | Foreign key to the issuing organization |
| `Status` | `SpecificationStatus` | Yes | Current status of the specification |
| `CreatedAt` | `DateTime?` | No | Timestamp when the specification was created |
| `CreatedBy` | `Guid?` | No | User who created the specification record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the specification record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `IssuingOrganization` | `IssuingOrganization` | The organization that issued this specification |
| `SupersedingSpecs` | `ICollection<Specification>` | Specifications that supersede this one |

## Relationships

### Many-to-One Relationships
- **Specification → IssuingOrganization**: Each specification is issued by one organization

### Self-Referencing Relationships
- **Specification → Specification**: Specifications can supersede other specifications

### One-to-Many Relationships
- **Specification → ElectrodeClassification**: Specifications can have multiple electrode classifications
- **Specification → ANumberElectrode**: Specifications can define multiple A-number electrodes

## Specification Types

The `SpecificationType` enumeration defines categories:

- **Weld**: Welding procedure specifications (WPS)
- **Material**: Material specifications and standards
- **Consumable**: Filler metal and consumable specifications
- **Qualification**: Welder and procedure qualification standards
- **Quality**: Quality control and inspection standards
- **Other**: Custom or miscellaneous specifications

## Specification Status

The `SpecificationStatus` enumeration tracks lifecycle:

- **Draft**: Specification under development
- **Active**: Current and valid specification
- **Superseded**: Replaced by newer version
- **Withdrawn**: No longer valid or applicable
- **Under Review**: Being reviewed for updates

## Common Specifications

### AWS (American Welding Society)
- **A5.1**: Carbon Steel Electrodes for Shielded Metal Arc Welding
- **A5.18**: Carbon Steel Electrodes and Rods for Gas Shielded Arc Welding
- **A5.28**: Low-Alloy Steel Electrodes and Rods for Gas Shielded Arc Welding
- **D1.1**: Structural Welding Code - Steel

### ASME (American Society of Mechanical Engineers)
- **Section II**: Materials Specifications
- **Section IX**: Welding and Brazing Qualifications
- **B31.1**: Power Piping Code
- **B31.3**: Process Piping Code

### ISO (International Organization for Standardization)
- **ISO 14341**: Wire electrodes and weld deposits for gas shielded metal arc welding
- **ISO 2560**: Covered electrodes for manual metal arc welding
- **ISO 9606**: Qualification testing of welders

## Business Rules

1. **Issuing Organization**: Every specification must have an issuing organization
2. **Code Uniqueness**: Specification codes should be unique within issuing organizations
3. **Status Management**: Specification status should be actively managed
4. **Supersession Chain**: Superseded specifications should maintain traceability
5. **Other Type Validation**: "Other" specification type requires description

## Usage Examples

### Creating an AWS Specification
```csharp
var awsSpec = new Specification
{
    Id = Guid.NewGuid(),
    SpecificationType = SpecificationType.Consumable,
    Code = "A5.18",
    Title = "Carbon Steel Electrodes and Rods for Gas Shielded Arc Welding",
    Description = "Specification for carbon steel electrodes and rods for GMAW and GTAW",
    IssuingOrganizationId = awsOrganizationId,
    Status = SpecificationStatus.Active,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Creating a Custom Specification
```csharp
var customSpec = new Specification
{
    SpecificationType = SpecificationType.Other,
    OtherSpecification = "Company Internal Standard",
    Code = "CIS-001",
    Title = "Internal Welding Procedure for Aerospace Components",
    Description = "Company-specific welding procedures for critical aerospace applications",
    IssuingOrganizationId = companyOrganizationId,
    Status = SpecificationStatus.Active
};
```

## Specification Relationships

### Electrode Classifications
Specifications define electrode classifications through the `ElectrodeClassification` entity, which includes:
- Performance requirements (yield strength, tensile strength, elongation)
- Welding position capabilities
- Current type compatibility
- Process compatibility

### A-Number Electrodes
Specifications can define A-number electrodes for procedure qualification purposes through the `ANumberElectrode` entity.

### Special Rules
Some specifications have special rules defined through entities like `CatElectrodeSpecialRule` for specific welding applications.

## Version Management

Consider implementing version control for specifications:

- **Version Number**: Track specification revisions
- **Effective Date**: When specification becomes effective
- **Supersession Date**: When specification is superseded
- **Change Summary**: Description of changes between versions

## Related Entities

- [IssuingOrganization](IssuingOrganization.md) - Organization that issued the specification
- [ElectrodeClassification](ElectrodeClassification.md) - Classifications defined by this specification
- [ANumberElectrode](ANumberElectrode.md) - A-number electrodes defined by this specification
- [CatElectrodeSpecialRule](CatElectrodeSpecialRule.md) - Special rules related to this specification

## Database Considerations

- Index on `Code` and `IssuingOrganizationId` for specification lookups
- Index on `SpecificationType` and `Status` for categorization queries
- Consider full-text search on `Title` and `Description`
- Foreign key constraints should be properly configured
- Implement unique constraints on issuing organization + code combinations
- Consider soft delete to maintain historical references
