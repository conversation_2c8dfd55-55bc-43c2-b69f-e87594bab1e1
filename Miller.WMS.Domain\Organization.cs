﻿using System;
using System.Collections.Generic;

namespace Miller.WMS.Domain;

public class Organization : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public required string Name { get; set; }

    public OrganizationIndustryType? IndustryType { get; set; }
    public OrganizationStatus Status { get; set; }
    public ICollection<Facility> Facilities { get; set; } = [];
    public ICollection<User> Users { get; set; } = [];
}