# ElectrodeHasElectrodeClassification

**Source File:** [ElectrodeHasElectrodeClassification.cs](../ElectrodeHasElectrodeClassification.cs)

## Overview
The `ElectrodeHasElectrodeClassification` entity represents the many-to-many relationship between electrodes and their classifications. This junction entity links specific electrodes to the performance classifications they meet, enabling proper electrode selection based on application requirements.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `ElectrodeId` | `Guid` | Yes | Foreign key to the electrode |
| `ElectrodeClassificationId` | `Guid` | Yes | Foreign key to the electrode classification |
| `CreatedAt` | `DateTime?` | No | Timestamp when the relationship was created |
| `CreatedBy` | `Guid?` | No | User who created this relationship |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this relationship |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Electrode` | `Electrode` | The electrode that meets the classification |
| `ElectrodeClassification` | `ElectrodeClassification` | The classification that the electrode meets |

## Relationships

### Many-to-One Relationships
- **ElectrodeHasElectrodeClassification → Electrode**: Each relationship belongs to one electrode
- **ElectrodeHasElectrodeClassification → ElectrodeClassification**: Each relationship is for one classification

## Composite Key

This entity uses a composite primary key consisting of:
- `ElectrodeId`
- `ElectrodeClassificationId`

This ensures that an electrode can only be associated once with a specific classification, but electrodes can meet multiple classifications and classifications can apply to multiple electrodes.

## Classification Relationships

### Single Classification
Most electrodes meet one primary classification:
```csharp
var primaryClassification = new ElectrodeHasElectrodeClassification
{
    ElectrodeId = er70s6ElectrodeId,
    ElectrodeClassificationId = er70s6ClassificationId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "quality_engineer"
};
```

### Multiple Classifications
Some electrodes may meet multiple classifications:
```csharp
var multipleClassifications = new List<ElectrodeHasElectrodeClassification>
{
    new ElectrodeHasElectrodeClassification
    {
        ElectrodeId = dualClassElectrodeId,
        ElectrodeClassificationId = primaryClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "quality_engineer"
    },
    new ElectrodeHasElectrodeClassification
    {
        ElectrodeId = dualClassElectrodeId,
        ElectrodeClassificationId = alternateClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "quality_engineer"
    }
};
```

## Business Rules

1. **Unique Relationships**: An electrode can only be associated once with each classification
2. **Validation Requirements**: Electrode must actually meet the classification requirements
3. **Testing Verification**: Relationships should be backed by test data
4. **Specification Compliance**: Classifications must be from applicable specifications
5. **Quality Assurance**: Regular verification of classification compliance

## Classification Types

### AWS Stick Electrode Classifications
- **E6010**: Cellulosic covering, all-position welding
- **E7018**: Low-hydrogen covering, multiple positions
- **E8018**: High-strength, low-hydrogen covering
- **E9018**: Very high-strength applications

### AWS Wire Electrode Classifications
- **ER70S-6**: Deoxidized wire for general purpose welding
- **ER80S-D2**: Low-alloy wire for high-strength applications
- **ER308L**: Stainless steel wire for austenitic steels
- **ER4043**: Aluminum wire for general purpose welding

### Dual Classifications
Some electrodes may meet multiple classifications:
- **Multi-Standard Compliance**: Meet both AWS and ISO standards
- **Grade Variations**: Meet multiple grades within a specification
- **Application Flexibility**: Suitable for multiple applications

## Verification and Testing

### Mechanical Property Testing
- **Tensile Testing**: Verify yield and tensile strength requirements
- **Impact Testing**: Charpy V-notch testing for toughness
- **Bend Testing**: Verify ductility requirements
- **Hardness Testing**: Verify hardness specifications

### Chemical Analysis
- **Weld Metal Analysis**: Verify chemical composition of deposited metal
- **Diffusible Hydrogen**: Test for hydrogen content in low-hydrogen electrodes
- **Trace Elements**: Verify limits on harmful elements

### Welding Performance
- **Position Testing**: Verify welding position capabilities
- **Current Type Testing**: Verify AC/DC compatibility
- **Process Testing**: Verify welding process compatibility

## Quality Assurance

### Certification Requirements
- **Mill Test Certificates**: Chemical composition certification
- **Mechanical Test Reports**: Mechanical property test results
- **Compliance Certificates**: Certification of specification compliance
- **Third-Party Testing**: Independent verification when required

### Ongoing Monitoring
- **Production Testing**: Regular testing of production lots
- **Statistical Process Control**: Monitor consistency over time
- **Customer Feedback**: Monitor field performance
- **Corrective Actions**: Address any non-conformances

## Usage Examples

### Creating Classification Relationships
```csharp
var classificationRelationships = new List<ElectrodeHasElectrodeClassification>
{
    new ElectrodeHasElectrodeClassification
    {
        ElectrodeId = lincolnElectrodeId,
        ElectrodeClassificationId = e7018ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "quality_manager"
    },
    new ElectrodeHasElectrodeClassification
    {
        ElectrodeId = millerElectrodeId,
        ElectrodeClassificationId = er70s6ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "quality_manager"
    }
};
```

### Bulk Classification Assignment
```csharp
// Assign multiple electrodes to the same classification
var electrodeIds = new[] { electrode1Id, electrode2Id, electrode3Id };
var relationships = electrodeIds.Select(electrodeId => 
    new ElectrodeHasElectrodeClassification
    {
        ElectrodeId = electrodeId,
        ElectrodeClassificationId = commonClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "batch_processor"
    }).ToList();
```

## Electrode Selection

This relationship enables electrode selection based on:

### Application Requirements
- **Strength Requirements**: Select electrodes meeting strength classifications
- **Position Requirements**: Select electrodes qualified for specific positions
- **Process Requirements**: Select electrodes compatible with welding process
- **Code Requirements**: Select electrodes meeting code specifications

### Substitution Analysis
- **Equivalent Classifications**: Find electrodes with equivalent classifications
- **Upgrade Paths**: Identify higher-performance alternatives
- **Availability**: Find available electrodes meeting requirements
- **Cost Optimization**: Balance performance and cost requirements

## Related Entities

- [Electrode](Electrode.md) - The electrodes being classified
- [ElectrodeClassification](ElectrodeClassification.md) - The classifications being met
- [Specification](Specification.md) - The specifications defining classifications
- [Equipment](Equipment.md) - Equipment that may require specific classifications

## Database Considerations

- Composite primary key on `ElectrodeId` and `ElectrodeClassificationId`
- Index on `ElectrodeClassificationId` for classification-based queries
- Index on `ElectrodeId` for electrode-based queries
- Foreign key constraints should be properly configured
- Consider adding fields for test data references and certification information
- Implement audit triggers for relationship change tracking

## Compliance and Traceability

### Regulatory Compliance
- **Code Requirements**: Meet applicable welding code requirements
- **Industry Standards**: Comply with industry standards (AWS, ASME, ISO)
- **Customer Specifications**: Meet customer-specific requirements
- **Quality Systems**: Maintain quality system compliance

### Traceability
- **Test Records**: Link to specific test records and certificates
- **Lot Tracking**: Trace relationships to specific electrode lots
- **Change History**: Maintain history of classification changes
- **Documentation**: Complete documentation of classification basis
