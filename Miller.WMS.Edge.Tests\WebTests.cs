namespace Miller.WMS.Edge.Tests;

[Collection("AspireTestCollection")]
public class WebTests
{
    private readonly AspireTestFixture _fixture;

    public WebTests(AspireTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task GetWebResourceRootReturnsOkStatusCode()
    {
        // Arrange
        var cancellationToken = TestContext.Current.CancellationToken;

        // Act
        var httpClient = _fixture.CreateHttpClient("wms-edge-web");
        await _fixture.WaitForResourceHealthyAsync("wms-edge-web", cancellationToken);
        var response = await httpClient.GetAsync("/", cancellationToken);

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }
}
