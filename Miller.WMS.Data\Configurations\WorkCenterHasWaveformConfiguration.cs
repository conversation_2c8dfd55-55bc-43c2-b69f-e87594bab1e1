﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WorkCenterHasWaveformConfiguration : IEntityTypeConfiguration<WorkCenterHasWaveform>
{
    public void Configure(EntityTypeBuilder<WorkCenterHasWaveform> builder)
    {
        builder.<PERSON><PERSON>ey(e => new { e.WorkCenterId, e.WaveformId });

        builder.HasIndex(e => e.WaveformId);

        builder.HasOne(e => e.WorkCenter)
               .WithMany()
               .HasForeignKey(e => e.WorkCenterId);

        builder.HasOne(e => e.Waveform)
               .WithMany()
               .<PERSON><PERSON><PERSON><PERSON><PERSON>(e => e.WaveformId);
    }
}
