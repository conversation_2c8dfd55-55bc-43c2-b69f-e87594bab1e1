﻿namespace Miller.WMS.Domain;

public class WorkCenterHasGas : IEntityWithAudit
{

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid WorkCenterId { get; set; }
    public required WorkCenter WorkCenter { get; set; }

    public Guid GasId { get; set; }
    public required Gas Gas { get; set; }
}
