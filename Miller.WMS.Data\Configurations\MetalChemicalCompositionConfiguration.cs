﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class MetalChemicalCompositionConfiguration : IEntityTypeConfiguration<MetalChemicalComposition>
{
    public void Configure(EntityTypeBuilder<MetalChemicalComposition> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.MinCarbon).HasPrecision(5, 4);
        builder.Property(e => e.MaxCarbon).HasPrecision(5, 4);
        builder.Property(e => e.MinChromium).HasPrecision(5, 4);
        builder.Property(e => e.MaxChromium).HasPrecision(5, 4);
        builder.Property(e => e.MinMolybdenum).HasPrecision(5, 4);
        builder.Property(e => e.MaxMolybdenum).HasPrecision(5, 4);
        builder.Property(e => e.<PERSON>).HasPrecision(5, 4);
        builder.Property(e => e.<PERSON>).HasPrecision(5, 4);
        builder.Property(e => e.<PERSON>).HasPrecision(5, 4);
        builder.Property(e => e.<PERSON>).HasPrecision(5, 4);
        builder.Property(e => e.MinSilicon).HasPrecision(5, 4);
        builder.Property(e => e.MaxSilicon).HasPrecision(5, 4);
    }
}
