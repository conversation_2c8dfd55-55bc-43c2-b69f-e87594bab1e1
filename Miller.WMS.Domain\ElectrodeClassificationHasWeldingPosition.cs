﻿// Domain entity
namespace Miller.WMS.Domain;
public class ElectrodeClassificationHasWeldingPosition : IEntityWithAudit
{
    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid ElectrodeClassificationId { get; set; }
    public ElectrodeClassification ElectrodeClassification { get; set; } = null!;

    public WeldingPosition WeldingPosition { get; set; }
}
