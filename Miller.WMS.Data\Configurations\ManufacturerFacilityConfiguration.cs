﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ManufacturerFacilityConfiguration : IEntityTypeConfiguration<ManufacturerFacility>
{
    public void Configure(EntityTypeBuilder<ManufacturerFacility> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Address).HasMaxLength(255);
        builder.Property(e => e.City).HasMaxLength(255);
        builder.Property(e => e.State).HasMaxLength(255);
        builder.Property(e => e.ZipCode).HasMaxLength(20);
        builder.Property(e => e.Country).HasMaxLength(255);

        builder.HasOne(e => e.Manufacturer)
               .WithMany()
               .HasForeignKey(e => e.ManufacturerId);
    }
}
