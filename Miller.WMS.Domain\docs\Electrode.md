# Electrode

**Source File:** [Electrode.cs](../Electrode.cs)

## Overview
The `Electrode` entity represents welding electrodes and filler metals used in welding operations. This includes stick electrodes, filler wires, and other consumable welding materials.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the electrode |
| `TradeName` | `string` | Yes | Commercial/trade name of the electrode |
| `MetalChemicalCompositionId` | `Guid` | Yes | Foreign key to the chemical composition |
| `CreatedAt` | `DateTime?` | No | Timestamp when the electrode was created |
| `CreatedBy` | `Guid?` | No | User who created the electrode record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the electrode record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `MetalChemicalComposition` | `MetalChemicalComposition` | The chemical composition of the electrode metal |

## Relationships

### Many-to-One Relationships
- **Electrode → MetalChemicalComposition**: Each electrode has one chemical composition

### Many-to-Many Relationships (via junction entities)
- **Electrode ↔ ElectrodeClassification**: Electrodes can have multiple classifications
- **Electrode ↔ Manufacturer**: Electrodes can be made by multiple manufacturers
- **Electrode ↔ WorkCenter**: Electrodes can be available at multiple work centers

## Electrode Types

Electrodes are categorized by the `ElectrodeType` enumeration:

- **FillerMetal**: Filler wires and rods for GTAW, GMAW
- **Stick**: Covered electrodes for SMAW (stick welding)
- **Tungsten**: Non-consumable tungsten electrodes for GTAW

## Chemical Composition

Each electrode has an associated `MetalChemicalComposition` that defines:

- **Carbon Content**: Min/max carbon percentages
- **Chromium Content**: Min/max chromium percentages
- **Molybdenum Content**: Min/max molybdenum percentages
- **Nickel Content**: Min/max nickel percentages
- **Manganese Content**: Min/max manganese percentages
- **Silicon Content**: Min/max silicon percentages

This composition determines the electrode's metallurgical properties and suitability for specific applications.

## Electrode Classifications

Electrodes can have multiple classifications through the `ElectrodeHasElectrodeClassification` relationship. Classifications define:

- **Performance Characteristics**: Yield strength, tensile strength, elongation
- **Welding Positions**: Supported welding positions
- **Current Types**: Compatible current types (AC, DCEN, DCEP)
- **Welding Processes**: Compatible welding processes
- **Covering Types**: For stick electrodes (rutile, cellulosic, basic, etc.)

## Business Rules

1. **Chemical Composition**: Every electrode must have an associated chemical composition
2. **Trade Name Uniqueness**: Trade names should be unique within manufacturers
3. **Classification Consistency**: Electrode classifications must be consistent with chemical composition
4. **Diameter Availability**: Electrodes should have defined available diameters
5. **Manufacturer Relationships**: Electrodes must be associated with manufacturers

## Usage Examples

### Creating a New Electrode
```csharp
var electrode = new Electrode
{
    Id = Guid.NewGuid(),
    TradeName = "ER70S-6",
    MetalChemicalCompositionId = compositionId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Electrode with Chemical Composition
```csharp
var electrode = new Electrode
{
    TradeName = "E7018",
    MetalChemicalComposition = new MetalChemicalComposition
    {
        MinCarbon = 0.05m,
        MaxCarbon = 0.15m,
        MinManganese = 0.80m,
        MaxManganese = 1.60m,
        MinSilicon = 0.15m,
        MaxSilicon = 0.35m
    }
};
```

## Electrode Specifications

Electrodes are often classified according to industry specifications such as:

- **AWS (American Welding Society)**: A5.1, A5.18, A5.28, etc.
- **ASME**: Section II specifications
- **ISO**: International standards
- **EN**: European standards

These specifications are managed through the `Specification` and `ElectrodeClassification` entities.

## Available Diameters

Electrode diameters are managed through the `ElectrodeDiameter` entity, which defines:
- Available diameters for specific electrode types
- Precision specifications (typically 6,3 decimal places)

## Manufacturing Information

Electrode manufacturing is tracked through:
- **ElectrodeMadeByManufacturer**: Links electrodes to their manufacturers
- **Manufacturer**: Contains manufacturer details and capabilities

## Related Entities

- [MetalChemicalComposition](MetalChemicalComposition.md) - Chemical composition details
- [ElectrodeClassification](ElectrodeClassification.md) - Performance classifications
- [ElectrodeHasElectrodeClassification](ElectrodeHasElectrodeClassification.md) - Classification relationships
- [ElectrodeMadeByManufacturer](ElectrodeMadeByManufacturer.md) - Manufacturing relationships
- [ElectrodeDiameter](ElectrodeDiameter.md) - Available diameters
- [WorkCenter](WorkCenter.md) - Work centers where electrodes are available
- [WorkCenterHasElectrode](WorkCenterHasElectrode.md) - Work center availability
- [Manufacturer](Manufacturer.md) - Electrode manufacturers
- [Specification](Specification.md) - Industry specifications

## Database Considerations

- Index on `TradeName` for electrode searches
- Index on `MetalChemicalCompositionId` for composition-based queries
- Consider full-text search capabilities for trade name searches
- Foreign key constraints should be properly configured
- Consider adding fields for electrode packaging, storage requirements, etc.
