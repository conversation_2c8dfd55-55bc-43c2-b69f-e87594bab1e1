﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.Domain;

public class Electrode : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public string TradeName { get; set; } = null!;

    public Guid MetalChemicalCompositionId { get; set; }
    public MetalChemicalComposition MetalChemicalComposition { get; set; } = null!;
}