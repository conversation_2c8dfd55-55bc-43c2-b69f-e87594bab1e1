﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class EquipmentHasCurrentTypeConfiguration : IEntityTypeConfiguration<EquipmentHasCurrentType>
{
    public void Configure(EntityTypeBuilder<EquipmentHasCurrentType> builder)
    {
        builder.HasKey(e => new { e.EquipmentId, e.CurrentType });


        builder.HasOne(e => e.Equipment)
               .WithMany()
               .HasForeignKey(e => e.EquipmentId);
    }
}
