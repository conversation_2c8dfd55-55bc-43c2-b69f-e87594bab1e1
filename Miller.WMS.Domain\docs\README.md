# Miller WMS Domain Documentation

This documentation describes the domain model for the Miller Welding Management System (WMS). The domain is organized around welding operations, equipment, materials, and organizational structures.

## Domain Overview

The Miller WMS domain models a comprehensive welding management system that tracks:

- **Organizations and Facilities**: Companies, their facilities, and users
- **Welding Equipment**: Power supplies, torches, feeders, and other welding equipment
- **Welding Materials**: Electrodes, gases, fluxes, and their specifications
- **Work Centers**: Physical locations where welding operations occur
- **Specifications and Standards**: Industry standards and classifications
- **Chemical Compositions**: Detailed chemical makeup of materials

## Core Interfaces

All domain entities implement one or both of these core interfaces:

- **`IEntityWithId`**: Provides a `Guid Id` property for unique identification
- **`IEntityWithAudit`**: Provides audit fields (`CreatedAt`, `CreatedBy`, `ModifiedAt`, `ModifiedBy`)

## Entity Categories

### 1. Organizational Entities
- [Organization](Organization.md) - Companies and business entities
- [Facility](Facility.md) - Physical locations and sites
- [User](User.md) - System users and their roles
- [Customer](Customer.md) - External customers
- [Manufacturer](Manufacturer.md) - Equipment and material manufacturers

### 2. Equipment Entities
- [Equipment](Equipment.md) - Welding equipment and machinery
- [WorkCenter](WorkCenter.md) - Physical work locations with equipment

### 3. Material Entities
- [Electrode](Electrode.md) - Welding electrodes and filler metals
- [Gas](Gas.md) - Shielding and process gases
- [Flux](Flux.md) - Welding fluxes and compounds
- [Material](Material.md) - Base materials and specifications

### 4. Classification and Specification Entities
- [Specification](Specification.md) - Industry standards and specifications
- [ElectrodeClassification](ElectrodeClassification.md) - Electrode performance classifications
- [TungstenElectrodeClassification](TungstenElectrodeClassification.md) - Tungsten electrode specifications
- [IssuingOrganization](IssuingOrganization.md) - Standards organizations
- [GasClassification](GasClassification.md) - Gas categorization system
- [FluxClass](FluxClass.md) - Flux classification categories

### 5. Chemical Composition Entities
- [MetalChemicalComposition](MetalChemicalComposition.md) - Metal alloy compositions
- [GasChemicalComposition](GasChemicalComposition.md) - Gas mixture compositions
- [FluxChemicalComposition](FluxChemicalComposition.md) - Flux chemical composition classifications
- [FluxChemicalCompositionLimits](FluxChemicalCompositionLimits.md) - Chemical constituent limits for flux compositions

### 6. Process and Configuration Entities
- [Waveform](Waveform.md) - Welding waveform configurations

### 7. Facility Structure Entities
- [FacilityAreaLevelOne](FacilityAreaLevelOne.md) - Top-level facility areas
- [FacilityAreaLevelTwo](FacilityAreaLevelTwo.md) - Second-level facility areas
- [FacilityAreaLevelThree](FacilityAreaLevelThree.md) - Third-level facility areas

### 8. Relationship/Junction Entities
- [UserFacilityRole](UserFacilityRole.md) - User roles at specific facilities
- [CustomerFacility](CustomerFacility.md) - Customer facility relationships
- [ManufacturerFacility](ManufacturerFacility.md) - Manufacturer facility relationships
- [WorkCenterHasEquipment](WorkCenterHasEquipment.md) - Equipment assignments to work centers
- [WorkCenterHasElectrode](WorkCenterHasElectrode.md) - Electrode availability at work centers
- [WorkCenterHasGas](WorkCenterHasGas.md) - Gas availability at work centers
- [WorkCenterHasWaveform](WorkCenterHasWaveform.md) - Waveform configurations at work centers
- [EquipmentHasElectrodeClassification](EquipmentHasElectrodeClassification.md) - Equipment electrode compatibility
- [ElectrodeHasElectrodeClassification](ElectrodeHasElectrodeClassification.md) - Electrode classification relationships
- [ElectrodeMadeByManufacturer](ElectrodeMadeByManufacturer.md) - Electrode manufacturer relationships
- [ElectrodeClassificationHasCurrentType](ElectrodeClassificationHasCurrentType.md) - Current type compatibility
- [ElectrodeClassificationHasWeldingPosition](ElectrodeClassificationHasWeldingPosition.md) - Position compatibility
- [ElectrodeClassificationHasWeldingProcess](ElectrodeClassificationHasWeldingProcess.md) - Process compatibility
- [FluxHasFluxClass](FluxHasFluxClass.md) - Flux classification relationships
- [MaterialSubstitution](MaterialSubstitution.md) - Material substitution rules
- [CatElectrodeSpecialRule](CatElectrodeSpecialRule.md) - Special electrode rules

### 9. Supporting Entities
- [ElectrodeDiameter](ElectrodeDiameter.md) - Available electrode diameters
- [ANumberElectrode](ANumberElectrode.md) - A-Number electrode classifications
- [SupplementalFillerMetal](SupplementalFillerMetal.md) - Additional filler materials
- [TungstenElectrodeClassification](TungstenElectrodeClassification.md) - Tungsten electrode specifications
- [MaterialSubstitution](MaterialSubstitution.md) - Material substitution rules
- [FluxClass](FluxClass.md) - Flux classification categories
- [FluxHasFluxClass](FluxHasFluxClass.md) - Flux classification relationships
- [FluxChemicalCompositionLimits](FluxChemicalCompositionLimits.md) - Chemical constituent limits
- [CustomerFacility](CustomerFacility.md) - Customer facility locations and details
- [ManufacturerFacility](ManufacturerFacility.md) - Manufacturer facility locations

## Enumerations

The domain includes several enumerations that define valid values for various properties:

### Core Welding Enumerations
- [CurrentType](CurrentType.md) - Welding current types (DCEN, DCEP, AC)
- [WeldingPosition](WeldingPosition.md) - Standard welding positions (Flat, Horizontal, Vertical, Overhead, etc.)
- [WeldingProcess](WeldingProcess.md) - Welding processes (OFW, SMAW, GTAW, GMAW, PAW, FCAW, SAW, etc.)

### Electrode and Material Enumerations
- [ElectrodeType](ElectrodeType.md) - Electrode categories (FillerMetal, Stick, Tungsten)
- [ElectrodeCoveringType](ElectrodeCoveringType.md) - Stick electrode coverings (RutileFlux, CellulosicFlux, BasicFlux, AcidFlux)

### Equipment and Status Enumerations
- [EquipmentStatus](EquipmentStatus.md) - Equipment operational status (Active, Inactive, Maintenance, Repair, Retired)
- [EquipmentSubType](EquipmentSubType.md) - Equipment subcategories (PowerSupply, Torch, Feeder, ContactTip, etc.)
- [EquipmentType](EquipmentType.md) - Equipment main categories (Welding, Machining, Assembly, Inspection, etc.)

### Organization and Business Enumerations
- [ManufacturerType](ManufacturerType.md) - Manufacturer specializations (PowerSupply, Electrode, Gas)
- [MaterialSubstitutionApprovalType](MaterialSubstitutionApprovalType.md) - Material substitution approval levels (A, B, C)
- [OrganizationIndustryType](OrganizationIndustryType.md) - Industry classifications (Manufacturing, ConstructionFabrication)
- [OrganizationStatus](OrganizationStatus.md) - Organization operational status (Active, Inactive, Suspended)
- [SpecificationStatus](SpecificationStatus.md) - Specification lifecycle status (Active, Inactive, Superseded)
- [SpecificationType](SpecificationType.md) - Specification categories (Weld, Material, Consumable, Qualification, Quality, Other)
- [WorkCenterStatus](WorkCenterStatus.md) - Work center operational status (Active, Maintenance, Idle, Inactive)
- [WorkCenterType](WorkCenterType.md) - Work center classifications (Welding, Assembly, Inspection, Other)

## Domain Relationships

The domain model includes complex relationships between entities:

1. **Hierarchical Relationships**: Organizations → Facilities → Areas → Work Centers
2. **Material Relationships**: Electrodes ↔ Classifications ↔ Specifications
3. **Equipment Relationships**: Equipment ↔ Work Centers ↔ Capabilities
4. **Chemical Relationships**: Materials ↔ Chemical Compositions ↔ Limits
5. **Process Relationships**: Work Centers ↔ Processes ↔ Positions ↔ Current Types

## Getting Started

To understand the domain model:

1. Start with [Organization](Organization.md) and [Facility](Facility.md) to understand the organizational structure
2. Review [WorkCenter](WorkCenter.md) to understand where work happens
3. Explore [Equipment](Equipment.md), [Electrode](Electrode.md), and [Gas](Gas.md) for the physical components
4. Study [Specification](Specification.md) and [ElectrodeClassification](ElectrodeClassification.md) for standards and classifications
5. Examine the relationship entities to understand how components work together

Each entity documentation includes:
- Purpose and description
- Properties and their types
- Relationships to other entities
- Business rules and constraints
- Usage examples where applicable
