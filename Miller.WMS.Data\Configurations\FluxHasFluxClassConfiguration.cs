﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FluxHasFluxClassConfiguration : IEntityTypeConfiguration<FluxHasFluxClass>
{
    public void Configure(EntityTypeBuilder<FluxHasFluxClass> builder)
    {
        builder.<PERSON><PERSON><PERSON>(e => new { e.FluxId, e.FluxClassId });

        builder.HasIndex(e => e.FluxClassId);

        builder.HasOne(e => e.Flux)
               .WithMany()
               .HasForeignKey(e => e.FluxId);

        builder.HasOne(e => e.FluxClass)
               .WithMany()
               .HasForeignKey(e => e.FluxClassId);
    }
}
