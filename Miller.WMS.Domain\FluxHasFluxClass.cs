﻿namespace Miller.WMS.Domain;

public class FluxHasFluxClass : IEntityWithAudit
{

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid FluxId { get; set; }
    public required Flux Flux { get; set; }

    public Guid FluxClassId { get; set; }
    public required FluxClass FluxClass { get; set; }
}
