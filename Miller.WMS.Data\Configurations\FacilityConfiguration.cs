﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class FacilityConfiguration : IEntityTypeConfiguration<Facility>
{
    public void Configure(EntityTypeBuilder<Facility> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(50)
               .IsRequired();

        builder.Property(e => e.Code)
               .HasMaxLength(50);

        builder.Property(e => e.Address)
               .HasMaxLength(255);

        builder.HasOne(e => e.Organization)
               .WithMany(o => o.Facilities)
               .HasForeignKey(e => e.OrganizationId);

        builder.HasMany(e => e.UserFacilityRoles)
               .WithOne(ufr => ufr.Facility)
               .HasForeignKey(ufr => ufr.FacilityId);
    }
}
