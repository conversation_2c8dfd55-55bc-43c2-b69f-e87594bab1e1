# ElectrodeClassification

**Source File:** [ElectrodeClassification.cs](../ElectrodeClassification.cs)

## Overview
The `ElectrodeClassification` entity represents performance classifications for welding electrodes as defined by industry specifications. These classifications specify mechanical properties, welding capabilities, and application requirements.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the classification |
| `SpecificationId` | `Guid` | Yes | Foreign key to the governing specification |
| `Classification` | `string` | Yes | Classification designation (e.g., "E7018", "ER70S-6") |
| `YieldStrength` | `decimal` | Yes | Minimum yield strength (6,3 precision) |
| `TensileStrength` | `decimal` | Yes | Minimum tensile strength (6,3 precision) |
| `Elongation` | `decimal` | Yes | Minimum elongation percentage (5,4 precision) |
| `CoveringType` | `ElectrodeCoveringType?` | No | Electrode covering type (for stick electrodes) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the classification was created |
| `CreatedBy` | `Guid?` | No | User who created the classification record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the classification record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Specification` | `Specification` | The specification that defines this classification |

## Relationships

### Many-to-One Relationships
- **ElectrodeClassification → Specification**: Each classification belongs to one specification

### Many-to-Many Relationships (via junction entities)
- **ElectrodeClassification ↔ Electrode**: Classifications can apply to multiple electrodes
- **ElectrodeClassification ↔ CurrentType**: Classifications support specific current types
- **ElectrodeClassification ↔ WeldingPosition**: Classifications support specific welding positions
- **ElectrodeClassification ↔ WeldingProcess**: Classifications support specific welding processes
- **ElectrodeClassification ↔ Equipment**: Equipment may be compatible with specific classifications

## Mechanical Properties

### Yield Strength
- Minimum yield strength in PSI or MPa
- Critical for structural applications
- Precision: 6 digits total, 3 decimal places

### Tensile Strength
- Minimum tensile strength in PSI or MPa
- Indicates maximum load capacity
- Precision: 6 digits total, 3 decimal places

### Elongation
- Minimum elongation percentage
- Indicates ductility and toughness
- Precision: 5 digits total, 4 decimal places

## Electrode Covering Types

For stick electrodes, the `ElectrodeCoveringType` enumeration defines:

- **RutileFlux**: Rutile-based covering (easy arc starting, smooth operation)
- **CellulosicFlux**: Cellulosic covering (deep penetration, all-position)
- **BasicFlux**: Basic/low-hydrogen covering (high strength, low hydrogen)
- **IronPowderFlux**: Iron powder covering (high deposition rate)
- **Other**: Custom or specialized coverings

## Classification Examples

### AWS A5.1 Stick Electrodes
- **E6010**: Cellulosic covering, all-position, DCEP
- **E7018**: Low-hydrogen covering, multiple positions, AC/DC
- **E8018**: High-strength, low-hydrogen covering

### AWS A5.18 GMAW Electrodes
- **ER70S-6**: Deoxidized wire for general purpose welding
- **ER80S-D2**: Low-alloy wire for high-strength applications
- **ER308L**: Stainless steel wire for austenitic steels

### AWS A5.28 Low-Alloy Electrodes
- **ER80S-B2**: Chrome-moly wire for elevated temperature service
- **ER90S-B3**: High-strength wire for pressure vessel applications

## Welding Capabilities

Classifications define welding capabilities through relationship entities:

### Current Type Compatibility
Via `ElectrodeClassificationHasCurrentType`:
- **DCEN**: Direct Current Electrode Negative
- **DCEP**: Direct Current Electrode Positive  
- **AC**: Alternating Current

### Welding Position Compatibility
Via `ElectrodeClassificationHasWeldingPosition`:
- **Flat**: 1G, 1F positions
- **Horizontal**: 2G, 2F positions
- **Vertical**: 3G, 3F positions
- **Overhead**: 4G, 4F positions
- **HorizontalFixed**: Fixed horizontal positions
- **Inclined**: Inclined positions

### Welding Process Compatibility
Via `ElectrodeClassificationHasWeldingProcess`:
- **SMAW**: Shielded Metal Arc Welding (stick)
- **GTAW**: Gas Tungsten Arc Welding (TIG)
- **GMAW**: Gas Metal Arc Welding (MIG)
- **FCAW**: Flux-Cored Arc Welding
- **SAW**: Submerged Arc Welding

## Business Rules

1. **Specification Association**: Every classification must belong to a specification
2. **Classification Uniqueness**: Classifications should be unique within specifications
3. **Mechanical Properties**: All mechanical properties are required and must be positive
4. **Covering Type Logic**: Covering type is relevant only for stick electrodes
5. **Capability Consistency**: Welding capabilities must be consistent with electrode type

## Usage Examples

### Creating a Stick Electrode Classification
```csharp
var e7018 = new ElectrodeClassification
{
    Id = Guid.NewGuid(),
    SpecificationId = aws51SpecId,
    Classification = "E7018",
    YieldStrength = 58000m,
    TensileStrength = 70000m,
    Elongation = 22.0m,
    CoveringType = ElectrodeCoveringType.BasicFlux,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Creating a GMAW Wire Classification
```csharp
var er70s6 = new ElectrodeClassification
{
    SpecificationId = aws518SpecId,
    Classification = "ER70S-6",
    YieldStrength = 58000m,
    TensileStrength = 70000m,
    Elongation = 22.0m,
    CoveringType = null // Not applicable for wire
};
```

## Quality Requirements

Classifications may include additional quality requirements:

- **Impact Toughness**: Charpy V-notch requirements
- **Chemical Composition**: Limits on carbon, sulfur, phosphorus
- **Hydrogen Content**: Maximum diffusible hydrogen levels
- **Radiographic Quality**: Weld quality requirements

## Related Entities

- [Specification](Specification.md) - The governing specification
- [Electrode](Electrode.md) - Electrodes that meet this classification
- [ElectrodeHasElectrodeClassification](ElectrodeHasElectrodeClassification.md) - Electrode-classification relationships
- [ElectrodeClassificationHasCurrentType](ElectrodeClassificationHasCurrentType.md) - Current type compatibility
- [ElectrodeClassificationHasWeldingPosition](ElectrodeClassificationHasWeldingPosition.md) - Position compatibility
- [ElectrodeClassificationHasWeldingProcess](ElectrodeClassificationHasWeldingProcess.md) - Process compatibility
- [EquipmentHasElectrodeClassification](EquipmentHasElectrodeClassification.md) - Equipment compatibility

## Database Considerations

- Index on `SpecificationId` for specification-based queries
- Index on `Classification` for classification lookups
- Consider composite index on specification + classification
- Ensure proper decimal precision for mechanical properties
- Foreign key constraints should be properly configured
- Consider adding check constraints for positive mechanical properties
