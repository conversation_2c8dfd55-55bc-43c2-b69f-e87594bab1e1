# Facility

**Source File:** [Facility.cs](../Facility.cs)

## Overview
The `Facility` entity represents physical locations, sites, or buildings where welding operations and business activities take place. Facilities belong to organizations and contain work centers, equipment, and users.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the facility |
| `Name` | `string` | Yes | Facility name |
| `Code` | `string?` | No | Optional facility code for identification |
| `Address` | `string?` | No | Physical address of the facility |
| `OrganizationId` | `Guid` | Yes | Foreign key to the owning organization |
| `CreatedAt` | `DateTime?` | No | Timestamp when the facility was created |
| `CreatedBy` | `Guid?` | No | User who created the facility record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the facility record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Organization` | `Organization?` | The organization that owns this facility |
| `UserFacilityRoles` | `ICollection<UserFacilityRole>` | Collection of user roles at this facility |

## Relationships

### Many-to-One Relationships
- **Facility → Organization**: Each facility belongs to one organization

### One-to-Many Relationships
- **Facility → UserFacilityRoles**: A facility can have multiple users with different roles
- **Facility → WorkCenters**: A facility can contain multiple work centers
- **Facility → FacilityAreaLevelOne**: A facility can be divided into multiple level-one areas

## Business Rules

1. **Organization Association**: Every facility must belong to an organization
2. **Code Uniqueness**: If facility codes are used, they should be unique within an organization
3. **Address Validation**: Physical addresses should be validated for accuracy
4. **Hierarchical Structure**: Facilities can be organized into hierarchical areas (Level 1, 2, 3)

## Usage Examples

### Creating a New Facility
```csharp
var facility = new Facility
{
    Id = Guid.NewGuid(),
    Name = "Main Manufacturing Plant",
    Code = "MMP001",
    Address = "123 Industrial Blvd, Manufacturing City, ST 12345",
    OrganizationId = organizationId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Facility with User Roles
```csharp
var facility = new Facility
{
    Name = "Welding Shop",
    Code = "WS001",
    OrganizationId = organizationId,
    UserFacilityRoles = new List<UserFacilityRole>
    {
        new UserFacilityRole 
        { 
            UserId = managerId, 
            RoleAtFacility = "Manager" 
        },
        new UserFacilityRole 
        { 
            UserId = supervisorId, 
            RoleAtFacility = "Supervisor" 
        }
    }
};
```

## Facility Area Hierarchy

Facilities can be organized into a three-level hierarchical structure:

1. **Level One Areas**: Top-level divisions (e.g., "Production Floor", "Quality Lab")
2. **Level Two Areas**: Sub-divisions within level one (e.g., "Welding Bay A", "Welding Bay B")
3. **Level Three Areas**: Specific locations within level two (e.g., "Station 1", "Station 2")

This hierarchy helps organize work centers and equipment within large facilities.

## Related Entities

- [Organization](Organization.md) - The owning organization
- [User](User.md) - Users who work at this facility
- [UserFacilityRole](UserFacilityRole.md) - User roles at this facility
- [WorkCenter](WorkCenter.md) - Work centers located in this facility
- [FacilityAreaLevelOne](FacilityAreaLevelOne.md) - Top-level areas within the facility
- [CustomerFacility](CustomerFacility.md) - Customer facility relationships
- [ManufacturerFacility](ManufacturerFacility.md) - Manufacturer facility relationships

## Database Considerations

- The `Name` property should have appropriate length constraints (typically 255 characters)
- Consider indexing on `OrganizationId` for query performance
- The `Code` field should be indexed if used for lookups
- Foreign key constraints should be properly configured
- Consider soft delete patterns to maintain historical data integrity
