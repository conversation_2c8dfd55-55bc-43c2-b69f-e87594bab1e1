﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class GasConfiguration : IEntityTypeConfiguration<Gas>
{
    public void Configure(EntityTypeBuilder<Gas> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.HasOne(e => e.GasClassification)
               .WithMany()
               .HasForeignKey(e => e.GasClassificationId);

        builder.HasOne(e => e.GasChemicalComposition)
               .WithMany()
               .HasForeignKey(e => e.GasChemicalCompositionId);
    }
}
