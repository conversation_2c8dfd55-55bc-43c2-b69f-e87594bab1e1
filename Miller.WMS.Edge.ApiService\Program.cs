using <PERSON>;
using Miller.WMS.Shared.Data;

var builder = WebApplication.CreateBuilder(args);

// Add service defaults & Aspire client integrations.
builder.AddServiceDefaults();

// Add services to the container.
builder.Services.AddProblemDetails();

// Add Entity Framework with Aspire-managed PostgreSQL
builder.AddNpgsqlDbContext<WmsContext>("wms");

// Add Carter
builder.Services.AddCarter();

// Learn more about configuring OpenAPI at https://aka.ms/aspnet/openapi
builder.Services.AddOpenApi();

var app = builder.Build();

// Configure the HTTP request pipeline.
app.UseExceptionHandler();

if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
}

// Map Carter modules
app.MapCarter();

// Health check endpoint
app.MapGet("/ping", () => "true")
    .WithName("Ping")
    .WithTags("Health")
    .WithSummary("Health check endpoint")
    .WithDescription("Returns 'true' if the service is running");

app.MapDefaultEndpoints();

app.Run();
