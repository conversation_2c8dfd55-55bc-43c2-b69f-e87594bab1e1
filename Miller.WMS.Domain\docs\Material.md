# Material

**Source File:** [Material.cs](../Material.cs)

## Overview
The `Material` entity represents base materials used in welding operations, including metals, alloys, and other materials that are welded, cut, or processed. This entity tracks material specifications, properties, and relationships.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the material |
| `Spec` | `string` | Yes | Material specification designation |
| `FulfilledById` | `Guid?` | No | Self-reference to superseding material |
| `MaterialForm` | `string?` | No | Physical form of the material (plate, pipe, etc.) |
| `Hardness` | `decimal?` | No | Material hardness (5,2 precision) |
| `MaterialDiameter` | `decimal?` | No | Material diameter for round sections (6,3 precision) |
| `MaterialThickness` | `decimal?` | No | Material thickness (6,3 precision) |
| `BaseMetalGroup` | `string` | Yes | Primary base metal group classification |
| `BaseMetalSubGroup` | `string` | Yes | Secondary base metal group classification |
| `CharpyRequirement` | `bool` | Yes | Whether Charpy impact testing is required |
| `WeldCarbonEquivalent` | `decimal?` | No | Carbon equivalent for weldability (5,2 precision) |
| `WeldMinYieldStrength` | `int?` | No | Minimum yield strength for welding |
| `Class` | `string` | Yes | Material class designation |
| `Grade` | `string` | Yes | Material grade designation |
| `Condition` | `string` | Yes | Material condition (heat treatment state) |
| `HeatTreatment` | `string?` | No | Required heat treatment |
| `ForgeMaterial` | `string?` | No | Forging material designation |
| `CreatedAt` | `DateTime?` | No | Timestamp when the material was created |
| `CreatedBy` | `Guid?` | No | User who created the material record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the material record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `FulfilledBy` | `Material?` | Material that supersedes this one |

## Relationships

### Self-Referencing Relationships
- **Material → Material**: Materials can be superseded by newer specifications

### One-to-Many Relationships
- **Material → MaterialSubstitution**: Materials can have approved substitutions

## Material Classifications

### Base Metal Groups
Common base metal group classifications:

- **Group I**: Carbon and Low-Alloy Steels
- **Group II**: Austenitic Stainless Steels
- **Group III**: Duplex and Super Duplex Stainless Steels
- **Group IV**: Nickel and Nickel Alloys
- **Group V**: Titanium and Titanium Alloys
- **Group VI**: Aluminum and Aluminum Alloys
- **Group VII**: Copper and Copper Alloys

### Material Forms
- **Plate**: Flat rolled products
- **Pipe**: Hollow cylindrical products
- **Tube**: Precision hollow products
- **Bar**: Solid round, square, or rectangular products
- **Sheet**: Thin flat rolled products
- **Forging**: Forged products
- **Casting**: Cast products

### Material Conditions
- **Annealed**: Soft, stress-relieved condition
- **Normalized**: Normalized heat treatment
- **Quenched and Tempered**: Q&T heat treatment
- **Solution Annealed**: Stainless steel solution treatment
- **Aged**: Precipitation hardened condition
- **As-Welded**: No post-weld heat treatment
- **Stress Relieved**: Post-weld stress relief

## Material Properties

### Mechanical Properties
- **Yield Strength**: Minimum yield strength for welding applications
- **Tensile Strength**: Ultimate tensile strength
- **Hardness**: Material hardness (Brinell, Rockwell, etc.)
- **Impact Toughness**: Charpy V-notch requirements

### Physical Properties
- **Thickness**: Material thickness for plates and sheets
- **Diameter**: Outside diameter for pipes and tubes
- **Wall Thickness**: Wall thickness for hollow products
- **Length**: Standard lengths available

### Chemical Properties
- **Carbon Equivalent**: Weldability indicator
- **Chemical Composition**: Detailed chemical analysis
- **Impurity Limits**: Maximum allowable impurities
- **Trace Elements**: Controlled trace element content

## Business Rules

1. **Specification Uniqueness**: Material specifications should be unique
2. **Group Classification**: Base metal groups must follow industry standards
3. **Weldability Assessment**: Carbon equivalent should be calculated for weldable materials
4. **Charpy Requirements**: Impact testing requirements must be specified
5. **Supersession Tracking**: Material supersession relationships must be maintained

## Usage Examples

### Creating a Carbon Steel Material
```csharp
var carbonSteel = new Material
{
    Id = Guid.NewGuid(),
    Spec = "ASTM A36",
    MaterialForm = "Plate",
    Hardness = 120m,
    MaterialThickness = 25.4m,
    BaseMetalGroup = "Group I",
    BaseMetalSubGroup = "Carbon Steel",
    CharpyRequirement = false,
    WeldCarbonEquivalent = 0.42m,
    WeldMinYieldStrength = 36000,
    Class = "Structural",
    Grade = "36",
    Condition = "As-Rolled",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "materials_engineer"
};
```

### Creating a Stainless Steel Material
```csharp
var stainlessSteel = new Material
{
    Spec = "ASTM A240 Type 316L",
    MaterialForm = "Plate",
    MaterialThickness = 12.7m,
    BaseMetalGroup = "Group II",
    BaseMetalSubGroup = "Austenitic Stainless",
    CharpyRequirement = true,
    Class = "Corrosion Resistant",
    Grade = "316L",
    Condition = "Solution Annealed",
    HeatTreatment = "Solution Annealed at 1900-2100°F"
};
```

### Creating a Pipe Material
```csharp
var pipeMaterial = new Material
{
    Spec = "API 5L Grade X65",
    MaterialForm = "Pipe",
    MaterialDiameter = 508.0m,
    MaterialThickness = 12.7m,
    BaseMetalGroup = "Group I",
    BaseMetalSubGroup = "HSLA Steel",
    CharpyRequirement = true,
    WeldCarbonEquivalent = 0.45m,
    WeldMinYieldStrength = 65000,
    Class = "Pipeline",
    Grade = "X65",
    Condition = "Normalized"
};
```

## Material Specifications

### ASTM Specifications
- **A36**: Structural carbon steel
- **A572**: High-strength low-alloy steel
- **A240**: Stainless steel plate
- **A312**: Stainless steel pipe
- **A106**: Carbon steel pipe for high-temperature service

### API Specifications
- **5L**: Line pipe for petroleum and natural gas
- **5CT**: Casing and tubing for oil and gas wells
- **650**: Welded steel tanks for oil storage

### ASME Specifications
- **SA-516**: Pressure vessel plates for moderate and lower temperatures
- **SA-106**: Seamless carbon steel pipe for high-temperature service
- **SA-312**: Seamless and welded austenitic stainless steel pipes

## Weldability Considerations

### Carbon Equivalent
The carbon equivalent formula helps assess weldability:
```
CE = C + Mn/6 + (Cr + Mo + V)/5 + (Ni + Cu)/15
```

### Preheating Requirements
- **CE < 0.40**: No preheating typically required
- **CE 0.40-0.60**: Preheating may be required
- **CE > 0.60**: Preheating usually required

### Post-Weld Heat Treatment
- **Stress Relief**: For thick sections and high-strength materials
- **Normalization**: For some carbon and low-alloy steels
- **Solution Annealing**: For some stainless steels

## Material Substitutions

Materials can have approved substitutions through the `MaterialSubstitution` entity:

### Substitution Types
- **Direct Substitution**: Equivalent materials
- **Upgrade Substitution**: Higher grade materials
- **Alternative Specification**: Different specification with equivalent properties
- **Emergency Substitution**: Temporary substitutions for availability

## Related Entities

- [MaterialSubstitution](MaterialSubstitution.md) - Approved material substitutions
- [MetalChemicalComposition](MetalChemicalComposition.md) - Chemical composition details
- [Specification](Specification.md) - Material specifications and standards

## Database Considerations

- Index on `Spec` for material specification searches
- Index on `BaseMetalGroup` and `BaseMetalSubGroup` for classification queries
- Consider full-text search on specification and description fields
- Implement proper decimal precision for dimensional and mechanical properties
- Foreign key constraints should be properly configured for self-references
- Consider adding computed columns for carbon equivalent calculations
