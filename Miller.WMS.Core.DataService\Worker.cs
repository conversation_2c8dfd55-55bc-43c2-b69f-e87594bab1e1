using Bogus;
using Microsoft.EntityFrameworkCore;
using Miller.WMS.Domain;
using Miller.WMS.Shared.Data;

namespace Miller.WMS.Core.DataService;

public class Worker(ILogger<Worker> logger, IServiceProvider serviceProvider, IHostApplicationLifetime hostApplicationLifetime) : BackgroundService
{

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        using var scope = serviceProvider.CreateScope();

        var dbContext = scope.ServiceProvider.GetRequiredService<WmsContext>();

        logger.LogInformation("Ensuring database is created and applying migrations...");

        //await dbContext.Database.MigrateAsync(stoppingToken);
        await dbContext.Database.EnsureCreatedAsync(stoppingToken);

        logger.LogInformation("Database setup complete.");

        if (!dbContext.Organizations.Any())
        {
            await SeedDataAsync(dbContext, stoppingToken);
        }
        hostApplicationLifetime.StopApplication();
    }
    private async Task SeedDataAsync(WmsContext dbContext, CancellationToken stoppingToken)
    {
        int seed = 8675309; // Fixed seed for reproducibility

        var random = new Random(seed);

        var orgFaker = new Faker<Organization>()
            .UseSeed(seed)
            .RuleFor(o => o.Name, f => f.Company.CompanyName());

        var facilityFaker = new Faker<Facility>()
            .UseSeed(seed)
            .RuleFor(f => f.Name, f => f.Address.BuildingNumber());

        var userFaker = new Faker<User>()
            .UseSeed(seed)
            .RuleFor(u => u.Name, f => f.Person.FullName)
            .RuleFor(u => u.Email, f => f.Person.Email);

        var roleFaker = new Faker<UserFacilityRole>()
            .UseSeed(seed)
            .RuleFor(r => r.RoleAtFacility, f => f.Name.JobTitle());

        for (int i = 0; i < 10; i++)
        {
            var org = orgFaker.Generate();

            logger.LogInformation($"Creating Organization {i + 1}: {org.Name}");

            int facilityCount = random.Next(1, 51); // Random number of facilities between 1 and 50
            for (int j = 0; j < facilityCount; j++)
            {
                var facility = facilityFaker.Generate();
                facility.Organization = org; // Set navigation property
                facility.OrganizationId = org.Id; // Set foreign key
                org.Facilities.Add(facility);

                int userCount = random.Next(10, 201); // Random number of users between 10 and 200
                for (int k = 0; k < userCount; k++)
                {
                    var user = userFaker.Generate();
                    user.Organization = org; // Set navigation property
                    user.OrganizationId = org.Id; // Set foreign key
                    org.Users.Add(user);

                    var role = roleFaker.Generate();
                    role.Facility = facility; // Set navigation property
                    role.FacilityId = facility.Id; // Set foreign key
                    role.User = user; // Set navigation property
                    role.UserId = user.Id; // Set foreign key
                }
            }

            dbContext.Organizations.Add(org);
            await dbContext.SaveChangesAsync(stoppingToken); // Save after each org to ensure IDs are generated
        }

        await dbContext.SaveChangesAsync(stoppingToken);

        logger.LogInformation("Random test data added to the database.");
    }

}
