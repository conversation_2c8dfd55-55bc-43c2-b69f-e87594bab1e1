﻿using System;

namespace Miller.WMS.Domain;

public class EquipmentHasCurrentType : IEntityWithAudit
{

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid EquipmentId { get; set; }
    public Equipment Equipment { get; set; } = null!;

    public CurrentType CurrentType { get; set; }
}
