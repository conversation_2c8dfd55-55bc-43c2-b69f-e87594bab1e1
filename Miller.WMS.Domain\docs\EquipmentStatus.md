# EquipmentStatus Enumeration

**Source File:** [EquipmentStatus.cs](../EquipmentStatus.cs)

## Overview
The `EquipmentStatus` enumeration defines the operational status of welding equipment within the manufacturing facility. This status tracking is essential for maintenance planning, capacity management, and operational efficiency.

## Values

| Value | Description | Operational State | Maintenance Implications |
|-------|-------------|-------------------|-------------------------|
| `Active` | Equipment is operational and available | Fully operational | Regular maintenance schedule |
| `Inactive` | Equipment is not currently in use | Temporarily unavailable | Reduced maintenance requirements |
| `Maintenance` | Equipment is undergoing maintenance | Not available for production | Active maintenance in progress |
| `Repair` | Equipment requires repair | Not available for production | Repair work needed |
| `Retired` | Equipment is permanently out of service | Permanently unavailable | No maintenance required |

## Status Characteristics

### Active
- **Operational State**: Fully operational and available for production
- **Availability**: Available for scheduling and work assignments
- **Maintenance**: Regular preventive maintenance schedule applies
- **Monitoring**: Active performance monitoring and data collection
- **Usage Tracking**: Full usage tracking and efficiency metrics
- **Applications**:
  - Production welding equipment
  - Equipment in regular rotation
  - Primary production assets
  - Equipment meeting performance standards

### Inactive
- **Operational State**: Temporarily not in use but capable of operation
- **Availability**: Not available for immediate scheduling
- **Maintenance**: Reduced maintenance schedule, preservation activities
- **Monitoring**: Limited monitoring, periodic status checks
- **Usage Tracking**: No current usage tracking
- **Reasons for Inactive Status**:
  - Seasonal production variations
  - Backup equipment not currently needed
  - Equipment awaiting reassignment
  - Temporary production line changes
- **Reactivation**: Can be returned to Active status when needed

### Maintenance
- **Operational State**: Undergoing scheduled or unscheduled maintenance
- **Availability**: Not available for production use
- **Maintenance**: Active maintenance work in progress
- **Monitoring**: Maintenance progress tracking
- **Usage Tracking**: Maintenance time tracking
- **Types of Maintenance**:
  - **Preventive Maintenance**: Scheduled routine maintenance
  - **Predictive Maintenance**: Condition-based maintenance
  - **Corrective Maintenance**: Addressing identified issues
  - **Overhaul**: Major maintenance or refurbishment
- **Duration**: Temporary status, returns to Active when complete

### Repair
- **Operational State**: Equipment has failed or is malfunctioning
- **Availability**: Not available for production use
- **Maintenance**: Active repair work required
- **Monitoring**: Repair progress and cost tracking
- **Usage Tracking**: Downtime tracking for reliability analysis
- **Types of Repairs**:
  - **Emergency Repair**: Critical equipment failures
  - **Scheduled Repair**: Planned repair during downtime
  - **Warranty Repair**: Repairs covered under warranty
  - **Component Replacement**: Major component failures
- **Priority**: Often high priority to minimize production impact

### Retired
- **Operational State**: Permanently removed from service
- **Availability**: Never available for production use
- **Maintenance**: No maintenance activities
- **Monitoring**: Asset disposal tracking
- **Usage Tracking**: Historical data only
- **Reasons for Retirement**:
  - End of useful life
  - Obsolete technology
  - Cost of maintenance exceeds value
  - Safety concerns
  - Replacement with newer equipment
- **Disposition**: Equipment may be sold, scrapped, or donated

## Status Transitions

### Typical Status Flow
```
Active ↔ Inactive
Active → Maintenance → Active
Active → Repair → Active
Active → Repair → Retired
Inactive → Maintenance → Active
Inactive → Retired
Maintenance → Repair (if issues found)
Repair → Retired (if not economical to repair)
```

### Transition Triggers
- **Active to Maintenance**: Scheduled maintenance due
- **Active to Repair**: Equipment failure or malfunction
- **Active to Inactive**: Production changes, seasonal variations
- **Maintenance to Active**: Maintenance completed successfully
- **Repair to Active**: Repair completed successfully
- **Any Status to Retired**: End of life decision

## Business Rules

### Status Assignment Rules
1. **Single Status**: Equipment can only have one status at a time
2. **Status Authority**: Only authorized personnel can change equipment status
3. **Documentation**: All status changes must be documented with reason
4. **Approval**: Retirement status requires management approval
5. **Notification**: Status changes trigger appropriate notifications

### Operational Rules
- **Active Equipment**: Must meet safety and performance standards
- **Maintenance Equipment**: Must follow lockout/tagout procedures
- **Repair Equipment**: Must be properly tagged and isolated
- **Retired Equipment**: Must be properly disposed of or transferred

## Maintenance Planning

### Status-Based Maintenance
- **Active**: Full preventive maintenance program
- **Inactive**: Preservation maintenance, periodic inspections
- **Maintenance**: Scheduled maintenance activities
- **Repair**: Corrective maintenance and repairs
- **Retired**: Asset disposal activities

### Maintenance Scheduling
- **Preventive**: Based on usage hours, calendar time, or condition
- **Predictive**: Based on condition monitoring data
- **Corrective**: Based on failure analysis and repair requirements
- **Emergency**: Immediate response for critical failures

## Capacity Management

### Available Capacity
- **Active Equipment**: Full capacity available for planning
- **Inactive Equipment**: Potential capacity if reactivated
- **Maintenance/Repair**: Temporary capacity reduction
- **Retired Equipment**: Permanent capacity reduction

### Capacity Planning
- **Short-term**: Consider maintenance and repair schedules
- **Medium-term**: Plan for equipment reactivation or replacement
- **Long-term**: Strategic capacity planning including retirements

## Performance Metrics

### Status-Based Metrics
- **Availability**: Percentage of time equipment is Active
- **Reliability**: Mean time between failures for Active equipment
- **Maintainability**: Mean time to repair for Repair status
- **Utilization**: Actual usage of Active equipment

### Key Performance Indicators
- **Overall Equipment Effectiveness (OEE)**: Availability × Performance × Quality
- **Mean Time Between Failures (MTBF)**: Reliability metric
- **Mean Time to Repair (MTTR)**: Maintainability metric
- **Planned Maintenance Percentage**: Maintenance efficiency metric

## Cost Management

### Status-Related Costs
- **Active**: Operating costs, maintenance costs, depreciation
- **Inactive**: Preservation costs, storage costs, opportunity costs
- **Maintenance**: Direct maintenance costs, lost production costs
- **Repair**: Repair costs, emergency response costs, downtime costs
- **Retired**: Disposal costs, replacement costs

### Cost Optimization
- **Preventive Maintenance**: Reduce repair costs and downtime
- **Condition Monitoring**: Optimize maintenance timing
- **Lifecycle Management**: Optimize retirement timing
- **Spare Parts**: Optimize inventory based on equipment status

## Related Entities

- [Equipment](Equipment.md) - Equipment entities that have status
- [WorkCenter](WorkCenter.md) - Work centers affected by equipment status
- [Maintenance records**: Maintenance activities related to status changes
- [Performance metrics**: Equipment performance tracking by status

## Usage in Domain Model

The `EquipmentStatus` enumeration is used in:
- **Equipment entity**: Tracks current operational status of equipment
- **Maintenance planning**: Schedules maintenance based on status
- **Capacity planning**: Calculates available production capacity
- **Performance reporting**: Reports equipment availability and utilization

## Integration with Systems

### Maintenance Management
- **CMMS Integration**: Computerized Maintenance Management Systems
- **Work Order Management**: Automatic work order generation
- **Spare Parts Management**: Parts availability based on equipment status
- **Maintenance Scheduling**: Schedule optimization based on status

### Production Planning
- **Capacity Planning**: Available capacity calculation
- **Schedule Optimization**: Production scheduling around maintenance
- **Resource Allocation**: Resource assignment based on equipment availability
- **Bottleneck Analysis**: Identify capacity constraints

## Best Practices

### Status Management
1. **Real-time Updates**: Keep status information current
2. **Clear Procedures**: Establish clear status change procedures
3. **Documentation**: Document all status changes with reasons
4. **Communication**: Communicate status changes to affected parties
5. **Review Process**: Regular review of equipment status

### Maintenance Optimization
- **Preventive Focus**: Emphasize preventive over corrective maintenance
- **Condition Monitoring**: Use condition monitoring to optimize maintenance
- **Root Cause Analysis**: Address root causes of failures
- **Continuous Improvement**: Continuously improve maintenance processes
