# GasChemicalComposition

**Source File:** [GasChemicalComposition.cs](../GasChemicalComposition.cs)

## Overview
The `GasChemicalComposition` entity defines the precise chemical composition of welding gases and gas mixtures. This information is critical for determining gas properties, welding characteristics, and application suitability.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the composition |
| `CarbonDioxide` | `decimal?` | No | CO₂ percentage (5,4 precision) |
| `Oxygen` | `decimal?` | No | O₂ percentage (5,4 precision) |
| `Argon` | `decimal?` | No | Ar percentage (5,4 precision) |
| `Helium` | `decimal?` | No | He percentage (5,4 precision) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the composition was created |
| `CreatedBy` | `Guid?` | No | User who created the composition record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the composition record |

## Relationships

### One-to-Many Relationships
- **GasChemicalComposition → Gas**: Compositions can be used by multiple gases

## Gas Components

### Carbon Dioxide (CO₂)
- **Purpose**: Active gas for steel welding, increases penetration
- **Typical Range**: 0% - 100% (pure CO₂ or in mixtures)
- **Effects**: Increases penetration, affects arc characteristics, can cause spatter

### Oxygen (O₂)
- **Purpose**: Active gas for stainless steel welding, improves arc stability
- **Typical Range**: 0% - 5% (small additions to argon)
- **Effects**: Improves arc stability, affects bead profile, oxidizes some elements

### Argon (Ar)
- **Purpose**: Primary inert shielding gas for most welding applications
- **Typical Range**: 0% - 100% (pure argon or in mixtures)
- **Effects**: Provides excellent arc stability, good bead appearance, inert protection

### Helium (He)
- **Purpose**: Inert gas for high heat input applications
- **Typical Range**: 0% - 100% (pure helium or in mixtures)
- **Effects**: Higher heat input, deeper penetration, faster travel speeds

## Common Gas Compositions

### Pure Gases
```csharp
// Pure Argon
var pureArgon = new GasChemicalComposition
{
    Id = Guid.NewGuid(),
    Argon = 100.0000m,
    CarbonDioxide = null,
    Oxygen = null,
    Helium = null,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "gas_engineer"
};

// Pure CO₂
var pureCO2 = new GasChemicalComposition
{
    CarbonDioxide = 100.0000m,
    Argon = null,
    Oxygen = null,
    Helium = null
};
```

### Binary Mixtures
```csharp
// Ar/CO₂ 75/25
var ar75co25 = new GasChemicalComposition
{
    Argon = 75.0000m,
    CarbonDioxide = 25.0000m,
    Oxygen = null,
    Helium = null
};

// Ar/He 75/25
var ar75he25 = new GasChemicalComposition
{
    Argon = 75.0000m,
    Helium = 25.0000m,
    CarbonDioxide = null,
    Oxygen = null
};
```

### Ternary Mixtures
```csharp
// Ar/CO₂/O₂ 90/8/2
var ar90co8o2 = new GasChemicalComposition
{
    Argon = 90.0000m,
    CarbonDioxide = 8.0000m,
    Oxygen = 2.0000m,
    Helium = null
};
```

## Business Rules

1. **Composition Validation**: Total composition should equal 100% when all components are considered
2. **Precision Requirements**: All percentages must be specified with appropriate precision (5,4)
3. **Null Handling**: Components not present in the gas should be null, not zero
4. **Range Validation**: All values should be between 0 and 100 percent
5. **Mixture Logic**: At least one component must be specified

## Gas Applications by Composition

### GTAW (TIG) Welding
- **Pure Argon**: General purpose, all materials
- **Ar/He mixtures**: Aluminum welding, increased heat input
- **Pure Helium**: Specialized high heat input applications

### GMAW (MIG) Welding
- **Ar/CO₂ mixtures**: Carbon steel welding (75/25, 90/10)
- **Pure Argon**: Aluminum and non-ferrous metals
- **Ar/O₂ mixtures**: Stainless steel welding (98/2, 99/1)

### FCAW (Flux-Cored) Welding
- **Pure CO₂**: Self-shielded flux-cored wires
- **Ar/CO₂ mixtures**: Gas-shielded flux-cored wires
- **Ar/CO₂/O₂ mixtures**: Specialized applications

## Metallurgical Effects

### Arc Characteristics
- **Argon**: Stable arc, good directional control
- **Helium**: Hotter arc, broader heat distribution
- **CO₂**: Active arc, deeper penetration
- **Oxygen**: Improved arc stability in small amounts

### Weld Metal Properties
- **Argon**: Minimal effect on chemistry, good mechanical properties
- **Helium**: Minimal chemical effect, increased heat input
- **CO₂**: Can affect carbon content, may cause porosity
- **Oxygen**: Deoxidizes weld metal, affects inclusion content

### Bead Characteristics
- **Argon**: Good bead appearance, minimal spatter
- **Helium**: Wide, shallow penetration profile
- **CO₂**: Deep penetration, may increase spatter
- **Oxygen**: Improved wetting, affects bead profile

## Quality Control

### Composition Verification
- **Gas Chromatography**: Precise composition analysis
- **Mass Spectrometry**: High-precision analysis
- **Infrared Analysis**: Specific component analysis
- **Certification**: Supplier composition certificates

### Tolerance Requirements
- **High Purity**: ±0.01% for critical applications
- **Standard Purity**: ±0.1% for general applications
- **Industrial Grade**: ±1.0% for non-critical applications

## Usage Examples

### Creating Standard Welding Gas Compositions
```csharp
var standardCompositions = new List<GasChemicalComposition>
{
    // Ar/CO₂ 75/25 for steel MIG welding
    new GasChemicalComposition
    {
        Argon = 75.0000m,
        CarbonDioxide = 25.0000m,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "gas_specialist"
    },
    
    // Ar/O₂ 98/2 for stainless steel
    new GasChemicalComposition
    {
        Argon = 98.0000m,
        Oxygen = 2.0000m,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "gas_specialist"
    }
};
```

## Storage and Handling

### Gas Purity Considerations
- **Moisture Content**: Control moisture levels for quality
- **Impurities**: Monitor and control impurity levels
- **Contamination**: Prevent cross-contamination between gases
- **Stability**: Ensure mixture stability over time

### Cylinder Management
- **Labeling**: Proper composition labeling
- **Color Coding**: Standard color coding for gas types
- **Pressure**: Maintain proper cylinder pressures
- **Testing**: Regular composition testing

## Safety Considerations

### Gas Hazards
- **Asphyxiation**: Inert gases can displace oxygen
- **Oxidation**: Oxygen-enriched atmospheres increase fire risk
- **Toxicity**: CO₂ can be toxic in high concentrations
- **Pressure**: High-pressure gas handling safety

### Handling Procedures
- **Ventilation**: Adequate ventilation for gas use
- **Detection**: Gas detection and monitoring systems
- **Emergency**: Emergency response procedures
- **Training**: Proper training for gas handling

## Related Entities

- [Gas](Gas.md) - Gases using this composition
- [GasClassification](GasClassification.md) - Gas classification categories
- [WorkCenter](WorkCenter.md) - Work centers where gases are used
- [WorkCenterHasGas](WorkCenterHasGas.md) - Gas availability at work centers

## Database Considerations

- All decimal properties have precision (5,4) for accurate composition tracking
- Consider adding check constraints to ensure total composition ≤ 100%
- Index on common composition combinations for performance
- Implement validation to ensure at least one component is specified
- Consider adding computed columns for total composition validation

## International Standards

### Gas Purity Classifications
- **Research Grade**: 99.999% purity
- **Ultra High Purity**: 99.99% purity
- **High Purity**: 99.9% purity
- **Commercial Grade**: 99.0% purity

### Mixture Tolerances
- **Precision Mixtures**: ±0.5% relative
- **Standard Mixtures**: ±2% relative
- **Commercial Mixtures**: ±5% relative
