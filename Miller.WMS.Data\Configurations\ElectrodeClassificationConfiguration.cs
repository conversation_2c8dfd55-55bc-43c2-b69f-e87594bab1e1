﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeClassificationConfiguration : IEntityTypeConfiguration<ElectrodeClassification>
{
    public void Configure(EntityTypeBuilder<ElectrodeClassification> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Classification)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.YieldStrength)
               .HasPrecision(6, 3)
               .IsRequired();

        builder.Property(e => e.TensileStrength)
               .HasPrecision(6, 3)
               .IsRequired();

        builder.Property(e => e.Elongation)
               .HasPrecision(5, 4)
               .IsRequired();

        builder.Property(e => e.CoveringType)
               .HasConversion<int?>();

        builder.HasOne(e => e.Specification)
               .WithMany()
               .HasForeignKey(e => e.SpecificationId)
               .OnDelete(DeleteBehavior.Restrict);

        builder.HasOne(e => e.TungstenElectrodeClassification)
               .WithMany()
               .HasForeignKey(e => e.TungstenElectrodeClassificationId)
               .OnDelete(DeleteBehavior.SetNull);

        builder.HasOne(e => e.MetalChemicalComposition)
               .WithMany()
               .HasForeignKey(e => e.MetalChemicalCompositionId)
               .OnDelete(DeleteBehavior.Restrict);
    }
}
