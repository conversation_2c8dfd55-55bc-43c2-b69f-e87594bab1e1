# WorkCenterHasWaveform

**Source File:** [WorkCenterHasWaveform.cs](../WorkCenterHasWaveform.cs)

## Overview
The `WorkCenterHasWaveform` entity represents the many-to-many relationship between work centers and waveforms, tracking which welding waveforms are available or configured at specific work centers.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `WorkCenterId` | `Guid` | Yes | Foreign key to the work center |
| `WaveformId` | `Guid` | Yes | Foreign key to the waveform |
| `CreatedAt` | `DateTime?` | No | Timestamp when the waveform was assigned |
| `CreatedBy` | `Guid?` | No | User who created this assignment |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this assignment |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `WorkCenter` | `WorkCenter` | The work center where waveform is available |
| `Waveform` | `Waveform` | The waveform available at the work center |

## Relationships

### Many-to-One Relationships
- **WorkCenterHasWaveform → WorkCenter**: Each assignment belongs to one work center
- **WorkCenterHasWaveform → Waveform**: Each assignment is for one waveform

## Composite Key

This entity uses a composite primary key consisting of:
- `WorkCenterId`
- `WaveformId`

This ensures that the same waveform can only be assigned once to a specific work center, but waveforms can be available at multiple work centers.

## Waveform Assignment Types

Waveform assignments can represent different availability scenarios:

### Standard Waveforms
- Standard waveforms available for general welding operations
- Common waveforms for typical applications
- Default waveforms for specific processes

### Specialized Waveforms
- Application-specific waveforms for unique requirements
- Material-specific waveforms for specialized alloys
- Process-optimized waveforms for specific techniques

### Custom Waveforms
- Customer-specific waveforms for special applications
- Proprietary waveforms for competitive advantage
- Research and development waveforms for testing

## Business Rules

1. **Work Center Compatibility**: Waveforms must be compatible with work center equipment
2. **Power Supply Support**: Work center power supplies must support the waveform
3. **Process Alignment**: Waveforms should align with work center welding processes
4. **Operator Training**: Operators must be trained on assigned waveforms
5. **Version Control**: Waveform versions should be properly managed

## Usage Examples

### Assigning Waveforms to a TIG Work Center
```csharp
var tigWaveforms = new List<WorkCenterHasWaveform>
{
    new WorkCenterHasWaveform
    {
        WorkCenterId = tigStationId,
        WaveformId = acTigAluminumWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = process_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = tigStationId,
        WaveformId = dcTigSteelWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = process_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = tigStationId,
        WaveformId = pulseTigWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = process_engineer_id
    }
};
```

### Assigning Waveforms to a MIG Work Center
```csharp
var migWaveforms = new List<WorkCenterHasWaveform>
{
    new WorkCenterHasWaveform
    {
        WorkCenterId = migStationId,
        WaveformId = pulseSprayWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = welding_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = migStationId,
        WaveformId = shortCircuitWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = welding_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = migStationId,
        WaveformId = surfaceTensionTransferWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = welding_engineer_id
    }
};
```

## Work Center Waveform Capabilities

Waveform assignments define work center advanced welding capabilities:

### Process-Specific Capabilities
- **GTAW (TIG)**: AC balance control, pulse waveforms, advanced AC
- **GMAW (MIG)**: Pulse spray, modified short circuit, surface tension transfer
- **FCAW**: Specialized flux-cored waveforms
- **Specialized Processes**: Cold metal transfer (CMT), plasma welding

### Material-Specific Capabilities
- **Aluminum**: AC TIG waveforms, specialized MIG waveforms
- **Stainless Steel**: Pulse waveforms for heat control
- **Carbon Steel**: Standard and modified waveforms
- **Exotic Alloys**: Specialized waveforms for difficult materials

### Application-Specific Capabilities
- **Aerospace**: High-quality, low-distortion waveforms
- **Automotive**: High-speed production waveforms
- **Pipeline**: Root pass and hot pass waveforms
- **Thin Material**: Low heat input waveforms

## Waveform Configuration Management

### Configuration Control
- **Version Management**: Track waveform versions at work centers
- **Change Control**: Controlled process for waveform updates
- **Backup Configurations**: Maintain backup waveform configurations
- **Documentation**: Complete waveform documentation

### Performance Optimization
- **Parameter Tuning**: Optimize waveform parameters for applications
- **Performance Monitoring**: Monitor waveform performance
- **Continuous Improvement**: Ongoing waveform optimization
- **Best Practices**: Share best practices across work centers

## Equipment Integration

### Power Supply Compatibility
- **Waveform Support**: Ensure power supply supports assigned waveforms
- **Parameter Ranges**: Verify parameter ranges are adequate
- **Control Interface**: Compatible control interfaces
- **Software Versions**: Compatible software and firmware versions

### System Integration
- **Data Logging**: Log waveform usage and performance
- **Quality Integration**: Integrate with quality monitoring systems
- **Production Integration**: Integrate with production management
- **Maintenance Integration**: Integrate with maintenance systems

## Usage Examples

### Creating Waveform Assignments for Different Applications
```csharp
var waveformAssignments = new List<WorkCenterHasWaveform>
{
    // Aerospace TIG Station - High-quality waveforms
    new WorkCenterHasWaveform
    {
        WorkCenterId = aerospaceTigStationId,
        WaveformId = precisionTigWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = aerospace_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = aerospaceTigStationId,
        WaveformId = lowDistortionWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = aerospace_engineer_id
    },
    
    // Production MIG Station - High-productivity waveforms
    new WorkCenterHasWaveform
    {
        WorkCenterId = productionMigStationId,
        WaveformId = highSpeedMigWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = production_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = productionMigStationId,
        WaveformId = lowSpatterWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = production_engineer_id
    },
    
    // Pipeline Station - Specialized pipeline waveforms
    new WorkCenterHasWaveform
    {
        WorkCenterId = pipelineStationId,
        WaveformId = rootPassWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = pipeline_engineer_id
    },
    new WorkCenterHasWaveform
    {
        WorkCenterId = pipelineStationId,
        WaveformId = hotPassWaveformId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = pipeline_engineer_id
    }
};
```

## Quality and Performance

### Quality Benefits
- **Consistent Results**: Standardized waveforms for consistent quality
- **Reduced Defects**: Optimized waveforms reduce welding defects
- **Improved Properties**: Enhanced mechanical properties
- **Better Appearance**: Improved weld bead appearance

### Performance Benefits
- **Increased Productivity**: Optimized waveforms increase welding speed
- **Reduced Rework**: Fewer defects reduce rework requirements
- **Energy Efficiency**: Optimized energy usage
- **Reduced Consumables**: Optimized electrode and gas consumption

## Training and Certification

### Operator Training
- **Waveform Understanding**: Train operators on waveform principles
- **Parameter Selection**: Train on proper parameter selection
- **Troubleshooting**: Train on waveform-related troubleshooting
- **Safety**: Safety considerations for advanced waveforms

### Certification Requirements
- **Waveform Qualification**: Qualify operators on specific waveforms
- **Procedure Qualification**: Qualify welding procedures with waveforms
- **Documentation**: Maintain training and qualification records
- **Continuous Education**: Ongoing education on new waveforms

## Maintenance and Support

### Waveform Maintenance
- **Software Updates**: Regular software and firmware updates
- **Calibration**: Regular calibration of waveform parameters
- **Performance Monitoring**: Monitor waveform performance
- **Troubleshooting**: Diagnose and resolve waveform issues

### Technical Support
- **Expert Support**: Access to waveform development experts
- **Documentation**: Comprehensive waveform documentation
- **Training Resources**: Training materials and resources
- **Best Practices**: Sharing of best practices and lessons learned

## Related Entities

- [WorkCenter](WorkCenter.md) - The work center where waveforms are available
- [Waveform](Waveform.md) - The waveforms available at work centers
- [Equipment](Equipment.md) - Power supplies that support the waveforms
- [ElectrodeClassification](ElectrodeClassification.md) - Electrodes compatible with waveforms

## Database Considerations

- Composite primary key on `WorkCenterId` and `WaveformId`
- Index on `WorkCenterId` for work center waveform queries
- Index on `WaveformId` for waveform location queries
- Foreign key constraints should be properly configured
- Consider adding fields for waveform parameters, settings, and performance data
- Implement audit triggers for assignment change tracking

## Operational Benefits

1. **Advanced Capabilities**: Enable advanced welding capabilities at work centers
2. **Quality Improvement**: Improve weld quality through optimized waveforms
3. **Productivity Enhancement**: Increase productivity with specialized waveforms
4. **Flexibility**: Provide flexibility for different applications and materials
5. **Standardization**: Standardize waveform usage across similar work centers
6. **Innovation**: Enable innovation through new waveform development

## Future Enhancements

Consider adding additional properties for comprehensive waveform management:

### Configuration Properties
- **Default Parameters**: Default waveform parameter settings
- **Parameter Ranges**: Allowable parameter adjustment ranges
- **Optimization Settings**: Automatic optimization settings
- **Performance Metrics**: Waveform performance tracking

### Integration Properties
- **Quality Integration**: Integration with quality monitoring systems
- **Production Integration**: Integration with production management
- **Maintenance Integration**: Integration with maintenance scheduling
- **Training Integration**: Integration with operator training systems
