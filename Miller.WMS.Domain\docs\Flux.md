# Flux

**Source File:** [Flux.cs](../Flux.cs)

## Overview
The `Flux` entity represents welding fluxes used in submerged arc welding (SAW) and flux-cored arc welding (FCAW) processes. Fluxes protect the weld pool from atmospheric contamination and can contribute alloying elements to the weld metal.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the flux |
| `TradeName` | `string` | Yes | Commercial trade name of the flux |
| `IsDrying` | `bool` | Yes | Whether the flux requires drying before use |
| `VendorRecommendationDryingFlux` | `string?` | No | Vendor recommendations for flux drying |
| `IsPenetrationEnhanced` | `bool` | Yes | Whether the flux enhances weld penetration |
| `FluxChemicalCompositionId` | `Guid` | Yes | Foreign key to the chemical composition |
| `CreatedAt` | `DateTime?` | No | Timestamp when the flux was created |
| `CreatedBy` | `Guid?` | No | User who created the flux record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the flux record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `FluxChemicalComposition` | `FluxChemicalComposition?` | The chemical composition of the flux |

## Relationships

### Many-to-One Relationships
- **Flux → FluxChemicalComposition**: Each flux has one chemical composition

### Many-to-Many Relationships (via junction entities)
- **Flux ↔ FluxClass**: Fluxes can belong to multiple classification classes

## Flux Types

### By Welding Process
- **Submerged Arc Welding (SAW)**: Granular flux for SAW process
- **Flux-Cored Arc Welding (FCAW)**: Flux contained within electrode core
- **Electroslag Welding (ESW)**: Specialized flux for electroslag process

### By Basicity
- **Acidic Flux**: Low basicity, good bead appearance
- **Basic Flux**: High basicity, low hydrogen, good mechanical properties
- **Neutral Flux**: Balanced basicity, general purpose applications

### By Manufacturing Method
- **Fused Flux**: Melted and solidified flux with consistent properties
- **Agglomerated Flux**: Bonded flux particles with controlled composition
- **Sintered Flux**: Heat-treated flux with specific characteristics

## Flux Properties

### Chemical Composition
Flux chemical composition affects:
- **Weld Metal Chemistry**: Flux can contribute alloying elements
- **Slag Properties**: Affects slag removal and appearance
- **Arc Characteristics**: Influences arc stability and penetration
- **Mechanical Properties**: Affects strength and toughness

### Physical Properties
- **Particle Size**: Affects arc characteristics and slag formation
- **Bulk Density**: Important for flux handling and consumption
- **Moisture Content**: Critical for hydrogen control
- **Flowability**: Affects flux feeding and distribution

### Drying Requirements
Many fluxes require drying to remove moisture:
- **Drying Temperature**: Typically 200-800°F (93-427°C)
- **Drying Time**: Usually 1-8 hours depending on flux type
- **Storage**: Proper storage to prevent moisture reabsorption
- **Handling**: Procedures to maintain dry condition

## Business Rules

1. **Trade Name Uniqueness**: Trade names should be unique within manufacturers
2. **Chemical Composition**: Every flux must have an associated chemical composition
3. **Drying Requirements**: Drying requirements must be clearly specified
4. **Classification**: Fluxes should be properly classified by type and application
5. **Storage Conditions**: Proper storage requirements must be documented

## Usage Examples

### Creating a Basic SAW Flux
```csharp
var sawFlux = new Flux
{
    Id = Guid.NewGuid(),
    TradeName = "Lincoln 880",
    IsDrying = true,
    VendorRecommendationDryingFlux = "Dry at 600-700°F for 2 hours",
    IsPenetrationEnhanced = false,
    FluxChemicalCompositionId = compositionId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "materials_engineer"
};
```

### Creating a High-Performance Flux
```csharp
var highPerfFlux = new Flux
{
    TradeName = "ESAB OK Flux 10.71",
    IsDrying = true,
    VendorRecommendationDryingFlux = "Dry at 572°F (300°C) for 2 hours minimum",
    IsPenetrationEnhanced = true,
    FluxChemicalComposition = new FluxChemicalComposition
    {
        Symbol = "F7A2-EM12K",
        Description = "Basic agglomerated flux for low-alloy steels"
    }
};
```

## Flux Classifications

Fluxes are classified according to AWS specifications:

### AWS A5.17 (SAW Fluxes)
- **F6**: Acidic flux for single-pass welding
- **F7**: Basic flux for multi-pass welding
- **F8**: Basic flux for low-temperature applications

### Classification System
- **F**: Flux designation
- **6, 7, 8**: Flux type number
- **A, P**: Acidic or basic flux
- **0, 2, 4, 6, 8**: Alloy additions
- **EM, EH**: Low or high manganese
- **12, 15, 16**: Minimum tensile strength (x10 ksi)

## Flux Applications

### Carbon Steel Applications
- **Structural Welding**: General construction and fabrication
- **Pressure Vessel**: ASME code applications
- **Pipeline**: Cross-country pipeline construction
- **Shipbuilding**: Marine construction applications

### Low-Alloy Steel Applications
- **Power Generation**: Boiler and pressure vessel construction
- **Petrochemical**: Refinery and chemical plant construction
- **Offshore**: Offshore platform and subsea applications
- **Heavy Equipment**: Mining and construction equipment

### Stainless Steel Applications
- **Chemical Processing**: Corrosion-resistant applications
- **Food Processing**: Sanitary welding applications
- **Pharmaceutical**: High-purity applications
- **Nuclear**: Nuclear power plant applications

## Flux Handling and Storage

### Storage Requirements
- **Moisture Control**: Store in dry, heated storage
- **Temperature Control**: Maintain consistent temperature
- **Contamination Prevention**: Prevent contamination from other materials
- **Inventory Rotation**: First-in, first-out inventory management

### Handling Procedures
- **Drying**: Follow manufacturer's drying recommendations
- **Transfer**: Use clean, dry transfer equipment
- **Reclamation**: Procedures for flux recovery and reuse
- **Disposal**: Proper disposal of contaminated or expired flux

## Quality Control

### Incoming Inspection
- **Chemical Analysis**: Verify chemical composition
- **Moisture Content**: Check moisture levels
- **Particle Size**: Verify particle size distribution
- **Packaging**: Inspect packaging integrity

### Process Control
- **Flux Consumption**: Monitor flux consumption rates
- **Slag Removal**: Evaluate slag removal characteristics
- **Weld Quality**: Monitor weld quality and defects
- **Mechanical Properties**: Test mechanical properties

## Related Entities

- [FluxChemicalComposition](FluxChemicalComposition.md) - Detailed chemical composition
- [FluxClass](FluxClass.md) - Flux classification categories
- [FluxHasFluxClass](FluxHasFluxClass.md) - Flux classification relationships
- [FluxChemicalCompositionLimits](FluxChemicalCompositionLimits.md) - Chemical composition limits

## Database Considerations

- Index on `TradeName` for flux searches
- Index on `FluxChemicalCompositionId` for composition-based queries
- Consider full-text search on trade name and description
- Boolean fields should have appropriate defaults
- Foreign key constraints should be properly configured
- Consider adding fields for manufacturer, AWS classification, etc.

## Safety and Environmental

### Safety Considerations
- **Respiratory Protection**: Flux dust can be harmful if inhaled
- **Skin Protection**: Some fluxes can cause skin irritation
- **Eye Protection**: Protect against flux dust and particles
- **Ventilation**: Adequate ventilation during use and handling

### Environmental Considerations
- **Waste Management**: Proper disposal of used flux and slag
- **Air Quality**: Control of airborne flux particles
- **Water Quality**: Prevent contamination of water sources
- **Recycling**: Flux reclamation and recycling programs
