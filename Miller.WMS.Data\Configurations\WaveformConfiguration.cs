﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WaveformConfiguration : IEntityTypeConfiguration<Waveform>
{
    public void Configure(EntityTypeBuilder<Waveform> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Version)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Description)
               .HasMaxLength(255)
               .IsRequired();
    }
}
