# ElectrodeType Enumeration

**Source File:** [ElectrodeType.cs](../ElectrodeType.cs)

## Overview
The `ElectrodeType` enumeration defines the fundamental categories of electrodes used in welding operations. This classification helps organize electrodes by their basic function and application method.

## Values

| Value | Description | Applications | Characteristics |
|-------|-------------|--------------|-----------------|
| `FillerMetal` | Filler metal electrodes | GTAW, GMAW, SAW | Consumable electrodes that become part of the weld |
| `Stick` | Stick electrodes | SMAW | Covered electrodes for manual welding |
| `Tungsten` | Tungsten electrodes | GTAW, PAW | Non-consumable electrodes for arc generation |

## Electrode Type Characteristics

### FillerMetal
- **Function**: Provides filler material for the weld joint
- **Consumption**: Consumed during welding process
- **Forms**:
  - Wire electrodes (solid or flux-cored)
  - Rod electrodes (for manual feeding)
  - Strip electrodes (for high deposition applications)
- **Welding Processes**:
  - **GTAW (TIG)**: Manual or automatic wire feeding
  - **GMAW (MIG)**: Continuous wire feeding
  - **SAW**: Submerged arc welding with wire
  - **FCAW**: Flux-cored arc welding
- **Characteristics**:
  - Melts and becomes part of weld metal
  - Chemical composition affects weld properties
  - Available in various diameters
  - Requires proper storage and handling

### Stick
- **Function**: Combined electrode and flux system for manual welding
- **Consumption**: Completely consumed during welding
- **Construction**:
  - Metal core wire
  - Flux covering (rutile, cellulosic, basic, or acid)
  - Coating provides arc stability and protection
- **Welding Processes**:
  - **SMAW**: Primary application
  - Manual welding in all positions
- **Characteristics**:
  - Self-contained electrode and flux system
  - Provides own shielding through flux decomposition
  - Available in various lengths (typically 12-18 inches)
  - Requires frequent electrode changes

### Tungsten
- **Function**: Creates and maintains welding arc
- **Consumption**: Non-consumable (minimal consumption)
- **Composition**:
  - Pure tungsten or tungsten alloys
  - Various oxide additions (thorium, lanthanum, cerium)
  - Color-coded for identification
- **Welding Processes**:
  - **GTAW (TIG)**: Primary application
  - **PAW (Plasma)**: Plasma arc welding
- **Characteristics**:
  - High melting point (6,170°F / 3,410°C)
  - Excellent arc starting and stability
  - Requires separate filler metal (if needed)
  - Reusable with proper maintenance

## Application Guidelines

### FillerMetal Applications
- **Material Matching**: Select composition to match base materials
- **Strength Requirements**: Choose grade for required mechanical properties
- **Corrosion Resistance**: Select alloy for environmental conditions
- **Weldability**: Consider ease of welding and defect resistance

#### Common FillerMetal Types
- **Carbon Steel**: ER70S-6, ER70S-3
- **Stainless Steel**: ER308L, ER316L, ER309L
- **Aluminum**: ER4043, ER5356, ER5183
- **Low-Alloy Steel**: ER80S-D2, ER90S-B3

### Stick Applications
- **Field Welding**: Portable, no gas required
- **Maintenance and Repair**: Versatile, all-position capability
- **Structural Welding**: High-strength applications
- **Root Pass Welding**: Deep penetration capability

#### Common Stick Types
- **E6010**: Deep penetration, all-position
- **E6011**: AC compatible, all-position
- **E7018**: Low hydrogen, high strength
- **E6013**: Easy operation, good appearance

### Tungsten Applications
- **Precision Welding**: High-quality, controlled welding
- **Thin Materials**: Excellent heat control
- **Critical Applications**: Aerospace, nuclear, food industry
- **Root Pass Welding**: Precise penetration control

#### Common Tungsten Types
- **EWP**: Pure tungsten for AC aluminum welding
- **EWTh-2**: 2% thoriated for DC steel welding
- **EWLa-1**: 1% lanthanated for AC/DC welding
- **EWCe-2**: 2% ceriated for low-current applications

## Storage and Handling

### FillerMetal Storage
- **Moisture Control**: Protect from moisture and contamination
- **Temperature Control**: Store at stable temperatures
- **Packaging**: Maintain original packaging until use
- **Inventory Rotation**: First-in, first-out rotation

### Stick Storage
- **Low-Hydrogen Types**: Heated storage ovens (250-300°F)
- **Standard Types**: Dry storage, protect from moisture
- **Reconditioning**: Rebaking capability for low-hydrogen electrodes
- **Exposure Limits**: Time limits for low-hydrogen electrode exposure

### Tungsten Storage
- **Contamination Prevention**: Protect from contamination
- **Physical Protection**: Prevent damage to electrode tips
- **Segregation**: Separate different tungsten types
- **Grinding Area**: Dedicated grinding area for tip preparation

## Quality Considerations

### FillerMetal Quality
- **Chemical Composition**: Verify chemistry meets specifications
- **Mechanical Properties**: Test weld metal properties
- **Cleanliness**: Ensure freedom from contamination
- **Dimensional Accuracy**: Verify diameter and tolerance

### Stick Quality
- **Coating Integrity**: Inspect for coating damage
- **Moisture Content**: Control moisture in low-hydrogen types
- **Arc Characteristics**: Verify proper arc starting and stability
- **Slag Removal**: Ensure proper slag removal characteristics

### Tungsten Quality
- **Purity**: Verify tungsten purity and oxide content
- **Geometry**: Check electrode straightness and diameter
- **Surface Quality**: Inspect for surface defects
- **Contamination**: Ensure freedom from contamination

## Economic Considerations

### Cost Factors
- **Material Cost**: Base cost of electrode materials
- **Productivity**: Deposition rates and welding speeds
- **Quality**: Rework costs and quality-related expenses
- **Handling**: Storage and handling costs

### Cost Comparison
- **FillerMetal**: Moderate material cost, high productivity
- **Stick**: Low equipment cost, moderate productivity
- **Tungsten**: High initial cost, long service life

## Safety Considerations

### General Safety
- **Personal Protection**: Appropriate PPE for all electrode types
- **Ventilation**: Adequate ventilation for fume control
- **Fire Prevention**: Proper fire prevention measures
- **Electrical Safety**: Electrical safety for all arc welding

### Electrode-Specific Safety
- **Tungsten**: Radioactive material handling (thoriated types)
- **Stick**: Flux fume exposure considerations
- **FillerMetal**: Wire feeding safety and pinch point protection

## Related Entities

- [Electrode](Electrode.md) - Specific electrodes of these types
- [ElectrodeClassification](ElectrodeClassification.md) - Classifications for different electrode types
- [TungstenElectrodeClassification](TungstenElectrodeClassification.md) - Specific tungsten classifications
- [WeldingProcess](WeldingProcess.md) - Processes using different electrode types

## Usage in Domain Model

The `ElectrodeType` enumeration is used in:
- **Electrode entity**: Categorizes electrodes by fundamental type
- **Inventory management**: Organizes electrode inventory by type
- **Process planning**: Matches electrode types to welding processes
- **Training programs**: Organizes training by electrode type

## Standards and Specifications

### AWS Standards
- **AWS A5.1**: Stick electrodes (SMAW)
- **AWS A5.12**: Tungsten electrodes (GTAW)
- **AWS A5.18**: Filler metals (GTAW/GMAW)
- **AWS A5.20**: Flux-cored electrodes (FCAW)

### Quality Standards
- **Chemical Composition**: Specified by AWS A5 series
- **Mechanical Properties**: Minimum requirements by classification
- **Physical Properties**: Dimensional and surface requirements

## Best Practices

### Electrode Type Selection
1. **Process Compatibility**: Match electrode type to welding process
2. **Application Requirements**: Consider specific application needs
3. **Quality Requirements**: Select type for required quality level
4. **Economic Factors**: Balance performance and cost
5. **Availability**: Consider local availability and supply

### Management Practices
- **Inventory Control**: Maintain appropriate inventory levels by type
- **Quality Assurance**: Implement type-specific quality control
- **Training**: Provide type-specific training programs
- **Documentation**: Maintain records by electrode type
