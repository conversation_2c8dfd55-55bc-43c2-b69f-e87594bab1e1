﻿using System;

namespace Miller.WMS.Domain;

public class ElectrodeDiameter : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid ElectrodeTypeId { get; set; }
    public ElectrodeType ElectrodeType { get; set; }

    public decimal Diameter { get; set; } // (6,3)
}
