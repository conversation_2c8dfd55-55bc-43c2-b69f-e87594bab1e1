using <PERSON>;
using Facet;
using Facet.Extensions;
using Microsoft.EntityFrameworkCore;
using Miller.WMS.Shared.Data;
using Miller.WMS.Domain;


namespace Miller.WMS.Edge.ApiService.Modules;

public class OrganizationModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/organizations")
            .WithTags("Organizations")
            .WithOpenApi();

        group.MapGet("/", GetOrganizations)
            .WithName("GetOrganizations")
            .WithSummary("Get all organizations")
            .WithDescription("Retrieves a list of all organizations");

        group.MapGet("/{id:guid}", GetOrganizationById)
            .WithName("GetOrganizationById")
            .WithSummary("Get organization by ID")
            .WithDescription("Retrieves a specific organization by its ID");

        group.MapPost("/", CreateOrganization)
            .WithName("CreateOrganization")
            .WithSummary("Create a new organization")
            .WithDescription("Creates a new organization");

        group.MapPut("/{id:guid}", UpdateOrganization)
            .WithName("UpdateOrganization")
            .WithSummary("Update an organization")
            .WithDescription("Updates an existing organization");

        group.MapDelete("/{id:guid}", DeleteOrganization)
            .WithName("DeleteOrganization")
            .WithSummary("Delete an organization")
            .WithDescription("Deletes an organization");
    }

    private static async Task<IResult> GetOrganizations(WmsContext context)
    {
        var organizations = await context.Organizations
            .Include(o => o.Facilities)
            .Include(o => o.Users)
            .ToListAsync();

        var organizationDtos = organizations.SelectFacets<Organization, OrganizationDto>().ToList();
        return Results.Ok(organizationDtos);
    }

    private static async Task<IResult> GetOrganizationById(Guid id, WmsContext context)
    {
        var organization = await context.Organizations
            .Include(o => o.Facilities)
            .Include(o => o.Users)
            .FirstOrDefaultAsync(o => o.Id == id);

        if (organization == null)
            return Results.NotFound($"Organization with ID {id} not found");

        return Results.Ok(organization.ToFacet<Organization, OrganizationDto>());
    }

    private static async Task<IResult> CreateOrganization(CreateOrganizationRequest request, WmsContext context)
    {
        var organization = request.ToFacet<CreateOrganizationRequest, Organization>();
        organization.Id = Guid.NewGuid();
        organization.CreatedAt = DateTime.UtcNow;
        organization.CreatedBy = Guid.NewGuid(); // Get from request token when auth configured

        context.Organizations.Add(organization);
        await context.SaveChangesAsync();

        return Results.Created($"/api/organizations/{organization.Id}", organization.ToFacet<Organization, OrganizationDto>());
    }

    private static async Task<IResult> UpdateOrganization(Guid id, UpdateOrganizationRequest request, WmsContext context)
    {
        var updatedOrganization = request.ToFacet<UpdateOrganizationRequest, Organization>();

        // Validate that URL ID matches request object ID
        if (id != updatedOrganization.Id)
            return Results.BadRequest($"URL ID {id} does not match request object ID {updatedOrganization.Id}");

        var existingOrganization = await context.Organizations.FindAsync(id);
        if (existingOrganization == null)
            return Results.NotFound($"Organization with ID {id} not found");

        // Preserve creation audit fields (excluded from request by design)
        updatedOrganization.CreatedAt = existingOrganization.CreatedAt;
        updatedOrganization.CreatedBy = existingOrganization.CreatedBy;
        updatedOrganization.ModifiedAt = DateTime.UtcNow;
        updatedOrganization.ModifiedBy = Guid.NewGuid(); // Get from request token when auth configured

        context.Entry(existingOrganization).CurrentValues.SetValues(updatedOrganization);
        await context.SaveChangesAsync();

        return Results.Ok(updatedOrganization.ToFacet<Organization, OrganizationDto>());
    }

    private static async Task<IResult> DeleteOrganization(Guid id, WmsContext context)
    {
        var organization = await context.Organizations.FindAsync(id);
        if (organization == null)
            return Results.NotFound($"Organization with ID {id} not found");

        context.Organizations.Remove(organization);
        await context.SaveChangesAsync();

        return Results.NoContent();
    }
}

// DTOs using Facet
[Facet(typeof(Organization))]
public partial class OrganizationDto
{
}

[Facet(typeof(Organization),
    exclude: [
        nameof(Organization.Id),
        nameof(Organization.CreatedAt),
        nameof(Organization.CreatedBy),
        nameof(Organization.ModifiedAt),
        nameof(Organization.ModifiedBy)],
    Kind = FacetKind.Record)]
public partial record CreateOrganizationRequest { }

[Facet(typeof(Organization),
    exclude: [
        nameof(Organization.CreatedAt),
        nameof(Organization.CreatedBy),
        nameof(Organization.ModifiedAt)],
    Kind = FacetKind.Record)]
public partial record UpdateOrganizationRequest { }
