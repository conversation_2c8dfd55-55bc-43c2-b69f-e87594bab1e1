﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class IssuingOrganizationConfiguration : IEntityTypeConfiguration<IssuingOrganization>
{
    public void Configure(EntityTypeBuilder<IssuingOrganization> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Name)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.Abbreviation)
               .HasMaxLength(50)
               .IsRequired();

        builder.Property(e => e.Website)
               .HasMaxLength(255);

        builder.Property(e => e.Description);
    }
}
