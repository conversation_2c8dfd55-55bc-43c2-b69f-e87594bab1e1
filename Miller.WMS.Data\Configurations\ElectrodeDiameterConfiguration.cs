﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeDiameterConfiguration : IEntityTypeConfiguration<ElectrodeDiameter>
{
    public void Configure(EntityTypeBuilder<ElectrodeDiameter> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Diameter)
               .HasPrecision(6, 3)
               .IsRequired();


        // Optional: if diameter should be unique per ElectrodeType
        // builder.HasIndex(e => new { e.ElectrodeTypeId, e.Diameter }).IsUnique();
    }
}
