# User

**Source File:** [User.cs](../User.cs)

## Overview
The `User` entity represents individuals who have access to the Miller WMS system. Users are associated with organizations and can have specific roles at different facilities.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the user |
| `Name` | `string` | Yes | User's full name (max 255 characters) |
| `Email` | `string` | Yes | User's email address (max 255 characters) |
| `OrganizationId` | `Guid` | Yes | Foreign key to the user's primary organization |
| `CreatedAt` | `DateTime?` | No | Timestamp when the user was created |
| `CreatedBy` | `Guid?` | No | User who created this user record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this user record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Organization` | `Organization?` | The primary organization this user belongs to |
| `UserFacilityRoles` | `ICollection<UserFacilityRole>` | Collection of roles this user has at various facilities |

## Relationships

### Many-to-One Relationships
- **User → Organization**: Each user belongs to one primary organization

### One-to-Many Relationships
- **User → UserFacilityRoles**: A user can have roles at multiple facilities

## Business Rules

1. **Email Uniqueness**: User email addresses must be unique across the system
2. **Organization Association**: Every user must belong to a primary organization
3. **Name Requirements**: User names are required and should be properly formatted
4. **Multi-Facility Access**: Users can have different roles at different facilities
5. **Active Status**: Consider implementing user activation/deactivation mechanisms

## Usage Examples

### Creating a New User
```csharp
var user = new User
{
    Id = Guid.NewGuid(),
    Name = "John Smith",
    Email = "<EMAIL>",
    OrganizationId = organizationId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### User with Multiple Facility Roles
```csharp
var user = new User
{
    Name = "Jane Doe",
    Email = "<EMAIL>",
    OrganizationId = organizationId,
    UserFacilityRoles = new List<UserFacilityRole>
    {
        new UserFacilityRole 
        { 
            FacilityId = facility1Id, 
            RoleAtFacility = "Supervisor" 
        },
        new UserFacilityRole 
        { 
            FacilityId = facility2Id, 
            RoleAtFacility = "Operator" 
        }
    }
};
```

## Common User Roles

While roles are stored as strings and can be customized, common roles include:

- **Administrator**: Full system access and configuration
- **Manager**: Facility-level management and oversight
- **Supervisor**: Work center supervision and coordination
- **Operator**: Equipment operation and welding tasks
- **Inspector**: Quality control and inspection duties
- **Maintenance**: Equipment maintenance and repair
- **Viewer**: Read-only access for reporting and monitoring

## Security Considerations

1. **Authentication**: Users should be authenticated through secure mechanisms
2. **Authorization**: Role-based access control should be implemented
3. **Audit Trail**: User actions should be logged for compliance
4. **Password Management**: Implement secure password policies
5. **Session Management**: Proper session handling and timeout

## Related Entities

- [Organization](Organization.md) - The user's primary organization
- [UserFacilityRole](UserFacilityRole.md) - Roles the user has at specific facilities
- [Facility](Facility.md) - Facilities where the user has roles

## Database Considerations

- The `Email` property should be unique and indexed
- The `Name` property has a maximum length of 255 characters
- Consider indexing on `OrganizationId` for query performance
- Foreign key constraints should be properly configured
- Implement soft delete to maintain audit trails
- Consider adding fields for user status, last login, etc.

## Integration Points

- **Identity Management**: Integration with corporate identity systems
- **Email Systems**: For notifications and communications
- **Reporting Systems**: User activity and access reports
- **Mobile Applications**: User authentication for mobile access
