<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>

    <!-- Set logging level for Debezium -->
    <logger name="io.debezium" level="INFO"/>
    
    <!-- Set logging level for Elasticsearch -->
    <logger name="co.elastic.clients" level="INFO"/>
    
    <!-- Set logging level for our application -->
    <logger name="com.miller.wms.cdc" level="INFO"/>

    <root level="INFO">
        <appender-ref ref="STDOUT"/>
    </root>
</configuration>
