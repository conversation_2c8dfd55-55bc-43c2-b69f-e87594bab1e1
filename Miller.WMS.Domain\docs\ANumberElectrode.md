# ANumberElectrode

**Source File:** [ANumberElectrode.cs](../ANumberElectrode.cs)

## Overview
The `ANumberElectrode` entity represents A-Number electrode classifications used in welding procedure qualification. A-Numbers are groupings of filler metals with similar metallurgical characteristics for the purpose of procedure qualification testing.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the A-Number electrode |
| `Name` | `string` | Yes | A-Number designation (max 255 characters) |
| `SpecificationId` | `Guid` | Yes | Foreign key to the governing specification |
| `MetalChemicalCompositionId` | `Guid` | Yes | Foreign key to the chemical composition |
| `CreatedAt` | `DateTime?` | No | Timestamp when the A-Number was created |
| `CreatedBy` | `Guid?` | No | User who created the A-Number record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the A-Number record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Specification` | `Specification?` | The specification that defines this A-Number |
| `MetalChemicalComposition` | `MetalChemicalComposition?` | The chemical composition of this A-Number |

## Relationships

### Many-to-One Relationships
- **ANumberElectrode → Specification**: Each A-Number belongs to one specification
- **ANumberElectrode → MetalChemicalComposition**: Each A-Number has one chemical composition

## A-Number Classifications

A-Numbers group filler metals with similar metallurgical characteristics:

### Common A-Numbers
- **A-1**: Carbon steel electrodes
- **A-2**: Low-alloy steel electrodes (0.5% to 2% alloy content)
- **A-3**: Low-alloy steel electrodes (2% to 5% alloy content)
- **A-4**: Low-alloy steel electrodes (5% to 10% alloy content)
- **A-5**: Stainless steel electrodes (austenitic)
- **A-6**: Stainless steel electrodes (martensitic and ferritic)
- **A-7**: Nickel and nickel-alloy electrodes
- **A-8**: Aluminum and aluminum-alloy electrodes

### Purpose of A-Numbers
1. **Procedure Qualification**: Simplify welding procedure qualification
2. **Metallurgical Grouping**: Group similar filler metals
3. **Code Compliance**: Meet ASME and AWS requirements
4. **Quality Assurance**: Ensure consistent welding procedures

## Business Rules

1. **Specification Association**: Every A-Number must belong to a specification
2. **Chemical Composition**: Every A-Number must have an associated chemical composition
3. **Name Uniqueness**: A-Number names should be unique within specifications
4. **Metallurgical Consistency**: Chemical composition must align with A-Number classification
5. **Code Compliance**: A-Numbers must comply with applicable welding codes

## Usage Examples

### Creating an A-1 Carbon Steel A-Number
```csharp
var aNumber1 = new ANumberElectrode
{
    Id = Guid.NewGuid(),
    Name = "A-1",
    SpecificationId = asmeSection2SpecId,
    MetalChemicalCompositionId = carbonSteelCompId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = welding_engineer_id
};
```

### Creating an A-5 Stainless Steel A-Number
```csharp
var aNumber5 = new ANumberElectrode
{
    Name = "A-5",
    SpecificationId = asmeSection2SpecId,
    MetalChemicalComposition = new MetalChemicalComposition
    {
        MaxCarbon = 0.08m,
        MinChromium = 18.0m,
        MaxChromium = 20.0m,
        MinNickel = 8.0m,
        MaxNickel = 12.0m
    }
};
```

## Procedure Qualification Applications

### ASME Section IX
A-Numbers are used in ASME Section IX for:
- **Welding Procedure Specification (WPS)**: Define qualified filler metals
- **Procedure Qualification Record (PQR)**: Document qualification testing
- **Welder Performance Qualification**: Qualify welders on specific A-Numbers

### AWS D1.1
A-Numbers are referenced in AWS D1.1 for:
- **Prequalified Procedures**: Use of prequalified welding procedures
- **Qualification Testing**: Procedure and welder qualification
- **Filler Metal Selection**: Approved filler metal groups

## Metallurgical Considerations

### Chemical Composition Ranges
Each A-Number has defined chemical composition ranges:
- **Carbon Content**: Maximum carbon limits for weldability
- **Alloy Content**: Total alloy content ranges
- **Specific Elements**: Limits on chromium, nickel, molybdenum, etc.

### Mechanical Properties
A-Numbers group filler metals with similar:
- **Strength Levels**: Yield and tensile strength ranges
- **Toughness**: Impact toughness characteristics
- **Ductility**: Elongation and reduction of area

### Heat Treatment Response
Filler metals in the same A-Number have similar:
- **Hardenability**: Response to cooling rates
- **Tempering Response**: Behavior during post-weld heat treatment
- **Stress Relief**: Response to stress relief treatments

## Related Entities

- [Specification](Specification.md) - The governing specification
- [MetalChemicalComposition](MetalChemicalComposition.md) - Chemical composition details
- [ElectrodeClassification](ElectrodeClassification.md) - Related electrode classifications
- [Material](Material.md) - Base materials that may reference A-Numbers

## Database Considerations

- The `Name` property has a maximum length of 255 characters
- Index on `SpecificationId` for specification-based queries
- Index on `Name` for A-Number lookups
- Foreign key constraints should be properly configured
- Consider unique constraints on specification + name combinations
- Ensure chemical composition consistency validation

## Code Compliance

### ASME Section IX Requirements
- A-Numbers must comply with ASME Section IX Table QW-442
- Chemical composition must fall within specified ranges
- Mechanical properties must meet minimum requirements

### AWS Requirements
- A-Numbers must align with AWS filler metal specifications
- Classification systems must be consistent
- Quality requirements must be maintained

## Quality Assurance

### Documentation Requirements
- Complete chemical analysis certificates
- Mechanical property test results
- Compliance with specification requirements
- Traceability to filler metal lots

### Validation Procedures
- Chemical composition verification
- Mechanical property testing
- Procedure qualification testing
- Ongoing quality monitoring
