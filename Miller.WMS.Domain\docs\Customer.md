# Customer

**Source File:** [Customer.cs](../Customer.cs)

## Overview
The `Customer` entity represents external customers or clients who receive welding services or products. This entity is essential for tracking customer relationships, requirements, and facility associations.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the customer |
| `Name` | `string` | Yes | Customer company or organization name |
| `Code` | `string` | Yes | Customer code for identification and reference |
| `CreatedAt` | `DateTime?` | No | Timestamp when the customer was created |
| `CreatedBy` | `Guid?` | No | User who created the customer record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the customer record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Facilities` | `ICollection<CustomerFacility>` | Collection of customer facility relationships |

## Relationships

### One-to-Many Relationships
- **Customer → CustomerFacility**: A customer can have relationships with multiple facilities

## Customer Types

While not explicitly modeled as an enumeration, customers typically fall into categories:

### Industry Sectors
- **Aerospace**: Aircraft and spacecraft manufacturers
- **Automotive**: Vehicle manufacturers and suppliers
- **Construction**: Building and infrastructure companies
- **Energy**: Power generation and oil & gas companies
- **Manufacturing**: General manufacturing companies
- **Marine**: Shipbuilding and offshore companies
- **Pipeline**: Pipeline construction and maintenance
- **Rail**: Railroad and transit companies

### Customer Sizes
- **Enterprise**: Large multinational corporations
- **Mid-Market**: Regional companies with multiple locations
- **Small Business**: Local companies with single locations
- **Government**: Federal, state, and local government entities

## Business Rules

1. **Name Uniqueness**: Customer names should be unique in the system
2. **Code Uniqueness**: Customer codes must be unique for identification
3. **Facility Relationships**: Customers can be associated with multiple facilities
4. **Active Status**: Consider implementing customer status tracking
5. **Contact Information**: Maintain current customer contact information

## Usage Examples

### Creating a New Customer
```csharp
var customer = new Customer
{
    Id = Guid.NewGuid(),
    Name = "Boeing Commercial Airplanes",
    Code = "BOEING-CA",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "sales_admin"
};
```

### Customer with Facility Relationships
```csharp
var customer = new Customer
{
    Name = "General Motors Corporation",
    Code = "GM-CORP",
    Facilities = new List<CustomerFacility>
    {
        new CustomerFacility 
        { 
            CustomerId = customerId,
            FacilityId = detroitPlantId,
            RelationshipType = "Primary Manufacturing"
        },
        new CustomerFacility 
        { 
            CustomerId = customerId,
            FacilityId = springHillPlantId,
            RelationshipType = "Secondary Manufacturing"
        }
    },
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "account_manager"
};
```

## Customer Information

Consider extending the customer entity with additional information:

### Contact Information
- **Primary Contact**: Main point of contact
- **Phone**: Primary phone number
- **Email**: Primary email address
- **Address**: Primary business address
- **Website**: Company website URL

### Business Information
- **Industry**: Primary industry sector
- **Annual Revenue**: Company annual revenue
- **Employee Count**: Number of employees
- **Founded**: Year the company was founded
- **Stock Symbol**: If publicly traded

### Account Information
- **Account Manager**: Assigned account manager
- **Credit Limit**: Approved credit limit
- **Payment Terms**: Standard payment terms
- **Discount Level**: Applicable discount percentage
- **Priority Level**: Customer priority classification

## Customer Relationships

### Facility Associations
Customers are associated with facilities through the `CustomerFacility` relationship:

- **Service Locations**: Facilities where services are provided
- **Delivery Locations**: Facilities where products are delivered
- **Billing Locations**: Facilities responsible for billing
- **Technical Contacts**: Facility-specific technical contacts

### Relationship Types
- **Direct Customer**: Direct business relationship
- **Subcontractor**: Customer through subcontracting
- **Partner**: Strategic business partner
- **Government**: Government contract customer

## Customer Requirements

### Quality Requirements
- **Certifications**: Required quality certifications (ISO, AS9100, etc.)
- **Inspection**: Required inspection and testing procedures
- **Documentation**: Required documentation and traceability
- **Compliance**: Regulatory compliance requirements

### Technical Requirements
- **Specifications**: Customer-specific welding specifications
- **Procedures**: Required welding procedures and qualifications
- **Materials**: Approved materials and suppliers
- **Testing**: Required testing and validation procedures

### Commercial Requirements
- **Pricing**: Customer-specific pricing agreements
- **Terms**: Payment and delivery terms
- **Contracts**: Master service agreements and contracts
- **Insurance**: Required insurance coverage and limits

## Customer Service

### Service Levels
- **Standard**: Basic service level
- **Premium**: Enhanced service with faster response
- **Enterprise**: Dedicated support and account management
- **Strategic**: Highest level with executive involvement

### Support Services
- **Technical Support**: Engineering and technical assistance
- **Training**: Operator and technician training
- **Maintenance**: Equipment maintenance and support
- **Consulting**: Process optimization and consulting

## Related Entities

- [CustomerFacility](CustomerFacility.md) - Customer facility relationships
- [Facility](Facility.md) - Facilities associated with customers
- [Organization](Organization.md) - The organization serving the customer

## Database Considerations

- Index on `Name` for customer searches
- Index on `Code` for customer code lookups
- Consider full-text search on `Name` for flexible searching
- Implement unique constraints on `Code`
- Foreign key relationships should be properly configured
- Consider adding customer status and classification fields

## Customer Analytics

### Performance Metrics
- **Revenue**: Total revenue from customer
- **Profitability**: Customer profitability analysis
- **Growth**: Revenue growth trends
- **Satisfaction**: Customer satisfaction scores

### Operational Metrics
- **Order Volume**: Number and size of orders
- **Delivery Performance**: On-time delivery rates
- **Quality Performance**: Quality metrics and defect rates
- **Service Utilization**: Usage of various services

## Integration Points

### CRM Systems
- **Contact Management**: Customer contact information
- **Opportunity Tracking**: Sales opportunities and pipeline
- **Communication History**: Customer interaction history
- **Account Planning**: Strategic account planning

### ERP Systems
- **Order Management**: Customer orders and fulfillment
- **Billing**: Invoice generation and payment tracking
- **Inventory**: Customer-specific inventory management
- **Shipping**: Delivery and logistics coordination
