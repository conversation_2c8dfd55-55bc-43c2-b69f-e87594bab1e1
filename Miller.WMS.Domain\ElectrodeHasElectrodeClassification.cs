﻿namespace Miller.WMS.Domain;

public class ElectrodeHasElectrodeClassification : IEntityWithAudit
{
    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }
    public Guid ElectrodeId { get; set; }
    public Electrode Electrode { get; set; } = null!;

    public Guid ElectrodeClassificationId { get; set; }
    public ElectrodeClassification ElectrodeClassification { get; set; } = null!;
}
