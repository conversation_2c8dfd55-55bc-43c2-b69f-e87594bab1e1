﻿// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Miller.WMS.Shared.Data;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

#nullable disable

namespace Miller.WMS.Data.Migrations
{
    [DbContext(typeof(WmsContext))]
    partial class WmsContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.8")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("Miller.WMS.Domain.ANumberElectrode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("MetalChemicalCompositionId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("SpecificationId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("MetalChemicalCompositionId");

                    b.HasIndex("SpecificationId");

                    b.ToTable("ANumberElectrode");
                });

            modelBuilder.Entity("Miller.WMS.Domain.CatElectrodeSpecialRule", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<decimal?>("MinimumYieldStrength")
                        .HasPrecision(6, 3)
                        .HasColumnType("numeric(6,3)");

                    b.Property<Guid?>("SpecificationId")
                        .HasColumnType("uuid");

                    b.Property<string>("WeldingArcDesignationSpec")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("WeldingProcess")
                        .HasColumnType("integer");

                    b.Property<Guid>("WeldingProcessId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("SpecificationId");

                    b.ToTable("CatElectrodeSpecialRules");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Customer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("Code")
                        .IsUnique();

                    b.ToTable("Customers");
                });

            modelBuilder.Entity("Miller.WMS.Domain.CustomerFacility", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("AddressAdditionalInformation")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("CustomerId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("CustomerId1")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("State")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("CustomerId");

                    b.HasIndex("CustomerId1");

                    b.ToTable("CustomerFacilities");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Electrode", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("MetalChemicalCompositionId")
                        .HasColumnType("uuid");

                    b.Property<string>("TradeName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("MetalChemicalCompositionId");

                    b.ToTable("Electrodes");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Classification")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int?>("CoveringType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<decimal>("Elongation")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("MetalChemicalCompositionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SpecificationId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("TensileStrength")
                        .HasPrecision(6, 3)
                        .HasColumnType("numeric(6,3)");

                    b.Property<Guid?>("TungstenElectrodeClassificationId")
                        .HasColumnType("uuid");

                    b.Property<decimal>("YieldStrength")
                        .HasPrecision(6, 3)
                        .HasColumnType("numeric(6,3)");

                    b.HasKey("Id");

                    b.HasIndex("MetalChemicalCompositionId");

                    b.HasIndex("SpecificationId");

                    b.HasIndex("TungstenElectrodeClassificationId");

                    b.ToTable("ElectrodeClassifications");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassificationCurrentType", b =>
                {
                    b.Property<Guid>("ElectrodeClassificationId")
                        .HasColumnType("uuid");

                    b.Property<int>("CurrentType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("ElectrodeClassificationId", "CurrentType");

                    b.HasIndex("CurrentType");

                    b.ToTable("ElectrodeClassificationCurrentTypes");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassificationHasWeldingPosition", b =>
                {
                    b.Property<Guid>("ElectrodeClassificationId")
                        .HasColumnType("uuid");

                    b.Property<int>("WeldingPosition")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("ElectrodeClassificationId", "WeldingPosition");

                    b.ToTable("ElectrodeClassificationWeldingPositions");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassificationHasWeldingProcess", b =>
                {
                    b.Property<Guid>("ElectrodeClassificationId")
                        .HasColumnType("uuid");

                    b.Property<int>("WeldingProcess")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("ElectrodeClassificationId", "WeldingProcess");

                    b.HasIndex("WeldingProcess");

                    b.ToTable("ElectrodeClassificationWeldingProcesses");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeDiameter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<decimal>("Diameter")
                        .HasPrecision(6, 3)
                        .HasColumnType("numeric(6,3)");

                    b.Property<int>("ElectrodeType")
                        .HasColumnType("integer");

                    b.Property<Guid>("ElectrodeTypeId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.ToTable("ElectrodeDiameters");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeHasElectrodeClassification", b =>
                {
                    b.Property<Guid>("ElectrodeId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ElectrodeClassificationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("ElectrodeId", "ElectrodeClassificationId");

                    b.HasIndex("ElectrodeClassificationId");

                    b.ToTable("ElectrodeHasElectrodeClassifications");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeMadeByManufacturer", b =>
                {
                    b.Property<Guid>("ElectrodeId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ManufacturerId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid?>("ManufacturerFacilityId")
                        .HasColumnType("uuid");

                    b.HasKey("ElectrodeId", "ManufacturerId");

                    b.HasIndex("ManufacturerFacilityId");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("ElectrodeMadeByManufacturers");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Equipment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("ManufacturerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("SubType")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("Equipment");
                });

            modelBuilder.Entity("Miller.WMS.Domain.EquipmentHasElectrodeClassification", b =>
                {
                    b.Property<Guid>("EquipmentId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ElectrodeClassificationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("EquipmentId", "ElectrodeClassificationId");

                    b.HasIndex("ElectrodeClassificationId");

                    b.ToTable("EquipmentHasCurrentTypes");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Facility", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Code")
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Facilities");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FacilityAreaLevelOne", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("FacilityAreaName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("FacilityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FacilityId");

                    b.ToTable("FacilityAreaLevelOnes");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FacilityAreaLevelThree", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("FacilityAreaLevelTwoId")
                        .HasColumnType("uuid");

                    b.Property<string>("FacilityAreaName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FacilityAreaLevelTwoId");

                    b.ToTable("FacilityAreaLevelThrees");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FacilityAreaLevelTwo", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("FacilityAreaLevelOneId")
                        .HasColumnType("uuid");

                    b.Property<string>("FacilityAreaName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FacilityAreaLevelOneId");

                    b.ToTable("FacilityAreaLevelTwos");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Flux", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("FluxChemicalCompositionId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsDrying")
                        .HasColumnType("boolean");

                    b.Property<bool>("IsPenetrationEnhanced")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("TradeName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("VendorRecommendationDryingFlux")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FluxChemicalCompositionId");

                    b.ToTable("Fluxes");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxChemicalComposition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Symbol")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.ToTable("FluxChemicalComposition");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxChemicalCompositionLimits", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("ChemicalConstituents")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<decimal>("ConstituentLimitMax")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal>("ConstituentLimitMin")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("FluxChemicalCompositionId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("Id");

                    b.HasIndex("FluxChemicalCompositionId");

                    b.ToTable("FluxChemicalCompositionLimits");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxClass", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("FluxClass");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxHasFluxClass", b =>
                {
                    b.Property<Guid>("FluxId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FluxClassId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("FluxId", "FluxClassId");

                    b.HasIndex("FluxClassId");

                    b.ToTable("FluxHasFluxClass");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Gas", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("GasChemicalCompositionId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("GasClassificationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("GasChemicalCompositionId");

                    b.HasIndex("GasClassificationId");

                    b.ToTable("Gases");
                });

            modelBuilder.Entity("Miller.WMS.Domain.GasChemicalComposition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<decimal?>("Argon")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("CarbonDioxide")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<decimal?>("Helium")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<decimal?>("Oxygen")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.HasKey("Id");

                    b.ToTable("GasChemicalCompositions");
                });

            modelBuilder.Entity("Miller.WMS.Domain.GasClassification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Group")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Subgroup")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("character varying(5)");

                    b.HasKey("Id");

                    b.ToTable("GasClassifications");
                });

            modelBuilder.Entity("Miller.WMS.Domain.IssuingOrganization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Abbreviation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Website")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("IssuingOrganizations");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Manufacturer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<bool>("IsActive")
                        .HasColumnType("boolean");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Manufacturers");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ManufacturerFacility", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Address")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("City")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Country")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("ManufacturerId")
                        .HasColumnType("uuid");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("State")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("ZipCode")
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.HasKey("Id");

                    b.HasIndex("ManufacturerId");

                    b.ToTable("ManufacturerFacilities");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Material", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("BaseMetalGroup")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<string>("BaseMetalSubGroup")
                        .IsRequired()
                        .HasMaxLength(10)
                        .HasColumnType("character varying(10)");

                    b.Property<bool>("CharpyRequirement")
                        .HasColumnType("boolean");

                    b.Property<string>("Class")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<string>("Condition")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("ForgeMaterial")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<Guid?>("FulfilledById")
                        .HasColumnType("uuid");

                    b.Property<string>("Grade")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<decimal?>("Hardness")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<string>("HeatTreatment")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<decimal?>("MaterialDiameter")
                        .HasPrecision(6, 3)
                        .HasColumnType("numeric(6,3)");

                    b.Property<string>("MaterialForm")
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("MaterialThickness")
                        .HasPrecision(6, 3)
                        .HasColumnType("numeric(6,3)");

                    b.Property<string>("Spec")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<decimal?>("WeldCarbonEquivalent")
                        .HasPrecision(5, 2)
                        .HasColumnType("numeric(5,2)");

                    b.Property<int?>("WeldMinYieldStrength")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FulfilledById");

                    b.ToTable("Materials");
                });

            modelBuilder.Entity("Miller.WMS.Domain.MaterialSubstitution", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<int>("ApprovalType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<Guid>("MaterialId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SpecificationId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("SubstitutedById")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("MaterialId");

                    b.HasIndex("SpecificationId");

                    b.HasIndex("SubstitutedById");

                    b.ToTable("MaterialSubstitutions");
                });

            modelBuilder.Entity("Miller.WMS.Domain.MetalChemicalComposition", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<decimal?>("MaxCarbon")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MaxChromium")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MaxManganese")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MaxMolybdenum")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MaxNickel")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MaxSilicon")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinCarbon")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinChromium")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinManganese")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinMolybdenum")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinNickel")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinSilicon")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.HasKey("Id");

                    b.ToTable("MetalChemicalCompositions");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Organization", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<int?>("IndustryType")
                        .HasColumnType("integer");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.ToTable("Organizations");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Specification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("character varying(100)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("IssuingOrganizationId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("OtherSpecification")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("SpecificationType")
                        .HasColumnType("integer");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<Guid?>("SupersededSpecId")
                        .HasColumnType("uuid");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.HasIndex("IssuingOrganizationId");

                    b.HasIndex("SupersededSpecId");

                    b.ToTable("Specification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.SupplementalFillerMetal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("TypeName")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("SupplementalFillerMetals");
                });

            modelBuilder.Entity("Miller.WMS.Domain.TungstenElectrodeClassification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Classification")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Color")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("character varying(20)");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<decimal?>("MaxImpurities")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MaxMass")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<decimal?>("MinMass")
                        .HasPrecision(5, 4)
                        .HasColumnType("numeric(5,4)");

                    b.Property<string>("PrincipalOxide")
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("TungstenElectrodeClassifications");
                });

            modelBuilder.Entity("Miller.WMS.Domain.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid>("OrganizationId")
                        .HasColumnType("uuid");

                    b.HasKey("Id");

                    b.HasIndex("OrganizationId");

                    b.ToTable("Users");
                });

            modelBuilder.Entity("Miller.WMS.Domain.UserFacilityRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FacilityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("RoleAtFacility")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("UserId", "FacilityId");

                    b.HasIndex("FacilityId");

                    b.ToTable("UserFacilities");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Waveform", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.HasKey("Id");

                    b.ToTable("Waveform");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenter", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<Guid?>("FacilityAreaLevelOneId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FacilityAreaLevelThreeId")
                        .HasColumnType("uuid");

                    b.Property<Guid?>("FacilityAreaLevelTwoId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("FacilityId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<int>("Status")
                        .HasColumnType("integer");

                    b.Property<int>("Type")
                        .HasColumnType("integer");

                    b.HasKey("Id");

                    b.HasIndex("FacilityAreaLevelOneId");

                    b.HasIndex("FacilityAreaLevelThreeId");

                    b.HasIndex("FacilityAreaLevelTwoId");

                    b.HasIndex("FacilityId");

                    b.ToTable("WorkCenter");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasElectrode", b =>
                {
                    b.Property<Guid>("WorkCenterId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("ElectrodeId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("WorkCenterId", "ElectrodeId");

                    b.HasIndex("ElectrodeId");

                    b.ToTable("WorkCenterHasElectrode");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasEquipment", b =>
                {
                    b.Property<Guid>("WorkCenterId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("EquipmentId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("WorkCenterId", "EquipmentId");

                    b.HasIndex("EquipmentId");

                    b.ToTable("WorkCenterHasEquipment");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasGas", b =>
                {
                    b.Property<Guid>("WorkCenterId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("GasId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("WorkCenterId", "GasId");

                    b.HasIndex("GasId");

                    b.ToTable("WorkCenterHasGas");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasWaveform", b =>
                {
                    b.Property<Guid>("WorkCenterId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("WaveformId")
                        .HasColumnType("uuid");

                    b.Property<DateTime?>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("CreatedBy")
                        .HasColumnType("text");

                    b.Property<DateTime?>("LastModifiedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("LastModifiedBy")
                        .HasColumnType("text");

                    b.HasKey("WorkCenterId", "WaveformId");

                    b.HasIndex("WaveformId");

                    b.ToTable("WorkCenterHasWaveform");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ANumberElectrode", b =>
                {
                    b.HasOne("Miller.WMS.Domain.MetalChemicalComposition", "MetalChemicalComposition")
                        .WithMany()
                        .HasForeignKey("MetalChemicalCompositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Specification", "Specification")
                        .WithMany()
                        .HasForeignKey("SpecificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MetalChemicalComposition");

                    b.Navigation("Specification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.CatElectrodeSpecialRule", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Specification", "Specification")
                        .WithMany()
                        .HasForeignKey("SpecificationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Specification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.CustomerFacility", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Customer", "Customer")
                        .WithMany()
                        .HasForeignKey("CustomerId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Customer", null)
                        .WithMany("Facilities")
                        .HasForeignKey("CustomerId1");

                    b.Navigation("Customer");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Electrode", b =>
                {
                    b.HasOne("Miller.WMS.Domain.MetalChemicalComposition", "MetalChemicalComposition")
                        .WithMany()
                        .HasForeignKey("MetalChemicalCompositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MetalChemicalComposition");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassification", b =>
                {
                    b.HasOne("Miller.WMS.Domain.MetalChemicalComposition", "MetalChemicalComposition")
                        .WithMany()
                        .HasForeignKey("MetalChemicalCompositionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Specification", "Specification")
                        .WithMany()
                        .HasForeignKey("SpecificationId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.TungstenElectrodeClassification", "TungstenElectrodeClassification")
                        .WithMany()
                        .HasForeignKey("TungstenElectrodeClassificationId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("MetalChemicalComposition");

                    b.Navigation("Specification");

                    b.Navigation("TungstenElectrodeClassification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassificationCurrentType", b =>
                {
                    b.HasOne("Miller.WMS.Domain.ElectrodeClassification", "ElectrodeClassification")
                        .WithMany()
                        .HasForeignKey("ElectrodeClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ElectrodeClassification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassificationHasWeldingPosition", b =>
                {
                    b.HasOne("Miller.WMS.Domain.ElectrodeClassification", "ElectrodeClassification")
                        .WithMany()
                        .HasForeignKey("ElectrodeClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ElectrodeClassification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeClassificationHasWeldingProcess", b =>
                {
                    b.HasOne("Miller.WMS.Domain.ElectrodeClassification", "ElectrodeClassification")
                        .WithMany()
                        .HasForeignKey("ElectrodeClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ElectrodeClassification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeHasElectrodeClassification", b =>
                {
                    b.HasOne("Miller.WMS.Domain.ElectrodeClassification", "ElectrodeClassification")
                        .WithMany()
                        .HasForeignKey("ElectrodeClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Electrode", "Electrode")
                        .WithMany()
                        .HasForeignKey("ElectrodeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Electrode");

                    b.Navigation("ElectrodeClassification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ElectrodeMadeByManufacturer", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Electrode", "Electrode")
                        .WithMany()
                        .HasForeignKey("ElectrodeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.ManufacturerFacility", "ManufacturerFacility")
                        .WithMany()
                        .HasForeignKey("ManufacturerFacilityId");

                    b.HasOne("Miller.WMS.Domain.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Electrode");

                    b.Navigation("Manufacturer");

                    b.Navigation("ManufacturerFacility");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Equipment", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("Miller.WMS.Domain.EquipmentHasElectrodeClassification", b =>
                {
                    b.HasOne("Miller.WMS.Domain.ElectrodeClassification", "ElectrodeClassification")
                        .WithMany()
                        .HasForeignKey("ElectrodeClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Equipment", "Equipment")
                        .WithMany()
                        .HasForeignKey("EquipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ElectrodeClassification");

                    b.Navigation("Equipment");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Facility", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Organization", "Organization")
                        .WithMany("Facilities")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FacilityAreaLevelOne", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Facility", "Facility")
                        .WithMany()
                        .HasForeignKey("FacilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Facility");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FacilityAreaLevelThree", b =>
                {
                    b.HasOne("Miller.WMS.Domain.FacilityAreaLevelTwo", "FacilityAreaLevelTwo")
                        .WithMany()
                        .HasForeignKey("FacilityAreaLevelTwoId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FacilityAreaLevelTwo");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FacilityAreaLevelTwo", b =>
                {
                    b.HasOne("Miller.WMS.Domain.FacilityAreaLevelOne", "FacilityAreaLevelOne")
                        .WithMany()
                        .HasForeignKey("FacilityAreaLevelOneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FacilityAreaLevelOne");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Flux", b =>
                {
                    b.HasOne("Miller.WMS.Domain.FluxChemicalComposition", "FluxChemicalComposition")
                        .WithMany()
                        .HasForeignKey("FluxChemicalCompositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FluxChemicalComposition");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxChemicalCompositionLimits", b =>
                {
                    b.HasOne("Miller.WMS.Domain.FluxChemicalComposition", "FluxChemicalComposition")
                        .WithMany("Limits")
                        .HasForeignKey("FluxChemicalCompositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FluxChemicalComposition");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxHasFluxClass", b =>
                {
                    b.HasOne("Miller.WMS.Domain.FluxClass", "FluxClass")
                        .WithMany()
                        .HasForeignKey("FluxClassId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Flux", "Flux")
                        .WithMany()
                        .HasForeignKey("FluxId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Flux");

                    b.Navigation("FluxClass");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Gas", b =>
                {
                    b.HasOne("Miller.WMS.Domain.GasChemicalComposition", "GasChemicalComposition")
                        .WithMany()
                        .HasForeignKey("GasChemicalCompositionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.GasClassification", "GasClassification")
                        .WithMany()
                        .HasForeignKey("GasClassificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GasChemicalComposition");

                    b.Navigation("GasClassification");
                });

            modelBuilder.Entity("Miller.WMS.Domain.ManufacturerFacility", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Manufacturer", "Manufacturer")
                        .WithMany()
                        .HasForeignKey("ManufacturerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Manufacturer");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Material", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Material", "FulfilledBy")
                        .WithMany()
                        .HasForeignKey("FulfilledById");

                    b.Navigation("FulfilledBy");
                });

            modelBuilder.Entity("Miller.WMS.Domain.MaterialSubstitution", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Material", "Material")
                        .WithMany()
                        .HasForeignKey("MaterialId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Specification", "Specification")
                        .WithMany()
                        .HasForeignKey("SpecificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Material", "SubstitutedBy")
                        .WithMany()
                        .HasForeignKey("SubstitutedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Material");

                    b.Navigation("Specification");

                    b.Navigation("SubstitutedBy");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Specification", b =>
                {
                    b.HasOne("Miller.WMS.Domain.IssuingOrganization", "IssuingOrganization")
                        .WithMany()
                        .HasForeignKey("IssuingOrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.Specification", "SupersededSpec")
                        .WithMany("SupersedingSpecs")
                        .HasForeignKey("SupersededSpecId");

                    b.Navigation("IssuingOrganization");

                    b.Navigation("SupersededSpec");
                });

            modelBuilder.Entity("Miller.WMS.Domain.User", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Organization", "Organization")
                        .WithMany("Users")
                        .HasForeignKey("OrganizationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Organization");
                });

            modelBuilder.Entity("Miller.WMS.Domain.UserFacilityRole", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Facility", "Facility")
                        .WithMany("UserFacilityRoles")
                        .HasForeignKey("FacilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.User", "User")
                        .WithMany("UserFacilityRoles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Facility");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenter", b =>
                {
                    b.HasOne("Miller.WMS.Domain.FacilityAreaLevelOne", "FacilityAreaLevelOne")
                        .WithMany()
                        .HasForeignKey("FacilityAreaLevelOneId");

                    b.HasOne("Miller.WMS.Domain.FacilityAreaLevelThree", "FacilityAreaLevelThree")
                        .WithMany()
                        .HasForeignKey("FacilityAreaLevelThreeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Miller.WMS.Domain.FacilityAreaLevelTwo", "FacilityAreaLevelTwo")
                        .WithMany()
                        .HasForeignKey("FacilityAreaLevelTwoId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Miller.WMS.Domain.Facility", "Facility")
                        .WithMany()
                        .HasForeignKey("FacilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Facility");

                    b.Navigation("FacilityAreaLevelOne");

                    b.Navigation("FacilityAreaLevelThree");

                    b.Navigation("FacilityAreaLevelTwo");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasElectrode", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Electrode", "Electrode")
                        .WithMany()
                        .HasForeignKey("ElectrodeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.WorkCenter", "WorkCenter")
                        .WithMany()
                        .HasForeignKey("WorkCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Electrode");

                    b.Navigation("WorkCenter");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasEquipment", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Equipment", "Equipment")
                        .WithMany()
                        .HasForeignKey("EquipmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.WorkCenter", "WorkCenter")
                        .WithMany()
                        .HasForeignKey("WorkCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Equipment");

                    b.Navigation("WorkCenter");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasGas", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Gas", "Gas")
                        .WithMany()
                        .HasForeignKey("GasId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.WorkCenter", "WorkCenter")
                        .WithMany()
                        .HasForeignKey("WorkCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Gas");

                    b.Navigation("WorkCenter");
                });

            modelBuilder.Entity("Miller.WMS.Domain.WorkCenterHasWaveform", b =>
                {
                    b.HasOne("Miller.WMS.Domain.Waveform", "Waveform")
                        .WithMany()
                        .HasForeignKey("WaveformId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Miller.WMS.Domain.WorkCenter", "WorkCenter")
                        .WithMany()
                        .HasForeignKey("WorkCenterId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Waveform");

                    b.Navigation("WorkCenter");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Customer", b =>
                {
                    b.Navigation("Facilities");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Facility", b =>
                {
                    b.Navigation("UserFacilityRoles");
                });

            modelBuilder.Entity("Miller.WMS.Domain.FluxChemicalComposition", b =>
                {
                    b.Navigation("Limits");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Organization", b =>
                {
                    b.Navigation("Facilities");

                    b.Navigation("Users");
                });

            modelBuilder.Entity("Miller.WMS.Domain.Specification", b =>
                {
                    b.Navigation("SupersedingSpecs");
                });

            modelBuilder.Entity("Miller.WMS.Domain.User", b =>
                {
                    b.Navigation("UserFacilityRoles");
                });
#pragma warning restore 612, 618
        }
    }
}
