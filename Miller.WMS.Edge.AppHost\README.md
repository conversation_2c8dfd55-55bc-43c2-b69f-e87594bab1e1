# Miller WMS AppHost - Service Orchestration

The Miller WMS AppHost is the central orchestration point for the entire Miller Welding Management System. It uses .NET Aspire to manage service discovery, dependencies, and container lifecycle for all system components.

## Architecture Overview

::: mermaid
graph TB
    subgraph "Data Layer"
        PostgreSQL[PostgreSQL Database<br/>wms-core-psql]
        Elasticsearch[Elasticsearch<br/>wms-core-search]
        Redis[Redis Cache<br/>wms-edge-cache]
    end
    
    subgraph "Core Services"
        DataService[Data Service<br/>wms-core-dataservice]
        CDCService[CDC Service<br/>wms-core-cdc<br/>Java/Spring]
    end
    
    subgraph "Edge Services"
        ApiService[API Service<br/>wms-edge-api]
        WebService[Web Service<br/>wms-edge-web]
    end
    
    subgraph "External Tools"
        DbGate[DbGate<br/>Database Admin]
    end
    
    PostgreSQL --> DataService
    PostgreSQL --> CDCService
    PostgreSQL --> ApiService
    PostgreSQL --> DbGate
    
    Elasticsearch --> CDCService
    Redis --> WebService
    
    DataService --> CDCService
    ApiService --> WebService
    
    CDCService -.->|Change Data Capture| Elasticsearch
    DataService -.->|Database Seeding| PostgreSQL
:::

## Service Definitions

### Data Infrastructure

#### PostgreSQL Database (`wms-core-psql`)
- **Type**: Container (PostgreSQL)
- **Purpose**: Primary data store for all WMS entities
- **Configuration**:
  - Persistent lifetime for data durability
  - WAL level set to `logical` for Change Data Capture
  - Default credentials: `guest/guest`
- **Database**: `wms` - Main application database
- **Admin Tool**: DbGate for database administration

#### Elasticsearch (`wms-core-search`)
- **Type**: Container (Elasticsearch)
- **Purpose**: Search and analytics engine
- **Configuration**:
  - Persistent lifetime for index durability
  - Used for organization search indexing
- **Integration**: Receives data via CDC from PostgreSQL

#### Redis Cache (`wms-edge-cache`)
- **Type**: Container (Redis)
- **Purpose**: Caching layer for web application
- **Configuration**: Standard Redis configuration
- **Usage**: Session storage, API response caching

### Core Services

#### Data Service (`wms-core-dataservice`)
- **Type**: .NET Project
- **Purpose**: Database management and seeding
- **Dependencies**:
  - PostgreSQL database (`wms`)
- **Responsibilities**:
  - Database schema management
  - Initial data seeding
  - Database migrations
- **Startup Order**: First service to start (seeds database)

#### CDC Service (`wms-core-cdc`)
- **Type**: Java Spring Application
- **Purpose**: Change Data Capture using Debezium
- **Dependencies**:
  - PostgreSQL database (source)
  - Elasticsearch (target)
  - Data Service (waits for seeding)
- **Configuration**:
  - Database connection via environment variables
  - Elasticsearch connection for indexing
  - Health check endpoint: `/ping`
- **Functionality**:
  - Monitors PostgreSQL for data changes
  - Streams changes to Elasticsearch
  - Maintains search index synchronization

### Edge Services

#### API Service (`wms-edge-api`)
- **Type**: .NET Project
- **Purpose**: RESTful API for WMS operations
- **Dependencies**:
  - PostgreSQL database (`wms`)
- **Features**:
  - Carter-based API modules
  - Facet-based DTO generation
  - Organization, Facility, User management
- **Health Check**: `/health`

#### Web Service (`wms-edge-web`)
- **Type**: .NET Project
- **Purpose**: Web frontend application
- **Dependencies**:
  - Redis cache
  - API Service
- **Configuration**:
  - External HTTP endpoints enabled
  - Integrated with API service
- **Health Check**: `/health`

## Service Dependencies and Startup Order

### Dependency Chain
```
1. PostgreSQL (starts first)
2. Elasticsearch (parallel with PostgreSQL)
3. Redis (parallel with data stores)
4. Data Service (waits for PostgreSQL)
5. CDC Service (waits for PostgreSQL, Elasticsearch, Data Service)
6. API Service (waits for PostgreSQL)
7. Web Service (waits for Redis, API Service)
```

### Dependency Matrix
| Service | PostgreSQL | Elasticsearch | Redis | Data Service | API Service |
|---------|------------|---------------|-------|--------------|-------------|
| Data Service | ✅ WaitFor | - | - | - | - |
| CDC Service | ✅ WaitFor | ✅ WaitFor | - | ✅ WaitFor | - |
| API Service | ✅ WaitFor | - | - | - | - |
| Web Service | - | - | ✅ WaitFor | - | ✅ WaitFor |

## Configuration Parameters

### Database Credentials
```csharp
var username = builder.AddParameter("username", "guest", secret: false);
var password = builder.AddParameter("password", "guest", secret: false);
```

### Environment Variables (CDC Service)
- `DB_HOST`: PostgreSQL host
- `DB_PORT`: PostgreSQL port
- `DB_NAME`: Database name (`wms`)
- `DB_USER`: Database username
- `DB_PASSWORD`: Database password
- `ES_HOST`: Elasticsearch host
- `ES_PORT`: Elasticsearch port
- `ES_INDEX_NAME`: Elasticsearch index (`organizations`)
- `HEALTH_PORT`: Health check port (`8080`)

## Health Monitoring

### Health Check Endpoints
- **CDC Service**: `http://localhost:8080/ping`
- **API Service**: `http://localhost:xxxx/health`
- **Web Service**: `http://localhost:xxxx/health`

### Container Health
- PostgreSQL: Built-in container health checks
- Elasticsearch: Built-in container health checks
- Redis: Built-in container health checks

## Development Workflow

### Starting the Application
```bash
# Start entire application stack
dotnet run --project Miller.WMS.Edge.AppHost

# Access Aspire Dashboard
# Navigate to the URL shown in console output (typically http://localhost:15000)
```

### Service Access
- **Aspire Dashboard**: Monitor all services, logs, and metrics
- **DbGate**: Database administration interface
- **API Service**: RESTful endpoints for WMS operations
- **Web Service**: Main application interface

### Development Benefits
- **Service Discovery**: Automatic service-to-service communication
- **Dependency Management**: Proper startup ordering
- **Container Lifecycle**: Automatic container management
- **Health Monitoring**: Real-time service health status
- **Logging**: Centralized logging and monitoring
- **Configuration**: Environment-specific configuration management

## Scaling and Production Considerations

### Container Persistence
- **PostgreSQL**: Persistent lifetime for data durability
- **Elasticsearch**: Persistent lifetime for index durability
- **Redis**: Standard lifetime (can be made persistent if needed)

### Resource Requirements
- **PostgreSQL**: Database storage and memory requirements
- **Elasticsearch**: Index storage and search performance
- **Redis**: Memory for caching
- **Java CDC Service**: JVM memory allocation
- **.NET Services**: Standard .NET runtime requirements

### Security Considerations
- Database credentials management
- Service-to-service authentication
- Network security between containers
- External endpoint security (Web Service)

## Troubleshooting

### Common Issues
1. **Service Startup Failures**: Check dependency order and health checks
2. **Database Connection Issues**: Verify PostgreSQL container status
3. **CDC Not Working**: Check Elasticsearch connectivity and WAL level
4. **Web Service Issues**: Verify Redis and API Service availability

### Monitoring Tools
- **Aspire Dashboard**: Real-time service monitoring
- **Container Logs**: Individual service logging
- **Health Endpoints**: Service-specific health status
- **DbGate**: Database query and administration

## Technology Stack Integration

### .NET Aspire Features Used
- **Service Discovery**: Automatic service-to-service communication
- **Container Orchestration**: Docker container lifecycle management
- **Health Checks**: Built-in health monitoring for all services
- **Configuration Management**: Environment-specific configuration
- **Observability**: Integrated logging, metrics, and tracing
- **Development Dashboard**: Real-time monitoring and debugging

### Database Technologies
- **PostgreSQL 16+**: Primary relational database
  - ACID compliance for data integrity
  - Logical replication for CDC
  - JSON support for flexible schemas
- **Elasticsearch 8+**: Search and analytics engine
  - Full-text search capabilities
  - Real-time indexing via CDC
  - Aggregation and analytics
- **Redis 7+**: In-memory caching
  - Session storage
  - API response caching
  - Distributed locking

### Integration Patterns

#### Change Data Capture (CDC)
```mermaid
sequenceDiagram
    participant App as Application
    participant PG as PostgreSQL
    participant CDC as CDC Service
    participant ES as Elasticsearch

    App->>PG: Insert/Update/Delete
    PG->>PG: Write to WAL
    CDC->>PG: Read WAL Changes
    CDC->>ES: Index Changes
    ES->>ES: Update Search Index
```

#### Service Communication
```mermaid
graph LR
    Web[Web Service] -->|HTTP| API[API Service]
    API -->|EF Core| PG[PostgreSQL]
    Web -->|Cache| Redis[Redis]
    CDC -->|Debezium| PG
    CDC -->|Bulk API| ES[Elasticsearch]
```

## Deployment Scenarios

### Local Development
```bash
# Clone repository
git clone <repository-url>
cd Miller.WMS

# Start development environment
dotnet run --project Miller.WMS.Edge.AppHost

# Access services
# - Aspire Dashboard: http://localhost:15000
# - Web Application: http://localhost:5000
# - API Service: http://localhost:5001
# - DbGate: http://localhost:5002
```

### Docker Compose Alternative
For environments without Aspire support:
```yaml
# docker-compose.yml equivalent structure
version: '3.8'
services:
  postgres:
    image: postgres:16
    environment:
      POSTGRES_DB: wms
      POSTGRES_USER: guest
      POSTGRES_PASSWORD: guest
    command: postgres -c wal_level=logical

  elasticsearch:
    image: elasticsearch:8.11.0
    environment:
      discovery.type: single-node

  redis:
    image: redis:7-alpine

  # Additional services...
```

### Production Considerations
- **Container Registry**: Push images to production registry
- **Secrets Management**: Use secure secret management
- **Load Balancing**: Configure load balancers for web services
- **Monitoring**: Integrate with production monitoring systems
- **Backup**: Configure database and search index backups

## Performance Optimization

### Database Performance
- **Connection Pooling**: Configured via Aspire
- **Index Optimization**: Database indexes for query performance
- **Query Optimization**: EF Core query optimization
- **Connection Limits**: Appropriate connection pool sizing

### Caching Strategy
- **Redis Caching**: API response and session caching
- **EF Core Caching**: Second-level caching for entities
- **CDN Integration**: Static asset caching (production)

### Search Performance
- **Elasticsearch Tuning**: Index settings and mappings
- **CDC Optimization**: Batch processing for bulk updates
- **Query Optimization**: Search query performance tuning

This orchestration provides a complete, production-ready environment for the Miller WMS system with proper service dependencies, health monitoring, development tooling, and scalability considerations.
