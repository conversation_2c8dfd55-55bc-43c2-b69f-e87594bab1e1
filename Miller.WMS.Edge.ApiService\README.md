# Miller WMS API Service

This API service provides RESTful endpoints for managing Organizations, Facilities, and Users in the Miller WMS system.

## Architecture

- **<PERSON>**: Used for organizing API endpoints into modules
- **Facet**: Used for automatic DTO generation and mapping via source generators
- **Entity Framework Core**: Data access layer with PostgreSQL
- **Aspire**: Orchestration and service discovery for database connectivity
- **ASP.NET Core 9.0**: Web framework

## API Modules

### Organization Module (`/api/organizations`)

Manages organization entities including companies and business units.

**Endpoints:**
- `GET /api/organizations` - Get all organizations
- `GET /api/organizations/{id}` - Get organization by ID
- `POST /api/organizations` - Create a new organization
- `PUT /api/organizations/{id}` - Update an organization
- `DELETE /api/organizations/{id}` - Delete an organization

**Example Request (Create Organization):**
```json
{
  "name": "Miller Electric Manufacturing Co.",
  "industryType": 0,
  "status": 0
}
```

**Implementation Pattern:**
```csharp
var organization = request.ToFacet<CreateOrganizationRequest, Organization>();
organization.Id = Guid.NewGuid();
organization.CreatedAt = DateTime.UtcNow;
organization.CreatedBy = Guid.NewGuid(); // From auth token
```

### Facility Module (`/api/facilities`)

Manages facility entities including manufacturing plants and warehouses.

**Endpoints:**
- `GET /api/facilities` - Get all facilities
- `GET /api/facilities/{id}` - Get facility by ID
- `GET /api/facilities/organization/{organizationId}` - Get facilities by organization
- `POST /api/facilities` - Create a new facility
- `PUT /api/facilities/{id}` - Update a facility
- `DELETE /api/facilities/{id}` - Delete a facility

**Example Request (Create Facility):**
```json
{
  "name": "Main Manufacturing Plant",
  "code": "MMP001",
  "address": "1635 W Spencer St, Appleton, WI 54914",
  "organizationId": "12345678-1234-1234-1234-123456789012"
}
```

### User Module (`/api/users`)

Manages user entities including employees and system users.

**Endpoints:**
- `GET /api/users` - Get all users
- `GET /api/users/{id}` - Get user by ID
- `GET /api/users/organization/{organizationId}` - Get users by organization
- `GET /api/users/email/{email}` - Get user by email
- `POST /api/users` - Create a new user
- `PUT /api/users/{id}` - Update a user
- `DELETE /api/users/{id}` - Delete a user

**Example Request (Create User):**
```json
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "organizationId": "12345678-1234-1234-1234-123456789012"
}
```

## Enumerations

### OrganizationIndustryType
- `0` - Manufacturing
- `1` - ConstructionFabrication

### OrganizationStatus
- `0` - Active
- `1` - Inactive
- `2` - Suspended

## Configuration

### Database Configuration

The API service uses **Aspire-managed PostgreSQL database** instead of direct connection strings:

```csharp
// Program.cs - Aspire manages the database connection
builder.AddNpgsqlDbContext<WmsContext>("wms");
```

**AppHost Configuration:**
```csharp
// Database is defined in AppHost and referenced by ApiService
var corePsql = builder.AddPostgres("wms-core-psql", username, password)
    .WithLifetime(ContainerLifetime.Persistent)
    .WithArgs("-c", "wal_level=logical");

var corePsqlDb = corePsql.AddDatabase("wms");

var apiService = builder.AddProject<Projects.Miller_WMS_Edge_ApiService>("wms-edge-api")
    .WithReference(corePsqlDb)
    .WaitFor(corePsqlDb);
```

## Features

### Data Transfer Objects (DTOs)
- Clean separation between domain models and API contracts
- **Facet Integration**: Automatic DTO generation with `[Facet(typeof(Entity))]` attributes
- **Smart Field Exclusion**: Create/Update requests exclude system-generated fields
- **Automatic Mapping**: Uses `ToFacet<TSource, TTarget>()` for seamless entity conversion
- **Audit Trail Management**: System automatically handles CreatedAt, CreatedBy, ModifiedAt, ModifiedBy
- Includes related entity data where appropriate

### Create/Update Pattern
```csharp
// Create: Convert request to entity, set system fields
var entity = request.ToFacet<CreateRequest, Entity>();
entity.Id = Guid.NewGuid();
entity.CreatedAt = DateTime.UtcNow;
entity.CreatedBy = GetCurrentUserId(); // From auth token

// Update: Validate ID consistency, preserve creation audit
var updated = request.ToFacet<UpdateRequest, Entity>();
if (urlId != updated.Id)
    return Results.BadRequest("URL ID does not match request object ID");

var existing = await context.FindAsync(urlId);
updated.CreatedAt = existing.CreatedAt; // Preserve creation audit
updated.CreatedBy = existing.CreatedBy; // Preserve creation audit
updated.ModifiedAt = DateTime.UtcNow;
updated.ModifiedBy = GetCurrentUserId();
```

### Validation
- **ID Consistency**: URL ID must match request object ID for updates
- **Organization existence**: Validation for facilities and users
- **Email uniqueness**: Validation for users across the system
- **Required field**: Validation for all mandatory fields
- **Business rules**: Entity-specific business rule validation

### Error Handling
- Consistent error responses
- Not Found (404) for missing entities
- Bad Request (400) for validation errors

### OpenAPI/Swagger
- Automatic API documentation generation
- Available in development environment at `/swagger`

## Health Check

- `GET /ping` - Returns "true" if the service is running

## Running the Service

### Via Aspire (Recommended)
1. Start the entire application stack via Aspire AppHost:
   ```bash
   dotnet run --project Miller.WMS.Edge.AppHost
   ```
2. The Aspire dashboard will show all services including the API service
3. Database is automatically provisioned and configured

### Standalone (Development)
1. Ensure PostgreSQL is running and accessible
2. Configure the database connection via Aspire service discovery
3. Start the service:
   ```bash
   dotnet run --project Miller.WMS.Edge.ApiService
   ```

The service will be available at the port assigned by Aspire or `https://localhost:7xxx` for standalone.

## Dependencies

- Carter 8.2.1
- Facet 1.0.0
- Npgsql.EntityFrameworkCore.PostgreSQL 9.0.0
- Microsoft.AspNetCore.OpenApi 9.0.8
- Aspire.Npgsql.EntityFrameworkCore.PostgreSQL (via ServiceDefaults)

## Project Structure

```
Miller.WMS.Edge.ApiService/
├── Modules/
│   ├── OrganizationModule.cs
│   ├── FacilityModule.cs
│   └── UserModule.cs
├── Program.cs
├── appsettings.json
├── appsettings.Development.json
└── README.md
```

Each module contains:
- Carter module implementation
- DTOs for requests and responses
- Extension methods for entity-to-DTO mapping
- Complete CRUD operations
