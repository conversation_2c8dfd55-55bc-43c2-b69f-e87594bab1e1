﻿namespace Miller.WMS.Domain;

public class Waveform : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }


    public required string Name { get; set; }
    public required string Version { get; set; }
    public required string Description { get; set; }
}
