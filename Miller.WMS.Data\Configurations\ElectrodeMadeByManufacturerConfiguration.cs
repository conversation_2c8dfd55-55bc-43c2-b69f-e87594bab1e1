﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class ElectrodeMadeByManufacturerConfiguration : IEntityTypeConfiguration<ElectrodeMadeByManufacturer>
{
    public void Configure(EntityTypeBuilder<ElectrodeMadeByManufacturer> builder)
    {
        builder.<PERSON><PERSON><PERSON>(e => new { e.ElectrodeId, e.ManufacturerId });

        builder.HasIndex(e => e.ManufacturerId);
        builder.HasIndex(e => e.ManufacturerFacilityId);

        builder.HasOne(e => e.Electrode)
               .WithMany()
               .HasForeignKey(e => e.ElectrodeId);

        builder.HasOne(e => e.Manufacturer)
               .WithMany()
               .HasForeignKey(e => e.ManufacturerId);

        builder.Has<PERSON>ne(e => e.ManufacturerFacility)
               .WithMany()
               .HasForeignKey(e => e.ManufacturerFacilityId);
    }
}
