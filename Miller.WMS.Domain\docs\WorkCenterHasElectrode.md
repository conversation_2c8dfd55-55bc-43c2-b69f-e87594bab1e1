# WorkCenterHasElectrode

**Source File:** [WorkCenterHasElectrode.cs](../WorkCenterHasElectrode.cs)

## Overview
The `WorkCenterHasElectrode` entity represents the many-to-many relationship between work centers and electrodes, tracking which electrodes are available or assigned to specific work centers for welding operations.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `WorkCenterId` | `Guid` | Yes | Foreign key to the work center |
| `ElectrodeId` | `Guid` | Yes | Foreign key to the electrode |
| `CreatedAt` | `DateTime?` | No | Timestamp when the electrode was assigned |
| `CreatedBy` | `Guid?` | No | User who created this assignment |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this assignment |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `WorkCenter` | `WorkCenter` | The work center where electrode is available |
| `Electrode` | `Electrode` | The electrode available at the work center |

## Relationships

### Many-to-One Relationships
- **WorkCenterHasElectrode → WorkCenter**: Each assignment belongs to one work center
- **WorkCenterHasElectrode → Electrode**: Each assignment is for one electrode

## Composite Key

This entity uses a composite primary key consisting of:
- `WorkCenterId`
- `ElectrodeId`

This ensures that the same electrode can only be assigned once to a specific work center, but electrodes can be available at multiple work centers.

## Electrode Availability Types

Electrode assignments can represent different availability scenarios:

### Permanent Stock
- Electrodes permanently stocked at the work center
- Primary electrodes for the work center's typical operations
- High-volume consumables for production work

### Temporary Assignment
- Electrodes temporarily assigned for specific jobs
- Special electrodes for unique applications
- Project-specific electrode assignments

### Shared Inventory
- Electrodes shared between multiple work centers
- Common electrodes available across the facility
- Backup electrodes for emergency use

## Business Rules

1. **Work Center Status**: Only active work centers should have electrode assignments
2. **Electrode Compatibility**: Electrodes should be compatible with work center equipment
3. **Process Compatibility**: Electrodes should match work center welding processes
4. **Inventory Management**: Electrode quantities should be tracked and managed
5. **Quality Control**: Electrode storage conditions must be maintained

## Usage Examples

### Assigning Electrodes to a TIG Work Center
```csharp
var tigElectrodes = new List<WorkCenterHasElectrode>
{
    new WorkCenterHasElectrode
    {
        WorkCenterId = tigStationId,
        ElectrodeId = er70s6Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "supervisor"
    },
    new WorkCenterHasElectrode
    {
        WorkCenterId = tigStationId,
        ElectrodeId = er308lId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "supervisor"
    }
};
```

### Assigning Stick Electrodes to a Manual Work Center
```csharp
var stickElectrodes = new List<WorkCenterHasElectrode>
{
    new WorkCenterHasElectrode
    {
        WorkCenterId = manualStationId,
        ElectrodeId = e7018Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "materials_coordinator"
    },
    new WorkCenterHasElectrode
    {
        WorkCenterId = manualStationId,
        ElectrodeId = e6010Id,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "materials_coordinator"
    }
};
```

## Work Center Capabilities

Electrode assignments define work center welding capabilities:

### Process Capabilities
- **GTAW (TIG)**: Filler wires and tungsten electrodes
- **GMAW (MIG)**: Wire electrodes and flux-cored wires
- **SMAW (Stick)**: Covered electrodes for manual welding
- **FCAW**: Flux-cored electrodes for semi-automatic welding

### Material Capabilities
- **Carbon Steel**: Standard carbon steel electrodes
- **Stainless Steel**: Corrosion-resistant electrodes
- **Aluminum**: Aluminum and aluminum alloy electrodes
- **Specialty Alloys**: High-performance and exotic alloy electrodes

### Application Capabilities
- **Structural**: Electrodes for structural welding applications
- **Pressure Vessel**: Code-qualified electrodes for pressure vessels
- **Pipeline**: Electrodes for pipeline construction and repair
- **Aerospace**: High-quality electrodes for aerospace applications

## Inventory Management

This relationship enables comprehensive electrode inventory management:

### Stock Levels
- **Current Inventory**: Real-time electrode quantities
- **Minimum Stock**: Reorder points for automatic replenishment
- **Maximum Stock**: Storage capacity limits
- **Safety Stock**: Emergency inventory levels

### Consumption Tracking
- **Usage Rates**: Historical electrode consumption patterns
- **Job Consumption**: Electrode usage by specific jobs or projects
- **Efficiency Metrics**: Electrode utilization and waste tracking
- **Cost Analysis**: Electrode cost per work center and job

### Replenishment Management
- **Automatic Reordering**: Trigger reorders based on stock levels
- **Supplier Management**: Preferred suppliers for each electrode type
- **Lead Times**: Delivery lead times for planning purposes
- **Batch Tracking**: Lot and batch tracking for quality control

## Quality and Storage

### Storage Requirements
- **Environmental Conditions**: Temperature and humidity control
- **Electrode Conditioning**: Drying and reconditioning procedures
- **Shelf Life**: Electrode expiration and rotation management
- **Contamination Prevention**: Protection from contamination

### Quality Control
- **Incoming Inspection**: Verification of electrode quality upon receipt
- **Storage Monitoring**: Continuous monitoring of storage conditions
- **Condition Assessment**: Regular assessment of electrode condition
- **Traceability**: Complete traceability from manufacturer to use

## Work Planning and Scheduling

### Job Planning
- **Electrode Requirements**: Determine electrode needs for specific jobs
- **Availability Checking**: Verify electrode availability before job start
- **Alternative Selection**: Identify alternative electrodes if primary choice unavailable
- **Quantity Estimation**: Estimate electrode quantities needed

### Resource Allocation
- **Work Center Assignment**: Assign jobs to work centers based on electrode availability
- **Load Balancing**: Balance electrode usage across work centers
- **Priority Management**: Prioritize electrode allocation for critical jobs
- **Conflict Resolution**: Resolve conflicts when multiple jobs need same electrodes

## Related Entities

- [WorkCenter](WorkCenter.md) - The work center where electrodes are available
- [Electrode](Electrode.md) - The electrodes available at work centers
- [Equipment](Equipment.md) - Equipment that uses the electrodes (via WorkCenter)
- [ElectrodeClassification](ElectrodeClassification.md) - Classifications of available electrodes

## Database Considerations

- Composite primary key on `WorkCenterId` and `ElectrodeId`
- Index on `WorkCenterId` for work center electrode queries
- Index on `ElectrodeId` for electrode location queries
- Foreign key constraints should be properly configured
- Consider adding quantity and inventory tracking fields
- Implement audit triggers for assignment change tracking

## Operational Benefits

1. **Resource Planning**: Understand electrode distribution and availability
2. **Inventory Optimization**: Optimize electrode inventory levels and locations
3. **Quality Assurance**: Ensure proper electrode storage and handling
4. **Cost Control**: Track and control electrode costs by work center
5. **Production Planning**: Plan production based on electrode availability
6. **Compliance**: Maintain traceability for quality and regulatory compliance

## Future Enhancements

Consider adding additional properties for comprehensive electrode management:

### Inventory Properties
- **Quantity On Hand**: Current inventory quantity
- **Reorder Point**: Automatic reorder trigger level
- **Maximum Stock**: Storage capacity limit
- **Unit of Measure**: Pounds, kilograms, pieces, etc.

### Quality Properties
- **Lot Number**: Manufacturer lot or batch number
- **Expiration Date**: Electrode expiration or reconditioning date
- **Storage Condition**: Current storage condition status
- **Last Inspection**: Date of last quality inspection
