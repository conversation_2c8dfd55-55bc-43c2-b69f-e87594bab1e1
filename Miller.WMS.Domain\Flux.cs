﻿namespace Miller.WMS.Domain;

public class Flux : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public required string TradeName { get; set; }
    public bool IsDrying { get; set; }
    public string? VendorRecommendationDryingFlux { get; set; }
    public bool IsPenetrationEnhanced { get; set; }
    public Guid FluxChemicalCompositionId { get; set; }
    public FluxChemicalComposition? FluxChemicalComposition { get; set; }
}
