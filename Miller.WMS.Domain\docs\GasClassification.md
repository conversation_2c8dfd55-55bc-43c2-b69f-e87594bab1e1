# GasClassification

**Source File:** [GasClassification.cs](../GasClassification.cs)

## Overview
The `GasClassification` entity represents the categorization system for welding gases. This classification helps organize gases by their chemical properties, applications, and welding characteristics.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the classification |
| `Group` | `string` | Yes | Primary gas group (max 5 characters) |
| `Subgroup` | `string` | Yes | Secondary gas subgroup (max 5 characters) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the classification was created |
| `CreatedBy` | `Guid?` | No | User who created the classification record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the classification record |

## Relationships

### One-to-Many Relationships
- **GasClassification → Gas**: Classifications can apply to multiple gases

## Classification System

### Primary Groups
The classification system organizes gases into primary groups based on their chemical behavior:

#### Group I - Inert Gases
- **Description**: Chemically inert gases that do not react with the weld metal
- **Characteristics**: Provide protection without affecting weld chemistry
- **Applications**: General shielding, high-quality welding

#### Group A - Active Gases
- **Description**: Chemically active gases that can affect weld metal composition
- **Characteristics**: React with weld metal, affect penetration and properties
- **Applications**: Steel welding, penetration enhancement

#### Group M - Mixed Gases
- **Description**: Mixtures of inert and active gases
- **Characteristics**: Combine benefits of both inert and active gases
- **Applications**: Optimized welding performance for specific applications

### Subgroups
Subgroups provide more specific categorization within each primary group:

#### Inert Gas Subgroups (Group I)
- **I1**: Pure noble gases (Argon, Helium)
- **I2**: Noble gas mixtures (Ar/He)
- **I3**: Nitrogen and nitrogen-based mixtures

#### Active Gas Subgroups (Group A)
- **A1**: Pure active gases (CO₂, O₂)
- **A2**: Active gas mixtures (CO₂/O₂)

#### Mixed Gas Subgroups (Group M)
- **M1**: Argon-based mixtures with CO₂
- **M2**: Argon-based mixtures with O₂
- **M3**: Complex mixtures (Ar/CO₂/O₂)
- **M4**: Helium-based mixtures

## Common Classifications

### Standard Welding Gas Classifications
```csharp
var classifications = new List<GasClassification>
{
    // Pure Argon
    new GasClassification
    {
        Id = Guid.NewGuid(),
        Group = "I",
        Subgroup = "I1",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "gas_engineer"
    },
    
    // Ar/CO₂ Mixtures
    new GasClassification
    {
        Group = "M",
        Subgroup = "M1",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "gas_engineer"
    },
    
    // Pure CO₂
    new GasClassification
    {
        Group = "A",
        Subgroup = "A1",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "gas_engineer"
    }
};
```

## Business Rules

1. **Group Validation**: Groups must follow established classification standards
2. **Subgroup Consistency**: Subgroups must be consistent with their parent group
3. **Uniqueness**: Group/subgroup combinations should be unique
4. **Standard Compliance**: Classifications should align with industry standards
5. **Application Alignment**: Classifications should reflect actual gas applications

## Classification Applications

### Welding Process Compatibility

#### GTAW (TIG) Welding
- **Group I (Inert)**: Primary choice for TIG welding
  - I1: Pure argon for general applications
  - I2: Ar/He mixtures for aluminum and high heat input
  - I3: Nitrogen for specialized applications

#### GMAW (MIG) Welding
- **Group M (Mixed)**: Most common for MIG welding
  - M1: Ar/CO₂ mixtures for steel welding
  - M2: Ar/O₂ mixtures for stainless steel
  - M3: Complex mixtures for specialized applications
- **Group A (Active)**: For specific applications
  - A1: Pure CO₂ for deep penetration welding

#### FCAW (Flux-Cored) Welding
- **Group A (Active)**: Common for flux-cored welding
  - A1: Pure CO₂ for self-shielded wires
- **Group M (Mixed)**: For gas-shielded flux-cored
  - M1: Ar/CO₂ mixtures for improved performance

### Material Compatibility

#### Carbon Steel
- **Group M1**: Ar/CO₂ mixtures (75/25, 90/10)
- **Group A1**: Pure CO₂ for deep penetration

#### Stainless Steel
- **Group I1**: Pure argon for TIG welding
- **Group M2**: Ar/O₂ mixtures (98/2) for MIG welding

#### Aluminum
- **Group I1**: Pure argon for general welding
- **Group I2**: Ar/He mixtures for thick sections

#### Exotic Alloys
- **Group I1**: Pure argon for most applications
- **Group I2**: Ar/He mixtures for specialized requirements

## Quality and Performance Characteristics

### Group I (Inert) Characteristics
- **Arc Stability**: Excellent arc stability
- **Bead Appearance**: Superior bead appearance
- **Spatter**: Minimal spatter generation
- **Penetration**: Moderate penetration
- **Chemistry**: No effect on weld metal chemistry

### Group A (Active) Characteristics
- **Arc Stability**: Good arc stability with proper technique
- **Bead Appearance**: May require technique adjustment
- **Spatter**: Potential for increased spatter
- **Penetration**: Enhanced penetration
- **Chemistry**: Can affect weld metal composition

### Group M (Mixed) Characteristics
- **Arc Stability**: Balanced arc characteristics
- **Bead Appearance**: Good bead appearance with proper mixture
- **Spatter**: Controlled spatter levels
- **Penetration**: Optimized penetration for application
- **Chemistry**: Controlled effect on weld metal

## Usage Examples

### Creating Gas Classifications
```csharp
var gasClassifications = new List<GasClassification>
{
    // Inert gases
    new GasClassification
    {
        Group = "I",
        Subgroup = "I1",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "standards_engineer"
    },
    
    // Mixed gases for steel
    new GasClassification
    {
        Group = "M",
        Subgroup = "M1",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "standards_engineer"
    },
    
    // Active gases
    new GasClassification
    {
        Group = "A",
        Subgroup = "A1",
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "standards_engineer"
    }
};
```

## International Standards

### AWS Classification
The American Welding Society provides gas classification standards:
- **AWS A5.32**: Specification for Welding Shielding Gases
- **Group Classifications**: Standardized group and subgroup definitions
- **Performance Requirements**: Minimum performance criteria

### ISO Classification
International Organization for Standardization gas standards:
- **ISO 14175**: Welding consumables - Gases and gas mixtures
- **Group System**: Similar to AWS but with some variations
- **Regional Adaptations**: Adaptations for different regions

### EN Classification
European standards for welding gases:
- **EN ISO 14175**: European adoption of ISO standard
- **CE Marking**: Compliance requirements for European market
- **National Standards**: Individual country requirements

## Selection Criteria

### Application-Based Selection
- **Material Type**: Base metal compatibility
- **Welding Process**: Process-specific requirements
- **Quality Requirements**: Quality and appearance standards
- **Production Requirements**: Productivity and efficiency needs

### Economic Considerations
- **Gas Cost**: Cost per unit volume
- **Consumption Rate**: Gas consumption efficiency
- **Availability**: Local availability and supply
- **Storage**: Storage and handling requirements

## Related Entities

- [Gas](Gas.md) - Gases belonging to this classification
- [GasChemicalComposition](GasChemicalComposition.md) - Chemical composition of classified gases
- [WorkCenter](WorkCenter.md) - Work centers using classified gases
- [Equipment](Equipment.md) - Equipment compatible with gas classifications

## Database Considerations

- The `Group` property has a maximum length of 5 characters
- The `Subgroup` property has a maximum length of 5 characters
- Consider unique constraints on Group + Subgroup combinations
- Index on Group and Subgroup for classification queries
- Foreign key relationships should be properly configured

## Future Enhancements

### Extended Classification
- **Environmental Impact**: Environmental classification criteria
- **Safety Classification**: Safety-based grouping
- **Cost Classification**: Economic classification system
- **Performance Metrics**: Performance-based classification

### Integration Features
- **Automatic Classification**: Automatic classification based on composition
- **Recommendation Engine**: Gas recommendation based on application
- **Compatibility Matrix**: Automated compatibility checking
- **Standards Compliance**: Automated standards compliance verification
