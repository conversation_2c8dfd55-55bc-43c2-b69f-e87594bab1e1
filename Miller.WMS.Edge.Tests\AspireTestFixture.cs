using Aspire.Hosting;
using Microsoft.Extensions.Logging;

namespace Miller.WMS.Edge.Tests;

/// <summary>
/// Shared test fixture for Aspire app host that manages the lifecycle of the distributed application
/// for Aspire tests. This fixture is shared across all tests in the AspireTestCollection.
/// </summary>
public class AspireTestFixture : IAsyncLifetime
{
    private static readonly TimeSpan DefaultTimeout = TimeSpan.FromSeconds(60);

    public DistributedApplication? App { get; private set; }

    /// <summary>
    /// Initializes the Aspire app host for integration testing.
    /// This is called once per test collection.
    /// </summary>
    public async ValueTask InitializeAsync()
    {
        var cancellationToken = CancellationToken.None;

        var appHost = await DistributedApplicationTestingBuilder.CreateAsync<Projects.Miller_WMS_Edge_AppHost>(cancellationToken);

        // Configure logging for all tests
        appHost.Services.AddLogging(logging =>
        {
            logging.SetMinimumLevel(LogLevel.Debug);
            // Override the logging filters from the app's configuration
            logging.AddFilter(appHost.Environment.ApplicationName, LogLevel.Debug);
            logging.AddFilter("Aspire.", LogLevel.Debug);
            // To output logs to the xUnit.net ITestOutputHelper, consider adding a package from https://www.nuget.org/packages?q=xunit+logging
        });

        // Configure HTTP client defaults for all tests
        appHost.Services.ConfigureHttpClientDefaults(clientBuilder =>
        {
            clientBuilder.AddStandardResilienceHandler();
        });

        // Build and start the application
        App = await appHost.BuildAsync(cancellationToken).WaitAsync(DefaultTimeout, cancellationToken);
        await App.StartAsync(cancellationToken).WaitAsync(DefaultTimeout, cancellationToken);
    }

    /// <summary>
    /// Creates an HTTP client for the specified service.
    /// </summary>
    /// <param name="serviceName">The name of the service to create a client for</param>
    /// <returns>An HttpClient configured for the service</returns>
    public HttpClient CreateHttpClient(string serviceName)
    {
        if (App == null)
            throw new InvalidOperationException("App host has not been initialized. Ensure InitializeAsync has been called.");
            
        return App.CreateHttpClient(serviceName);
    }

    /// <summary>
    /// Waits for a resource to become healthy.
    /// </summary>
    /// <param name="resourceName">The name of the resource to wait for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>A task that completes when the resource is healthy</returns>
    public async Task WaitForResourceHealthyAsync(string resourceName, CancellationToken cancellationToken = default)
    {
        if (App == null)
            throw new InvalidOperationException("App host has not been initialized. Ensure InitializeAsync has been called.");

        await App.ResourceNotifications.WaitForResourceHealthyAsync(resourceName, cancellationToken)
            .WaitAsync(DefaultTimeout, cancellationToken);
    }



    /// <summary>
    /// Disposes the Aspire app host.
    /// This is called once per test collection.
    /// </summary>
    public async ValueTask DisposeAsync()
    {
        if (App != null)
        {
            await App.DisposeAsync();
        }
    }
}
