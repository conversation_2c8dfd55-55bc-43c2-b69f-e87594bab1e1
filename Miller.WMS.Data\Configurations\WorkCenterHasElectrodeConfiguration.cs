﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class WorkCenterHasElectrodeConfiguration : IEntityTypeConfiguration<WorkCenterHasElectrode>
{
    public void Configure(EntityTypeBuilder<WorkCenterHasElectrode> builder)
    {
        builder.HasKey(e => new { e.WorkCenterId, e.ElectrodeId });

        builder.HasIndex(e => e.ElectrodeId);

        builder.HasOne(e => e.WorkCenter)
               .WithMany()
               .HasForeignKey(e => e.WorkCenterId);

        builder.HasOne(e => e.Electrode)
               .WithMany()
               .HasForeignKey(e => e.ElectrodeId);
    }
}
