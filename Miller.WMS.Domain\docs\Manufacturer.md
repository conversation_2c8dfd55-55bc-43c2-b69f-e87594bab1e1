# Manufacturer

**Source File:** [Manufacturer.cs](../Manufacturer.cs)

## Overview
The `Manufacturer` entity represents companies that manufacture welding equipment, electrodes, gases, and other welding-related products. This entity is essential for tracking product sources, warranties, and supplier relationships.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the manufacturer |
| `Name` | `string` | Yes | Manufacturer company name |
| `IsActive` | `bool` | Yes | Whether the manufacturer is currently active |
| `Type` | `ManufacturerType` | Yes | Primary type of products manufactured |
| `CreatedAt` | `DateTime?` | No | Timestamp when the manufacturer was created |
| `CreatedBy` | `Guid?` | No | User who created the manufacturer record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the manufacturer record |

## Relationships

### One-to-Many Relationships
- **Manufacturer → Equipment**: A manufacturer can produce multiple pieces of equipment
- **Manufacturer → ManufacturerFacility**: A manufacturer can have multiple facilities

### Many-to-Many Relationships (via junction entities)
- **Manufacturer ↔ Electrode**: Manufacturers can produce multiple electrodes

## Manufacturer Types

The `ManufacturerType` enumeration categorizes manufacturers:

- **PowerSupply**: Manufacturers of welding power sources and equipment
- **Electrode**: Manufacturers of welding electrodes and filler metals
- **Gas**: Manufacturers and suppliers of welding gases

## Major Welding Manufacturers

### Power Supply Manufacturers
- **Miller Electric**: Welding equipment and power supplies
- **Lincoln Electric**: Welding equipment and consumables
- **ESAB**: Welding and cutting equipment
- **Fronius**: Advanced welding technology
- **Kemppi**: Professional welding equipment

### Electrode Manufacturers
- **Lincoln Electric**: Comprehensive electrode line
- **ESAB**: Welding consumables and electrodes
- **Hobart**: Welding consumables
- **Bohler Welding**: Specialty welding consumables
- **Voestalpine Bohler Welding**: High-performance consumables

### Gas Suppliers
- **Air Liquide**: Industrial gases and welding gases
- **Linde**: Welding gases and equipment
- **Praxair**: Industrial and welding gases
- **Airgas**: Gas distribution and welding supplies
- **Matheson**: Specialty and welding gases

## Business Rules

1. **Name Uniqueness**: Manufacturer names should be unique in the system
2. **Type Consistency**: Manufacturer type should align with their products
3. **Active Status**: Only active manufacturers should be available for new product assignments
4. **Product Association**: Manufacturers must be associated with their products
5. **Facility Tracking**: Manufacturer facilities should be tracked for supply chain management

## Usage Examples

### Creating a Power Supply Manufacturer
```csharp
var miller = new Manufacturer
{
    Id = Guid.NewGuid(),
    Name = "Miller Electric Manufacturing Co.",
    IsActive = true,
    Type = ManufacturerType.PowerSupply,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "admin"
};
```

### Creating an Electrode Manufacturer
```csharp
var lincoln = new Manufacturer
{
    Name = "Lincoln Electric Company",
    IsActive = true,
    Type = ManufacturerType.Electrode,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "procurement"
};
```

### Creating a Gas Supplier
```csharp
var airgas = new Manufacturer
{
    Name = "Airgas Inc.",
    IsActive = true,
    Type = ManufacturerType.Gas,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "supply_chain"
};
```

## Manufacturer Information

Consider extending the manufacturer entity with additional information:

### Contact Information
- **Address**: Primary business address
- **Phone**: Primary contact phone number
- **Email**: Primary contact email
- **Website**: Company website URL

### Business Information
- **Founded**: Year the company was founded
- **Headquarters**: Location of headquarters
- **Revenue**: Annual revenue (if public)
- **Employees**: Number of employees

### Certification and Quality
- **ISO Certifications**: Quality management certifications
- **AWS Approvals**: American Welding Society approvals
- **ASME Certifications**: Pressure vessel and boiler certifications
- **Quality Rating**: Internal quality assessment rating

## Supplier Management

Manufacturers serve as suppliers in the supply chain:

### Performance Metrics
- **On-Time Delivery**: Percentage of on-time deliveries
- **Quality Rating**: Quality performance metrics
- **Cost Competitiveness**: Pricing evaluation
- **Technical Support**: Level of technical support provided

### Relationship Management
- **Preferred Supplier**: Designation as preferred supplier
- **Contract Terms**: Standard contract terms and conditions
- **Payment Terms**: Standard payment terms
- **Volume Discounts**: Available volume discount structures

## Product Lines

Manufacturers typically have multiple product lines:

### Equipment Product Lines
- **Power Supplies**: Different series and models
- **Torches and Guns**: Various torch types and accessories
- **Automation**: Robotic and automated welding systems
- **Safety Equipment**: Fume extraction and safety systems

### Consumable Product Lines
- **Stick Electrodes**: Various classifications and sizes
- **Wire Electrodes**: MIG and TIG wires
- **Flux**: Submerged arc and flux-cored fluxes
- **Accessories**: Contact tips, nozzles, and consumables

## Related Entities

- [Equipment](Equipment.md) - Equipment manufactured by this manufacturer
- [Electrode](Electrode.md) - Electrodes manufactured by this manufacturer
- [ElectrodeMadeByManufacturer](ElectrodeMadeByManufacturer.md) - Electrode manufacturing relationships
- [ManufacturerFacility](ManufacturerFacility.md) - Manufacturer facility locations

## Database Considerations

- Index on `Name` for manufacturer searches
- Index on `Type` for manufacturer categorization
- Index on `IsActive` for active manufacturer queries
- Consider full-text search on `Name` for flexible searching
- Foreign key relationships should be properly configured
- Consider adding unique constraints on manufacturer names

## Integration Points

### ERP Systems
- **Purchase Orders**: Integration with procurement systems
- **Inventory Management**: Stock level and reorder point management
- **Accounts Payable**: Invoice processing and payment

### Quality Systems
- **Supplier Audits**: Quality audit results and schedules
- **Corrective Actions**: Quality issue tracking and resolution
- **Certifications**: Certificate management and renewal tracking

### Supply Chain Systems
- **Delivery Tracking**: Shipment and delivery status
- **Demand Planning**: Forecast and capacity planning
- **Risk Management**: Supply chain risk assessment and mitigation
