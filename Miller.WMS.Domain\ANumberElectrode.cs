﻿namespace Miller.WMS.Domain;

public class ANumberElectrode : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public required string Name { get; set; }

    public required Guid SpecificationId { get; set; }
    public Specification? Specification { get; set; }
    public required Guid MetalChemicalCompositionId { get; set; }
    public MetalChemicalComposition? MetalChemicalComposition { get; set; }
}
