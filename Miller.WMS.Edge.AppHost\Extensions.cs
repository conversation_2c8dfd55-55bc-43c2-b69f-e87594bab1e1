﻿using Aspire.Hosting;
using Aspire.Hosting.ApplicationModel;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using Microsoft.Extensions.Hosting;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Reflection.Metadata.Ecma335;
using System.Text;
using System.Threading.Tasks;

namespace Miller.WMS.AppHost
{
    public static class Extensions
    {
        public static bool IsTest(this IHostEnvironment hostEnvironment)
        {
            return hostEnvironment.IsEnvironment("Test");
        }

        public static IResourceBuilder<T> WithSwaggerUI<T>(this IResourceBuilder<T> builder)
            where T : IResourceWithEndpoints
        {
            return builder.WithOpenApiDocs("swagger-ui", "Swagger API Doc", "swagger");
        }

        public static IResourceBuilder<T> WithOpenApiDocs<T>(this IResourceBuilder<T> builder,
            string name, string displayName, string openApiUiPath)
            where T : IResourceWithEndpoints
        {
            return builder.WithCommand(
                name,
                displayName,
                executeCommand: async _ => {
                    try
                    {
                        var endpoint = builder.GetEndpoint("https");

                        var url = $"{endpoint.Url}/{openApiUiPath}";

                        await Task.Run(() => Process.Start(new ProcessStartInfo(url) { UseShellExecute = true }));

                        return new ExecuteCommandResult
                        {
                            Success = true
                        };
                    }
                    catch (Exception ouch)
                    {
                        return new ExecuteCommandResult { Success = false, ErrorMessage = ouch.Message };
                    }
                },
                commandOptions: new CommandOptions
                {
                    IconName = "Document",
                    IconVariant = IconVariant.Filled,
                    UpdateState = context => context.ResourceSnapshot.HealthStatus == HealthStatus.Healthy ? ResourceCommandState.Enabled : ResourceCommandState.Disabled
                }
            );
        }

        /// <summary>
        /// Adds a reference to another Aspire resource and tells the runtime
        /// to wait for that resource to become healthy before starting this one.
        /// </summary>
        /// <typeparam name="TResource">The resource being configured.</typeparam>
        /// <typeparam name="TDependency">The dependency’s resource type.</typeparam>
        /// <param name="builder">The builder for the current resource.</param>
        /// <param name="dependency">The builder for the resource we depend on.</param>
        /// <returns>The same <paramref name="builder"/> for fluent chaining.</returns>

        public static IResourceBuilder<TDestination> WaitForReference<TDestination>(
            this IResourceBuilder<TDestination> builder,
            IResourceBuilder<IResourceWithConnectionString> source
            )
             where TDestination : IResourceWithEnvironment, IResourceWithWaitSupport
        {
            return builder.WithReference(source)
                .WaitFor(source);
        }

        /// <summary>
        /// Adds a reference to another Aspire resource and tells the runtime
        /// to wait for that resource to become healthy before starting this one.
        /// </summary>
        /// <typeparam name="TResource">The resource being configured.</typeparam>
        /// <typeparam name="TDependency">The dependency’s resource type.</typeparam>
        /// <param name="builder">The builder for the current resource.</param>
        /// <param name="dependency">The builder for the resource we depend on.</param>
        /// <returns>The same <paramref name="builder"/> for fluent chaining.</returns>

        public static IResourceBuilder<TDestination> WaitForReference<TDestination>(
            this IResourceBuilder<TDestination> builder,
            IResourceBuilder<ProjectResource> source
            )
             where TDestination : IResourceWithEnvironment, IResourceWithWaitSupport
        {
            return builder.WithReference(source)
                .WaitFor(source);
        }

        /// <summary>
        /// Adds a reference to another Aspire resource and tells the runtime
        /// to wait for that resource to become healthy before starting this one.
        /// </summary>
        /// <typeparam name="TResource">The resource being configured.</typeparam>
        /// <typeparam name="TDependency">The dependency’s resource type.</typeparam>
        /// <param name="builder">The builder for the current resource.</param>
        /// <param name="dependency">The builder for the resource we depend on.</param>
        /// <returns>The same <paramref name="builder"/> for fluent chaining.</returns>

        public static IResourceBuilder<TDestination> WaitForReference<TDestination>(
            this IResourceBuilder<TDestination> builder,
            IResourceBuilder<KeycloakResource> source
            )
             where TDestination : IResourceWithEnvironment, IResourceWithWaitSupport
        {
            return builder.WithReference(source)
                .WaitFor(source);
        }
    }
}

