﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class GasChemicalCompositionConfiguration : IEntityTypeConfiguration<GasChemicalComposition>
{
    public void Configure(EntityTypeBuilder<GasChemicalComposition> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.CarbonDioxide).HasPrecision(5, 4);
        builder.Property(e => e.Oxygen).HasPrecision(5, 4);
        builder.Property(e => e.Argon).HasPrecision(5, 4);
        builder.Property(e => e.Helium).HasPrecision(5, 4);
    }
}
