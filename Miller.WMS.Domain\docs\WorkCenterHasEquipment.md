# WorkCenterHasEquipment

**Source File:** [WorkCenterHasEquipment.cs](../WorkCenterHasEquipment.cs)

## Overview
The `WorkCenterHasEquipment` entity represents the many-to-many relationship between work centers and equipment, tracking which equipment is assigned to or available at specific work centers.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `WorkCenterId` | `Guid` | Yes | Foreign key to the work center |
| `EquipmentId` | `Guid` | Yes | Foreign key to the equipment |
| `CreatedAt` | `DateTime?` | No | Timestamp when the equipment was assigned |
| `CreatedBy` | `string?` | No | User who created this assignment |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this assignment |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `WorkCenter` | `WorkCenter` | The work center where equipment is assigned |
| `Equipment` | `Equipment` | The equipment assigned to the work center |

## Relationships

### Many-to-One Relationships
- **WorkCenterHasEquipment → WorkCenter**: Each assignment belongs to one work center
- **WorkCenterHasEquipment → Equipment**: Each assignment is for one piece of equipment

## Composite Key

This entity uses a composite primary key consisting of:
- `WorkCenterId`
- `EquipmentId`

This ensures that the same equipment can only be assigned once to a specific work center, but equipment can be assigned to multiple work centers if needed.

## Equipment Assignment Types

Equipment assignments can represent different relationships:

### Permanent Assignment
- Equipment permanently located at the work center
- Primary equipment for the work center's operations
- Equipment that requires specific installation or setup

### Shared Assignment
- Equipment shared between multiple work centers
- Mobile equipment that can be moved as needed
- Common tools and accessories

### Backup Assignment
- Backup equipment available when primary equipment is down
- Redundant equipment for critical operations
- Emergency replacement equipment

## Business Rules

1. **Equipment Status**: Only active equipment should be assigned to work centers
2. **Work Center Status**: Only active work centers should receive equipment assignments
3. **Capacity Limits**: Work centers may have limits on equipment capacity
4. **Equipment Compatibility**: Equipment should be compatible with work center capabilities
5. **Safety Requirements**: Equipment assignments must meet safety requirements

## Usage Examples

### Assigning Equipment to a Work Center
```csharp
var assignment = new WorkCenterHasEquipment
{
    WorkCenterId = tigStationId,
    EquipmentId = dynasty350Id,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "supervisor"
};
```

### Bulk Equipment Assignment
```csharp
var assignments = new List<WorkCenterHasEquipment>
{
    new WorkCenterHasEquipment
    {
        WorkCenterId = weldingCellId,
        EquipmentId = powerSupplyId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "setup_admin"
    },
    new WorkCenterHasEquipment
    {
        WorkCenterId = weldingCellId,
        EquipmentId = torchId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "setup_admin"
    },
    new WorkCenterHasEquipment
    {
        WorkCenterId = weldingCellId,
        EquipmentId = feederId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "setup_admin"
    }
};
```

## Equipment Configuration

Consider extending this entity to include configuration information:

### Assignment Properties
- **Assignment Type**: Permanent, Shared, Backup
- **Primary Equipment**: Flag indicating if this is the primary equipment
- **Installation Date**: When equipment was installed
- **Configuration Notes**: Special setup or configuration information

### Operational Properties
- **Utilization Rate**: How often the equipment is used
- **Maintenance Schedule**: Maintenance requirements specific to this assignment
- **Calibration Status**: Calibration requirements and status
- **Operating Parameters**: Specific parameters for this work center

## Work Center Capabilities

Equipment assignments define work center capabilities:

### Welding Capabilities
- **Power Supply**: Determines available welding processes and current ranges
- **Torch Type**: Defines compatible welding processes (TIG, MIG, etc.)
- **Feeder**: Enables wire feeding for GMAW/FCAW processes

### Support Capabilities
- **Fume Extraction**: Enables safe welding operations
- **Positioning Equipment**: Enables complex part positioning
- **Fixturing**: Enables specific part holding and alignment

### Quality Capabilities
- **Inspection Equipment**: Enables quality control operations
- **Measurement Tools**: Enables dimensional verification
- **Testing Equipment**: Enables material and weld testing

## Equipment Tracking

This relationship enables comprehensive equipment tracking:

### Location Tracking
- Current work center assignment
- Equipment movement history
- Utilization across work centers

### Availability Management
- Equipment availability at specific work centers
- Scheduling and resource planning
- Conflict resolution for shared equipment

### Maintenance Coordination
- Maintenance scheduling based on work center needs
- Impact assessment when equipment is down
- Replacement equipment coordination

## Related Entities

- [WorkCenter](WorkCenter.md) - The work center receiving equipment
- [Equipment](Equipment.md) - The equipment being assigned
- [Facility](Facility.md) - The facility context (via WorkCenter)
- [EquipmentHasElectrodeClassification](EquipmentHasElectrodeClassification.md) - Equipment capabilities

## Database Considerations

- Composite primary key on `WorkCenterId` and `EquipmentId`
- Index on `WorkCenterId` for work center equipment queries
- Index on `EquipmentId` for equipment location queries
- Foreign key constraints should be properly configured
- Consider adding check constraints for valid status combinations
- Implement audit triggers for assignment change tracking

## Operational Benefits

1. **Resource Planning**: Understand equipment distribution across work centers
2. **Capacity Management**: Plan work center capabilities based on equipment
3. **Maintenance Scheduling**: Coordinate maintenance with work center operations
4. **Utilization Analysis**: Analyze equipment usage patterns
5. **Cost Allocation**: Allocate equipment costs to work centers and jobs
