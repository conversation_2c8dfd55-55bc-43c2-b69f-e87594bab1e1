# MaterialSubstitution

**Source File:** [MaterialSubstitution.cs](../MaterialSubstitution.cs)

## Overview
The `MaterialSubstitution` entity represents approved substitutions between materials. This entity enables controlled material substitution based on engineering approval, ensuring that substitute materials meet the same performance requirements as the original specification.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the substitution |
| `SpecificationId` | `Guid` | Yes | Foreign key to the governing specification |
| `MaterialId` | `Guid` | Yes | Foreign key to the original material |
| `SubstitutedById` | `Guid` | Yes | Foreign key to the substitute material |
| `ApprovalType` | `MaterialSubstitutionApprovalType` | Yes | Type of approval for the substitution |
| `CreatedAt` | `DateTime?` | No | Timestamp when the substitution was created |
| `CreatedBy` | `Guid?` | No | User who created the substitution record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the substitution record |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Specification` | `Specification` | The specification governing this substitution |
| `Material` | `Material` | The original material being substituted |
| `SubstitutedBy` | `Material` | The substitute material |

## Relationships

### Many-to-One Relationships
- **MaterialSubstitution → Specification**: Each substitution belongs to one specification
- **MaterialSubstitution → Material (Original)**: Each substitution has one original material
- **MaterialSubstitution → Material (Substitute)**: Each substitution has one substitute material

## Substitution Types

### Direct Substitution
Materials with equivalent properties and performance:
- **Same Grade**: Different suppliers of the same material grade
- **Equivalent Specification**: Materials meeting equivalent specifications
- **Brand Substitution**: Different brand names for equivalent materials

### Upgrade Substitution
Higher grade materials that exceed original requirements:
- **Higher Strength**: Materials with superior mechanical properties
- **Enhanced Corrosion Resistance**: Materials with better corrosion resistance
- **Improved Toughness**: Materials with enhanced impact toughness

### Alternative Specification
Materials from different specifications with equivalent performance:
- **Cross-Standard**: Materials from different standards (ASTM vs. EN)
- **Regional Variants**: Regional variations of international standards
- **Updated Standards**: Materials from newer versions of standards

### Emergency Substitution
Temporary substitutions for availability or emergency situations:
- **Supply Chain**: Substitutions due to supply chain disruptions
- **Obsolete Materials**: Substitutions for discontinued materials
- **Regional Availability**: Substitutions based on regional availability

## Approval Types

The `MaterialSubstitutionApprovalType` enumeration defines different levels of approval:

### Engineering Approval
- **Standard Engineering**: Routine engineering approval
- **Senior Engineering**: Senior engineer approval required
- **Chief Engineer**: Chief engineer approval required

### Customer Approval
- **Customer Concurrence**: Customer approval required
- **Customer Specification**: Customer-specified substitution
- **Customer Preference**: Customer-preferred substitution

### Code Approval
- **Code Authority**: Code authority approval required
- **Inspector Approval**: Inspector approval required
- **Third Party**: Third-party approval required

## Business Rules

1. **Specification Governance**: All substitutions must be governed by a specification
2. **Material Validation**: Both original and substitute materials must be valid
3. **Approval Requirements**: Appropriate approval must be obtained before use
4. **Performance Equivalence**: Substitute materials must meet or exceed original requirements
5. **Documentation**: Complete documentation must be maintained for all substitutions

## Usage Examples

### Creating a Direct Material Substitution
```csharp
var directSubstitution = new MaterialSubstitution
{
    Id = Guid.NewGuid(),
    SpecificationId = astmA36SpecId,
    MaterialId = supplierAA36Id,
    SubstitutedById = supplierBA36Id,
    ApprovalType = MaterialSubstitutionApprovalType.StandardEngineering,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = materials_engineer_id
};
```

### Creating an Upgrade Substitution
```csharp
var upgradeSubstitution = new MaterialSubstitution
{
    SpecificationId = astmA572SpecId,
    MaterialId = grade50MaterialId,
    SubstitutedById = grade65MaterialId,
    ApprovalType = MaterialSubstitutionApprovalType.CustomerConcurrence,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = project_engineer_id
};
```

### Creating an Emergency Substitution
```csharp
var emergencySubstitution = new MaterialSubstitution
{
    SpecificationId = asmeSection2SpecId,
    MaterialId = primaryMaterialId,
    SubstitutedById = emergencyMaterialId,
    ApprovalType = MaterialSubstitutionApprovalType.ChiefEngineer,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = emergency_coordinator_id
};
```

## Substitution Validation

### Technical Validation
- **Chemical Composition**: Verify chemical composition compatibility
- **Mechanical Properties**: Ensure mechanical properties meet requirements
- **Physical Properties**: Validate physical property compatibility
- **Weldability**: Confirm weldability characteristics

### Performance Validation
- **Strength Requirements**: Verify strength requirements are met
- **Toughness Requirements**: Ensure impact toughness is adequate
- **Corrosion Resistance**: Validate corrosion resistance properties
- **Service Temperature**: Confirm service temperature compatibility

### Code Compliance
- **Standard Compliance**: Ensure compliance with applicable standards
- **Code Requirements**: Meet specific code requirements
- **Certification**: Maintain proper material certifications
- **Traceability**: Ensure complete material traceability

## Quality Assurance

### Documentation Requirements
- **Substitution Justification**: Technical justification for substitution
- **Approval Documentation**: Formal approval documentation
- **Test Results**: Supporting test data and results
- **Certification**: Material test certificates

### Change Control
- **Change Requests**: Formal change request process
- **Impact Assessment**: Assessment of substitution impact
- **Approval Workflow**: Structured approval workflow
- **Implementation Control**: Controlled implementation process

## Supply Chain Management

### Procurement Impact
- **Supplier Qualification**: Ensure substitute material suppliers are qualified
- **Cost Impact**: Assess cost impact of substitutions
- **Availability**: Verify availability of substitute materials
- **Lead Times**: Consider lead time differences

### Inventory Management
- **Stock Management**: Manage inventory of substitute materials
- **Obsolescence**: Handle obsolete material inventory
- **Rotation**: Implement first-in-first-out rotation
- **Emergency Stock**: Maintain emergency substitute material stock

## Risk Management

### Technical Risks
- **Performance Risk**: Risk of performance degradation
- **Compatibility Risk**: Risk of incompatibility issues
- **Quality Risk**: Risk of quality problems
- **Reliability Risk**: Risk of reliability issues

### Business Risks
- **Cost Risk**: Risk of increased costs
- **Schedule Risk**: Risk of schedule delays
- **Compliance Risk**: Risk of non-compliance
- **Customer Risk**: Risk of customer dissatisfaction

## Related Entities

- [Material](Material.md) - The original and substitute materials
- [Specification](Specification.md) - The governing specification
- [MaterialSubstitutionApprovalType](MaterialSubstitutionApprovalType.md) - The approval type enumeration

## Database Considerations

- Index on `SpecificationId` for specification-based queries
- Index on `MaterialId` for original material queries
- Index on `SubstitutedById` for substitute material queries
- Index on `ApprovalType` for approval type queries
- Foreign key constraints should be properly configured
- Consider unique constraints to prevent duplicate substitutions
- Implement business rules to prevent circular substitutions

## Integration with Engineering Systems

### CAD Integration
- **Drawing Updates**: Update drawings with substitute materials
- **Bill of Materials**: Update BOMs with approved substitutions
- **Change Notifications**: Notify design teams of substitutions
- **Version Control**: Maintain version control of design changes

### ERP Integration
- **Procurement**: Integrate with procurement systems
- **Inventory**: Update inventory systems with substitutions
- **Cost Accounting**: Track costs of substitute materials
- **Planning**: Integrate with production planning systems

## Compliance and Auditing

### Audit Trail
- **Change History**: Complete history of substitution changes
- **Approval Records**: Records of all approvals
- **Usage Tracking**: Track usage of substitute materials
- **Performance Monitoring**: Monitor performance of substitutions

### Regulatory Compliance
- **Code Compliance**: Ensure compliance with applicable codes
- **Standard Compliance**: Meet industry standard requirements
- **Customer Requirements**: Comply with customer specifications
- **Legal Requirements**: Meet legal and regulatory requirements

## Future Enhancements

### Advanced Substitution Management
- **AI-Driven Substitutions**: AI recommendations for material substitutions
- **Performance Prediction**: Predict performance of substitute materials
- **Cost Optimization**: Optimize substitutions for cost savings
- **Sustainability**: Consider environmental impact in substitutions

### Digital Integration
- **Digital Approvals**: Digital approval workflows
- **Real-Time Validation**: Real-time validation of substitutions
- **Mobile Access**: Mobile access to substitution information
- **Blockchain Verification**: Blockchain-based substitution verification
