﻿namespace Miller.WMS.Domain;

public class MaterialSubstitution : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public Guid SpecificationId { get; set; }
    public Specification Specification { get; set; } = null!;

    public Guid MaterialId { get; set; }
    public Material Material { get; set; } = null!;

    public Guid SubstitutedById { get; set; }
    public Material SubstitutedBy { get; set; } = null!;

    public MaterialSubstitutionApprovalType ApprovalType { get; set; }
}
