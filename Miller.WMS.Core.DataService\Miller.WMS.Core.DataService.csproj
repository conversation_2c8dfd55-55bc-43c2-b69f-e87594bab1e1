<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dotnet-Miller.WMS.Core.DataService-75c1b25e-a5ec-47ee-bc5d-aaf1ec9683ba</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Bogus" Version="35.6.3" />
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.0" />
    <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Miller.WMS.Data\Miller.WMS.Data.csproj" />
    <ProjectReference Include="..\Miller.WMS.ServiceDefaults\Miller.WMS.ServiceDefaults.csproj" />
  </ItemGroup>
</Project>
