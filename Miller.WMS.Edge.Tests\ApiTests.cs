namespace Miller.WMS.Edge.Tests;

[Collection("AspireTestCollection")]
public class ApiTests
{
    private readonly AspireTestFixture _fixture;

    public ApiTests(AspireTestFixture fixture)
    {
        _fixture = fixture;
    }

    [Fact]
    public async Task GetApiResourceRootReturnsOkStatusCode()
    {
        // Arrange
        var cancellationToken = TestContext.Current.CancellationToken;

        // Act
        var httpClient = _fixture.CreateHttpClient("wms-edge-api");
        await _fixture.WaitForResourceHealthyAsync("wms-edge-api", cancellationToken);
        var response = await httpClient.GetAsync("/health", cancellationToken);

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
    }

    [Fact]
    public async Task GetPingEndpointReturnsTrue()
    {
        // Arrange
        var cancellationToken = TestContext.Current.CancellationToken;

        // Act
        var httpClient = _fixture.CreateHttpClient("wms-edge-api");
        await _fixture.WaitForResourceHealthyAsync("wms-edge-api", cancellationToken);
        var response = await httpClient.GetAsync("/ping", cancellationToken);
        var content = await response.Content.ReadAsStringAsync(cancellationToken);

        // Assert
        Assert.Equal(HttpStatusCode.OK, response.StatusCode);
        Assert.Equal("true", content);
    }
}
