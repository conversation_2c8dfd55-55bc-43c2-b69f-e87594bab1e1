﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class TungstenElectrodeClassificationConfiguration : IEntityTypeConfiguration<TungstenElectrodeClassification>
{
    public void Configure(EntityTypeBuilder<TungstenElectrodeClassification> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Classification)
               .HasMaxLength(255)
               .IsRequired();

        builder.Property(e => e.PrincipalOxide)
               .HasMaxLength(255);

        builder.Property(e => e.MinMass).HasPrecision(5, 4);
        builder.Property(e => e.MaxMass).HasPrecision(5, 4);
        builder.Property(e => e.MaxImpurities).HasPrecision(5, 4);

        builder.Property(e => e.Color)
               .HasMaxLength(20)
               .IsRequired();
    }
}
