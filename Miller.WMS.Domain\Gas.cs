﻿using System;

namespace Miller.WMS.Domain;

public class Gas : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }


    public string Name { get; set; } = null!;

    public Guid GasClassificationId { get; set; }
    public GasClassification GasClassification { get; set; } = null!;

    public Guid GasChemicalCompositionId { get; set; }
    public GasChemicalComposition GasChemicalComposition { get; set; } = null!;
}
