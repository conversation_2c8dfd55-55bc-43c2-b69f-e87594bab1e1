﻿using System;

namespace Miller.WMS.Domain;

public class User : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public required string Name { get; set; }
    public required string Email { get; set; }
    public Guid OrganizationId { get; set; }
    public Organization? Organization { get; set; }
    public ICollection<UserFacilityRole> UserFacilityRoles { get; set; } = [];
}