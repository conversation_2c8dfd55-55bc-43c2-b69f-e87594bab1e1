# Waveform

**Source File:** [Waveform.cs](../Waveform.cs)

## Overview
The `Waveform` entity represents welding waveform configurations used in advanced welding power supplies. Waveforms define the electrical characteristics of the welding arc, including current patterns, frequency, and timing parameters that affect weld quality and characteristics.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `Id` | `Guid` | Yes | Unique identifier for the waveform |
| `Name` | `string` | Yes | Waveform name or designation (max 255 characters) |
| `Version` | `string` | Yes | Waveform version number (max 255 characters) |
| `Description` | `string` | Yes | Detailed description of the waveform (max 255 characters) |
| `CreatedAt` | `DateTime?` | No | Timestamp when the waveform was created |
| `CreatedBy` | `Guid?` | No | User who created the waveform record |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified the waveform record |

## Relationships

### Many-to-Many Relationships (via junction entities)
- **Waveform ↔ WorkCenter**: Waveforms can be available at multiple work centers

## Waveform Types

Waveforms are used in various welding processes and applications:

### GTAW (TIG) Waveforms
- **AC Balance Control**: Controls electrode positive vs. negative time
- **AC Frequency**: Controls arc characteristics and penetration
- **Pulse Waveforms**: Controls heat input and bead appearance
- **Advanced AC**: Complex waveforms for specific applications

### GMAW (MIG) Waveforms
- **Pulse Spray**: Controlled metal transfer for aluminum
- **Modified Short Circuit**: Reduced spatter and improved transfer
- **Surface Tension Transfer**: Low heat input for thin materials
- **Cold Metal Transfer (CMT)**: Specialized low-heat process

### Specialized Waveforms
- **Root Pass**: Optimized for root pass welding
- **Fill Pass**: Optimized for fill and cap passes
- **Thin Material**: Low heat input for thin sections
- **High Deposition**: High productivity waveforms

## Waveform Parameters

While not explicitly modeled in the current entity, waveforms typically include:

### Timing Parameters
- **Peak Current**: Maximum current level
- **Background Current**: Minimum current level
- **Peak Time**: Duration of peak current
- **Background Time**: Duration of background current

### AC Parameters (for GTAW)
- **EN Time**: Electrode negative time percentage
- **EP Time**: Electrode positive time percentage
- **Frequency**: AC frequency in Hz
- **Balance**: EN/EP time ratio

### Pulse Parameters
- **Pulse Frequency**: Pulses per second
- **Pulse Width**: Duration of each pulse
- **Pulse Amplitude**: Current amplitude variation
- **Duty Cycle**: Percentage of time at peak current

## Business Rules

1. **Name Uniqueness**: Waveform names should be unique within versions
2. **Version Control**: Waveforms should have proper version management
3. **Description Requirements**: Descriptions should clearly explain waveform purpose
4. **Work Center Compatibility**: Waveforms should only be assigned to compatible work centers
5. **Equipment Compatibility**: Waveforms must be compatible with power supply capabilities

## Usage Examples

### Creating a GTAW Waveform
```csharp
var tigWaveform = new Waveform
{
    Id = Guid.NewGuid(),
    Name = "AC-TIG-Aluminum",
    Version = "1.2",
    Description = "Optimized AC waveform for aluminum TIG welding with enhanced cleaning action",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "process_engineer"
};
```

### Creating a GMAW Waveform
```csharp
var migWaveform = new Waveform
{
    Name = "Pulse-Spray-Steel",
    Version = "2.0",
    Description = "Pulse spray transfer waveform for steel applications with reduced spatter",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "welding_engineer"
};
```

### Creating a Specialized Waveform
```csharp
var rootWaveform = new Waveform
{
    Name = "Root-Pass-Pipeline",
    Version = "1.0",
    Description = "Specialized waveform for pipeline root pass welding with keyhole control",
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "pipeline_specialist"
};
```

## Waveform Applications

### Material-Specific Waveforms
- **Aluminum**: AC waveforms with cleaning action
- **Stainless Steel**: Pulse waveforms for heat control
- **Carbon Steel**: Standard and modified waveforms
- **Exotic Alloys**: Specialized waveforms for difficult materials

### Application-Specific Waveforms
- **Aerospace**: High-quality, low-distortion waveforms
- **Pipeline**: Root pass and hot pass waveforms
- **Automotive**: High-speed production waveforms
- **Shipbuilding**: Heavy section waveforms

### Position-Specific Waveforms
- **Flat Position**: High deposition rate waveforms
- **Vertical**: Controlled penetration waveforms
- **Overhead**: Low heat input waveforms
- **Pipe**: Specialized pipe welding waveforms

## Waveform Development

### Development Process
1. **Requirements Analysis**: Define application requirements
2. **Parameter Development**: Develop timing and current parameters
3. **Testing**: Laboratory and field testing
4. **Validation**: Performance validation and approval
5. **Documentation**: Create application guidelines

### Performance Metrics
- **Penetration**: Depth and consistency of penetration
- **Bead Appearance**: Surface finish and profile
- **Spatter Level**: Amount of spatter generated
- **Travel Speed**: Maximum achievable travel speed
- **Defect Rate**: Incidence of welding defects

## Work Center Assignment

Waveforms are assigned to work centers through the `WorkCenterHasWaveform` relationship:

### Assignment Criteria
- **Equipment Compatibility**: Power supply must support the waveform
- **Process Requirements**: Work center must perform compatible processes
- **Operator Training**: Operators must be trained on waveform use
- **Quality Requirements**: Waveform must meet quality standards

### Configuration Management
- **Version Control**: Track waveform versions at work centers
- **Update Procedures**: Manage waveform updates and rollouts
- **Backup Waveforms**: Maintain backup waveforms for critical operations
- **Documentation**: Maintain application and setup documentation

## Related Entities

- [WorkCenter](WorkCenter.md) - Work centers where waveforms are available
- [WorkCenterHasWaveform](WorkCenterHasWaveform.md) - Waveform assignments to work centers
- [Equipment](Equipment.md) - Power supplies that support waveforms

## Database Considerations

- All string properties have maximum length of 255 characters
- Index on `Name` for waveform searches
- Consider composite index on `Name` and `Version`
- Foreign key constraints should be properly configured
- Consider adding fields for waveform parameters and specifications
- Implement version control mechanisms

## Future Enhancements

Consider adding additional properties for comprehensive waveform management:

### Technical Parameters
- **Process Type**: GTAW, GMAW, FCAW, etc.
- **Current Range**: Minimum and maximum current
- **Voltage Range**: Operating voltage range
- **Material Compatibility**: Compatible base materials

### Application Data
- **Recommended Settings**: Default parameter recommendations
- **Travel Speed Range**: Recommended travel speeds
- **Joint Configuration**: Compatible joint types
- **Thickness Range**: Applicable material thickness range
