using Carter;
using Facet;
using Facet.Extensions;
using Microsoft.EntityFrameworkCore;
using Miller.WMS.Shared.Data;
using Miller.WMS.Domain;

namespace Miller.WMS.Edge.ApiService.Modules;

public class FacilityModule : ICarterModule
{
    public void AddRoutes(IEndpointRouteBuilder app)
    {
        var group = app.MapGroup("/api/facilities")
            .WithTags("Facilities")
            .WithOpenApi();

        group.MapGet("/", GetFacilities)
            .WithName("GetFacilities")
            .WithSummary("Get all facilities")
            .WithDescription("Retrieves a list of all facilities");

        group.MapGet("/{id:guid}", GetFacilityById)
            .WithName("GetFacilityById")
            .WithSummary("Get facility by ID")
            .WithDescription("Retrieves a specific facility by its ID");

        group.MapGet("/organization/{organizationId:guid}", GetFacilitiesByOrganization)
            .WithName("GetFacilitiesByOrganization")
            .WithSummary("Get facilities by organization")
            .WithDescription("Retrieves all facilities for a specific organization");

        group.MapPost("/", CreateFacility)
            .WithName("CreateFacility")
            .WithSummary("Create a new facility")
            .WithDescription("Creates a new facility");

        group.MapPut("/{id:guid}", UpdateFacility)
            .WithName("UpdateFacility")
            .WithSummary("Update a facility")
            .WithDescription("Updates an existing facility");

        group.MapDelete("/{id:guid}", DeleteFacility)
            .WithName("DeleteFacility")
            .WithSummary("Delete a facility")
            .WithDescription("Deletes a facility");
    }

    private static async Task<IResult> GetFacilities(WmsContext context)
    {
        var facilities = await context.Facilities
            .Include(f => f.Organization)
            .Include(f => f.UserFacilityRoles)
            .ToListAsync();

        var facilityDtos = facilities.Select(f => f.ToFacet<Facility, FacilityDto>()).ToList();
        return Results.Ok(facilityDtos);
    }

    private static async Task<IResult> GetFacilityById(Guid id, WmsContext context)
    {
        var facility = await context.Facilities
            .Include(f => f.Organization)
            .Include(f => f.UserFacilityRoles)
            .FirstOrDefaultAsync(f => f.Id == id);

        if (facility == null)
            return Results.NotFound($"Facility with ID {id} not found");

        return Results.Ok(facility.ToFacet<Facility, FacilityDto>());
    }

    private static async Task<IResult> GetFacilitiesByOrganization(Guid organizationId, WmsContext context)
    {
        var facilities = await context.Facilities
            .Include(f => f.Organization)
            .Include(f => f.UserFacilityRoles)
            .Where(f => f.OrganizationId == organizationId)
            .ToListAsync();

        var facilityDtos = facilities.SelectFacets<Facility, FacilityDto>().ToList();
        return Results.Ok(facilityDtos);
    }

    private static async Task<IResult> CreateFacility(CreateFacilityRequest request, WmsContext context)
    {
        var facility = request.ToFacet<CreateFacilityRequest, Facility>();

        // Verify organization exists
        var organizationExists = await context.Organizations.AnyAsync(o => o.Id == facility.OrganizationId);
        if (!organizationExists)
            return Results.BadRequest($"Organization with ID {facility.OrganizationId} not found");

        facility.Id = Guid.NewGuid();
        facility.CreatedAt = DateTime.UtcNow;
        facility.CreatedBy = Guid.NewGuid(); // Get from request token when auth configured

        context.Facilities.Add(facility);
        await context.SaveChangesAsync();

        // Reload with includes for response
        var createdFacility = await context.Facilities
            .Include(f => f.Organization)
            .Include(f => f.UserFacilityRoles)
            .FirstAsync(f => f.Id == facility.Id);

        return Results.Created($"/api/facilities/{facility.Id}", createdFacility.ToFacet<Facility, FacilityDto>());
    }

    private static async Task<IResult> UpdateFacility(Guid id, UpdateFacilityRequest request, WmsContext context)
    {
        var updatedFacility = request.ToFacet<UpdateFacilityRequest, Facility>();

        // Validate that URL ID matches request object ID
        if (id != updatedFacility.Id)
            return Results.BadRequest($"URL ID {id} does not match request object ID {updatedFacility.Id}");

        var existingFacility = await context.Facilities.FindAsync(id);
        if (existingFacility == null)
            return Results.NotFound($"Facility with ID {id} not found");

        // Verify organization exists if changing
        if (updatedFacility.OrganizationId != existingFacility.OrganizationId)
        {
            var organizationExists = await context.Organizations.AnyAsync(o => o.Id == updatedFacility.OrganizationId);
            if (!organizationExists)
                return Results.BadRequest($"Organization with ID {updatedFacility.OrganizationId} not found");
        }

        // Preserve creation audit fields (excluded from request by design)
        updatedFacility.CreatedAt = existingFacility.CreatedAt;
        updatedFacility.CreatedBy = existingFacility.CreatedBy;
        updatedFacility.ModifiedAt = DateTime.UtcNow;
        updatedFacility.ModifiedBy = Guid.NewGuid(); // Get from request token when auth configured

        context.Entry(existingFacility).CurrentValues.SetValues(updatedFacility);
        await context.SaveChangesAsync();

        // Reload with includes for response
        var reloadedFacility = await context.Facilities
            .Include(f => f.Organization)
            .Include(f => f.UserFacilityRoles)
            .FirstAsync(f => f.Id == id);

        return Results.Ok(reloadedFacility.ToFacet<Facility, FacilityDto>());
    }

    private static async Task<IResult> DeleteFacility(Guid id, WmsContext context)
    {
        var facility = await context.Facilities.FindAsync(id);
        if (facility == null)
            return Results.NotFound($"Facility with ID {id} not found");

        context.Facilities.Remove(facility);
        await context.SaveChangesAsync();

        return Results.NoContent();
    }
}

// DTOs using Facet
[Facet(typeof(Facility))]
public partial class FacilityDto
{
}

[Facet(typeof(Facility),
    exclude: [
        nameof(Facility.Id),
        nameof(Facility.CreatedAt),
        nameof(Facility.CreatedBy),
        nameof(Facility.ModifiedAt),
        nameof(Facility.ModifiedBy)],
    Kind = FacetKind.Record)]
public partial record CreateFacilityRequest { }

[Facet(typeof(Facility),
    exclude: [
        nameof(Facility.CreatedAt),
        nameof(Facility.CreatedBy),
        nameof(Facility.ModifiedAt)], 
    Kind = FacetKind.Record)]
public partial record UpdateFacilityRequest { }
