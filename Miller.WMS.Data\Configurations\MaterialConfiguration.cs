﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Miller.WMS.Domain;

namespace Miller.WMS.Data.Configurations;

public class MaterialConfiguration : IEntityTypeConfiguration<Material>
{
    public void Configure(EntityTypeBuilder<Material> builder)
    {
        builder.HasKey(e => e.Id);

        builder.Property(e => e.Spec)
               .HasMaxLength(100)
               .IsRequired();

        builder.Property(e => e.MaterialForm).HasMaxLength(100);
        builder.Property(e => e.Hardness).HasPrecision(5, 2);
        builder.Property(e => e.MaterialDiameter).HasPrecision(6, 3);
        builder.Property(e => e.MaterialThickness).HasPrecision(6, 3);

        builder.Property(e => e.BaseMetalGroup)
               .HasMaxLength(10)
               .IsRequired();

        builder.Property(e => e.BaseMetalSubGroup)
               .HasMaxLength(10)
               .IsRequired();

        builder.Property(e => e.CharpyRequirement).IsRequired();
        builder.Property(e => e.WeldCarbonEquivalent).HasPrecision(5, 2);

        builder.Property(e => e.Class).HasMaxLength(50).IsRequired();
        builder.Property(e => e.Grade).HasMaxLength(50).IsRequired();
        builder.Property(e => e.Condition).HasMaxLength(100).IsRequired();
        builder.Property(e => e.HeatTreatment).HasMaxLength(100);
        builder.Property(e => e.ForgeMaterial).HasMaxLength(100);
    }
}
