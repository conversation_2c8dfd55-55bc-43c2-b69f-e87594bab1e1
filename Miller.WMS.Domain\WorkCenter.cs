﻿namespace Miller.WMS.Domain;

public class WorkCenter : IEntityWithId, IEntityWithAudit
{
    // IEntityWithId
    public Guid Id { get; set; }

    // IEntityWithAudit
    public DateTime? CreatedAt { get; set; }
    public Guid? CreatedBy { get; set; }
    public DateTime? ModifiedAt { get; set; }
    public Guid? ModifiedBy { get; set; }

    public required string Name { get; set; }
    public required string Description { get; set; }

    public WorkCenterType Type { get; set; }
    public WorkCenterStatus Status { get; set; }

    // Required
    public Guid FacilityId { get; set; }
    public required Facility Facility { get; set; }

    // Optional (hierarchical location)
    public Guid? FacilityAreaLevelOneId { get; set; }
    public FacilityAreaLevelOne? FacilityAreaLevelOne { get; set; }

    public Guid? FacilityAreaLevelTwoId { get; set; }
    public FacilityAreaLevelTwo? FacilityAreaLevelTwo { get; set; }

    public Guid? FacilityAreaLevelThreeId { get; set; }
    public FacilityAreaLevelThree? FacilityAreaLevelThree { get; set; }
}
