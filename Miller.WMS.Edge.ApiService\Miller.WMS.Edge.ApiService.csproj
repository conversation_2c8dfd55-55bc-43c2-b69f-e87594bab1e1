<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Miller.WMS.ServiceDefaults\Miller.WMS.ServiceDefaults.csproj" />
    <ProjectReference Include="..\Miller.WMS.Domain\Miller.WMS.Domain.csproj" />
    <ProjectReference Include="..\Miller.WMS.Data\Miller.WMS.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Facet.Extensions" Version="2.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.8" />
    <PackageReference Include="Carter" Version="9.0.0" />
    <PackageReference Include="Facet" Version="2.0.1" />
    <PackageReference Include="Aspire.Npgsql.EntityFrameworkCore.PostgreSQL" Version="9.4.1" />
  </ItemGroup>

</Project>
