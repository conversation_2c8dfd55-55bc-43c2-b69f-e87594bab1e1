# ElectrodeMadeByManufacturer

**Source File:** [ElectrodeMadeByManufacturer.cs](../ElectrodeMadeByManufacturer.cs)

## Overview
The `ElectrodeMadeByManufacturer` entity represents the many-to-many relationship between electrodes and their manufacturers. This junction entity tracks which manufacturers produce specific electrodes and optionally which specific manufacturing facility produces them.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `ElectrodeId` | `Guid` | Yes | Foreign key to the electrode |
| `ManufacturerId` | `Guid` | Yes | Foreign key to the manufacturer |
| `ManufacturerFacilityId` | `Guid?` | No | Optional foreign key to specific manufacturing facility |
| `CreatedAt` | `DateTime?` | No | Timestamp when the relationship was created |
| `CreatedBy` | `Guid?` | No | User who created this relationship |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this relationship |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Electrode` | `Electrode` | The electrode being manufactured |
| `Manufacturer` | `Manufacturer` | The manufacturer producing the electrode |
| `ManufacturerFacility` | `ManufacturerFacility?` | Optional specific manufacturing facility |

## Relationships

### Many-to-One Relationships
- **ElectrodeMadeByManufacturer → Electrode**: Each relationship belongs to one electrode
- **ElectrodeMadeByManufacturer → Manufacturer**: Each relationship is for one manufacturer
- **ElectrodeMadeByManufacturer → ManufacturerFacility**: Each relationship may specify one facility

## Composite Key

This entity uses a composite primary key consisting of:
- `ElectrodeId`
- `ManufacturerId`

This ensures that an electrode can only be associated once with each manufacturer, but electrodes can be made by multiple manufacturers and manufacturers can produce multiple electrodes.

## Manufacturing Relationships

### Single Manufacturer
Most electrodes are produced by a single manufacturer:
```csharp
var singleManufacturer = new ElectrodeMadeByManufacturer
{
    ElectrodeId = er70s6ElectrodeId,
    ManufacturerId = lincolnElectricId,
    ManufacturerFacilityId = lincolnMainPlantId,
    CreatedAt = DateTime.UtcNow,
    CreatedBy = "procurement_manager"
};
```

### Multiple Manufacturers
Some electrodes may be produced by multiple manufacturers:
```csharp
var multipleManufacturers = new List<ElectrodeMadeByManufacturer>
{
    new ElectrodeMadeByManufacturer
    {
        ElectrodeId = e7018ElectrodeId,
        ManufacturerId = lincolnElectricId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "procurement_manager"
    },
    new ElectrodeMadeByManufacturer
    {
        ElectrodeId = e7018ElectrodeId,
        ManufacturerId = esabId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "procurement_manager"
    }
};
```

## Business Rules

1. **Unique Relationships**: An electrode can only be associated once with each manufacturer
2. **Active Manufacturers**: Only active manufacturers should be associated with electrodes
3. **Facility Validation**: If specified, manufacturer facility must belong to the manufacturer
4. **Quality Approval**: Manufacturers should be approved for electrode production
5. **Traceability**: Maintain complete traceability from electrode to manufacturer

## Manufacturing Information

### Production Details
- **Manufacturing Location**: Specific facility where electrode is produced
- **Production Capacity**: Manufacturing capacity and capabilities
- **Quality Certifications**: Manufacturer quality certifications
- **Lead Times**: Standard production and delivery lead times

### Quality Assurance
- **Certification Requirements**: Required quality certifications
- **Testing Protocols**: Manufacturing quality testing requirements
- **Inspection Procedures**: Incoming inspection procedures
- **Compliance Standards**: Applicable manufacturing standards

## Supply Chain Management

### Supplier Relationships
- **Primary Suppliers**: Main suppliers for each electrode type
- **Secondary Suppliers**: Backup suppliers for continuity
- **Preferred Suppliers**: Suppliers with preferred status
- **Approved Suppliers**: Suppliers approved for production

### Procurement Planning
- **Sourcing Strategy**: Single vs. multiple sourcing strategies
- **Cost Management**: Cost comparison across manufacturers
- **Risk Management**: Supply chain risk mitigation
- **Performance Monitoring**: Supplier performance tracking

## Usage Examples

### Creating Manufacturing Relationships
```csharp
var manufacturingRelationships = new List<ElectrodeMadeByManufacturer>
{
    new ElectrodeMadeByManufacturer
    {
        ElectrodeId = lincolnElectrodeId,
        ManufacturerId = lincolnElectricId,
        ManufacturerFacilityId = lincolnOhioPlantId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "supply_chain_manager"
    },
    new ElectrodeMadeByManufacturer
    {
        ElectrodeId = millerElectrodeId,
        ManufacturerId = millerElectricId,
        ManufacturerFacilityId = millerWisconsinPlantId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "supply_chain_manager"
    }
};
```

### Bulk Manufacturer Assignment
```csharp
// Assign multiple electrodes to the same manufacturer
var electrodeIds = new[] { electrode1Id, electrode2Id, electrode3Id };
var relationships = electrodeIds.Select(electrodeId => 
    new ElectrodeMadeByManufacturer
    {
        ElectrodeId = electrodeId,
        ManufacturerId = primaryManufacturerId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = "batch_processor"
    }).ToList();
```

## Manufacturer Capabilities

### Production Capabilities
- **Electrode Types**: Types of electrodes the manufacturer can produce
- **Size Ranges**: Available electrode diameter ranges
- **Volume Capacity**: Maximum production volumes
- **Specialty Products**: Specialized or custom electrode capabilities

### Quality Capabilities
- **Certifications**: ISO, AWS, ASME certifications
- **Testing Equipment**: Available testing and quality equipment
- **Traceability Systems**: Lot tracking and traceability capabilities
- **Documentation**: Certificate and documentation capabilities

## Facility-Specific Manufacturing

### Manufacturing Facilities
When `ManufacturerFacilityId` is specified:
- **Location-Specific Production**: Track production at specific facilities
- **Facility Capabilities**: Different capabilities at different facilities
- **Quality Variations**: Facility-specific quality characteristics
- **Logistics Optimization**: Optimize shipping from specific facilities

### Multi-Facility Manufacturers
Large manufacturers may have multiple facilities:
- **Facility Specialization**: Different facilities for different electrode types
- **Geographic Distribution**: Facilities in different regions
- **Capacity Distribution**: Load balancing across facilities
- **Quality Consistency**: Ensure consistent quality across facilities

## Supplier Performance

### Performance Metrics
- **Quality Performance**: Defect rates and quality metrics
- **Delivery Performance**: On-time delivery rates
- **Cost Performance**: Cost competitiveness and trends
- **Service Performance**: Technical support and service quality

### Supplier Development
- **Capability Development**: Help suppliers develop new capabilities
- **Quality Improvement**: Continuous quality improvement programs
- **Cost Reduction**: Joint cost reduction initiatives
- **Innovation Partnership**: Collaborative product development

## Related Entities

- [Electrode](Electrode.md) - The electrodes being manufactured
- [Manufacturer](Manufacturer.md) - The manufacturers producing electrodes
- [ManufacturerFacility](ManufacturerFacility.md) - Specific manufacturing facilities
- [ElectrodeClassification](ElectrodeClassification.md) - Classifications of manufactured electrodes

## Database Considerations

- Composite primary key on `ElectrodeId` and `ManufacturerId`
- Index on `ManufacturerId` for manufacturer-based queries
- Index on `ManufacturerFacilityId` for facility-based queries
- Foreign key constraints should be properly configured
- Consider adding fields for production dates, lot numbers, and quality certifications
- Implement audit triggers for relationship change tracking

## Compliance and Traceability

### Regulatory Compliance
- **Manufacturing Standards**: Comply with applicable manufacturing standards
- **Quality Requirements**: Meet quality and certification requirements
- **Documentation**: Maintain required manufacturing documentation
- **Traceability**: Complete traceability from raw materials to finished product

### Supply Chain Traceability
- **Raw Material Traceability**: Track raw materials used in production
- **Process Traceability**: Track manufacturing processes and parameters
- **Quality Traceability**: Track quality testing and certifications
- **Distribution Traceability**: Track distribution and delivery

## Risk Management

### Supply Chain Risks
- **Single Source Risk**: Risks of single-source suppliers
- **Geographic Risk**: Risks from geographic concentration
- **Quality Risk**: Risks from quality variations
- **Capacity Risk**: Risks from capacity constraints

### Risk Mitigation
- **Supplier Diversification**: Multiple suppliers for critical electrodes
- **Geographic Diversification**: Suppliers in different regions
- **Quality Assurance**: Robust quality assurance programs
- **Contingency Planning**: Plans for supply disruptions
