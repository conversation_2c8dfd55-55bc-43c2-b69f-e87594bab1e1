# EquipmentHasElectrodeClassification

**Source File:** [EquipmentHasElectrodeClassification.cs](../EquipmentHasElectrodeClassification.cs)

## Overview
The `EquipmentHasElectrodeClassification` entity represents the many-to-many relationship between equipment and electrode classifications. This junction entity defines which electrode classifications are compatible with specific welding equipment.

## Properties

| Property | Type | Required | Description |
|----------|------|----------|-------------|
| `EquipmentId` | `Guid` | Yes | Foreign key to the equipment |
| `ElectrodeClassificationId` | `Guid` | Yes | Foreign key to the electrode classification |
| `CreatedAt` | `DateTime?` | No | Timestamp when the relationship was created |
| `CreatedBy` | `Guid?` | No | User who created this relationship |
| `ModifiedAt` | `DateTime?` | No | Timestamp of last modification |
| `ModifiedBy` | `Guid?` | No | User who last modified this relationship |

## Navigation Properties

| Property | Type | Description |
|----------|------|-------------|
| `Equipment` | `Equipment` | The equipment that supports the classification |
| `ElectrodeClassification` | `ElectrodeClassification` | The electrode classification supported by the equipment |

## Relationships

### Many-to-One Relationships
- **EquipmentHasElectrodeClassification → Equipment**: Each relationship belongs to one piece of equipment
- **EquipmentHasElectrodeClassification → ElectrodeClassification**: Each relationship is for one classification

## Composite Key

This entity uses a composite primary key consisting of:
- `EquipmentId`
- `ElectrodeClassificationId`

This ensures that each piece of equipment can only be associated once with each electrode classification.

## Equipment Compatibility

### Power Supply Compatibility
Different power supplies support different electrode classifications:

#### SMAW Power Supplies
```csharp
var smawPowerSupplyCompatibility = new List<EquipmentHasElectrodeClassification>
{
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = smawPowerSupplyId,
        ElectrodeClassificationId = e6010ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_engineer_id
    },
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = smawPowerSupplyId,
        ElectrodeClassificationId = e7018ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_engineer_id
    }
};
```

#### GTAW Power Supplies
```csharp
var gtawPowerSupplyCompatibility = new List<EquipmentHasElectrodeClassification>
{
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = gtawPowerSupplyId,
        ElectrodeClassificationId = er70s6ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_engineer_id
    },
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = gtawPowerSupplyId,
        ElectrodeClassificationId = er308lClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_engineer_id
    }
};
```

## Business Rules

1. **Equipment Type Compatibility**: Electrode classifications must be compatible with equipment type
2. **Process Alignment**: Equipment and electrode classifications must support the same welding processes
3. **Current Type Support**: Equipment must support the current types required by the classification
4. **Parameter Ranges**: Equipment must support the parameter ranges required by the classification
5. **Safety Compliance**: Combinations must meet safety requirements

## Equipment Types and Compatibility

### Power Supply Equipment
- **SMAW Power Supplies**: Compatible with stick electrode classifications
- **GTAW Power Supplies**: Compatible with TIG filler wire classifications
- **GMAW Power Supplies**: Compatible with MIG wire classifications
- **Multi-Process Power Supplies**: Compatible with multiple classification types

### Torch Equipment
- **SMAW Electrode Holders**: Compatible with stick electrode classifications
- **GTAW Torches**: Compatible with TIG filler wire classifications
- **GMAW Guns**: Compatible with MIG wire classifications
- **Plasma Torches**: Compatible with plasma electrode classifications

### Feeder Equipment
- **Wire Feeders**: Compatible with wire electrode classifications
- **Electrode Ovens**: Compatible with stick electrode classifications
- **Flux Feeders**: Compatible with flux-cored electrode classifications

## Compatibility Criteria

### Current Requirements
- **DC Equipment**: Compatible with DCEN and DCEP classifications
- **AC Equipment**: Compatible with AC classifications
- **AC/DC Equipment**: Compatible with all current type classifications

### Amperage Ranges
- **Low Amperage Equipment**: Compatible with thin material classifications
- **Medium Amperage Equipment**: Compatible with general purpose classifications
- **High Amperage Equipment**: Compatible with heavy section classifications

### Process Capabilities
- **Single Process Equipment**: Compatible with specific process classifications
- **Multi-Process Equipment**: Compatible with multiple process classifications
- **Specialized Equipment**: Compatible with specialized classifications

## Usage Examples

### Creating Equipment Compatibility Relationships
```csharp
var equipmentCompatibility = new List<EquipmentHasElectrodeClassification>
{
    // Multi-process power supply compatibility
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = multiProcessPowerSupplyId,
        ElectrodeClassificationId = e7018ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_specialist_id
    },
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = multiProcessPowerSupplyId,
        ElectrodeClassificationId = er70s6ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_specialist_id
    },
    
    // Specialized TIG power supply
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = advancedTigPowerSupplyId,
        ElectrodeClassificationId = er308lClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_specialist_id
    },
    new EquipmentHasElectrodeClassification
    {
        EquipmentId = advancedTigPowerSupplyId,
        ElectrodeClassificationId = er4043ClassificationId,
        CreatedAt = DateTime.UtcNow,
        CreatedBy = equipment_specialist_id
    }
};
```

## Equipment Selection

This relationship enables equipment selection based on electrode requirements:

### Electrode-Driven Selection
- **Classification Requirements**: Select equipment that supports required classifications
- **Process Requirements**: Ensure equipment supports required welding processes
- **Current Requirements**: Verify equipment supports required current types
- **Parameter Requirements**: Confirm equipment supports required parameters

### Equipment-Driven Selection
- **Available Equipment**: Determine which classifications are supported
- **Capability Assessment**: Assess equipment welding capabilities
- **Utilization Planning**: Plan equipment utilization based on capabilities
- **Upgrade Planning**: Plan equipment upgrades for new capabilities

## Quality Assurance

### Compatibility Verification
- **Technical Verification**: Verify technical compatibility
- **Performance Testing**: Test equipment performance with classifications
- **Quality Testing**: Verify weld quality with equipment/classification combinations
- **Certification**: Maintain certification records for combinations

### Documentation Requirements
- **Compatibility Matrix**: Maintain equipment/classification compatibility matrix
- **Test Records**: Document compatibility testing results
- **Approval Records**: Maintain approval records for combinations
- **Change Control**: Control changes to compatibility relationships

## Maintenance and Support

### Equipment Maintenance
- **Classification-Specific Maintenance**: Maintenance requirements for specific classifications
- **Performance Monitoring**: Monitor equipment performance with different classifications
- **Calibration**: Calibration requirements for classification support
- **Upgrades**: Equipment upgrades to support new classifications

### Technical Support
- **Application Support**: Support for equipment/classification applications
- **Troubleshooting**: Troubleshoot issues with specific combinations
- **Training**: Training on equipment/classification combinations
- **Documentation**: Maintain technical documentation

## Performance Optimization

### Optimization Strategies
- **Parameter Optimization**: Optimize parameters for equipment/classification combinations
- **Process Optimization**: Optimize processes for specific combinations
- **Quality Optimization**: Optimize quality for equipment/classification pairs
- **Productivity Optimization**: Maximize productivity with optimal combinations

### Performance Monitoring
- **Quality Metrics**: Monitor quality performance by combination
- **Productivity Metrics**: Track productivity by equipment/classification
- **Efficiency Metrics**: Measure efficiency of combinations
- **Cost Metrics**: Track costs associated with combinations

## Related Entities

- [Equipment](Equipment.md) - The equipment supporting classifications
- [ElectrodeClassification](ElectrodeClassification.md) - The classifications supported by equipment
- [WorkCenter](WorkCenter.md) - Work centers where equipment is located
- [Electrode](Electrode.md) - Electrodes that meet the classifications

## Database Considerations

- Composite primary key on `EquipmentId` and `ElectrodeClassificationId`
- Index on `EquipmentId` for equipment-based queries
- Index on `ElectrodeClassificationId` for classification-based queries
- Foreign key constraints should be properly configured
- Consider adding fields for compatibility parameters and limitations
- Implement audit triggers for relationship change tracking

## Integration with Work Planning

### Job Planning
- **Equipment Requirements**: Determine equipment requirements based on electrode needs
- **Capability Matching**: Match job requirements to equipment capabilities
- **Resource Allocation**: Allocate equipment based on classification requirements
- **Scheduling**: Schedule work based on equipment/classification availability

### Quality Planning
- **Quality Requirements**: Plan quality based on equipment/classification combinations
- **Testing Requirements**: Plan testing based on combinations
- **Inspection Requirements**: Plan inspection based on equipment capabilities
- **Documentation**: Document quality requirements for combinations

## Future Enhancements

Consider adding additional properties for comprehensive compatibility management:

### Compatibility Properties
- **Parameter Ranges**: Supported parameter ranges for combinations
- **Performance Ratings**: Performance ratings for combinations
- **Quality Ratings**: Quality ratings for combinations
- **Efficiency Ratings**: Efficiency ratings for combinations

### Certification Properties
- **Certification Status**: Certification status for combinations
- **Test Results**: Test results for compatibility verification
- **Approval Status**: Approval status for combinations
- **Expiration Dates**: Certification expiration dates
